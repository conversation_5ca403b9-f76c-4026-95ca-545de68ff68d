import rasterio
from rasterio.mask import mask
import geopandas as gpd
from shapely.geometry import box
import numpy as np

# 打开第一个文件
with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\tif\hazard_level.tif') as src1:
    data1 = src1.read(1)
    meta1 = src1.meta
    # 获取经纬度范围
    bounds1 = src1.bounds
    min_x1, min_y1, max_x1, max_y1 = bounds1.left, bounds1.bottom, bounds1.right, bounds1.top
    # 四舍五入坐标
    min_x1 = np.round(min_x1, 6)
    min_y1 = np.round(min_y1, 6)
    max_x1 = np.round(max_x1, 6)
    max_y1 = np.round(max_y1, 6)
    print(meta1)

    # 创建裁剪区域的几何对象
    geom = box(min_x1, min_y1, max_x1, max_y1)
    geo = gpd.GeoDataFrame({'geometry': [geom]}, crs=src1.crs)










# 打开第二个文件
with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\人口.tif') as src2:
    data2 = src2.read(1)
    meta2 = src2.meta
    # 获取经纬度范围
    bounds2 = src2.bounds
    min_x2, min_y2, max_x2, max_y2 = bounds2.left, bounds2.bottom, bounds2.right, bounds2.top
    print(meta2)

    # 裁剪第二个文件
    out_image, out_transform = mask(src2, shapes=geo.geometry, crop=True)
    out_meta = src2.meta.copy()

    # 更新元数据
    out_meta.update({"driver": "GTiff",
                     "height": out_image.shape[1],
                     "width": out_image.shape[2],
                     "transform": out_transform})

    # 保存裁剪后的文件
    with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\人口_裁剪.tif', "w", **out_meta) as dest:
        dest.write(out_image)