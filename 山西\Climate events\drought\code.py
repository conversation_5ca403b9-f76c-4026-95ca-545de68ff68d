#In[0]: 数据加载
import pandas as pd

file_path = r'D:\python\山西\Climate events\drought\data\T_DPW_CP_RINDEX_202505271513.xlsx'
data = pd.read_excel(file_path)
data = data.fillna(0)
station_ids = data['V01000'].unique()  
data['V04000'] = pd.to_datetime(data['V04000'], format='%Y%m%d')
data = data[data['V04000'] >= '2022-01-01']


# %%
# 判断无旱、轻旱、中旱、重旱、特旱(返回整数分类)
def classify_drought_intensity(mci_value):
    if -0.5 < mci_value:
        return 0
    elif -1.0 < mci_value <= -0.5:
        return 1
    elif -1.5 < mci_value <= -1.0:
        return 2
    elif -2.0 < mci_value <= -1.5:
        return 3
    else:
        return 4
    
# 应用函数到 MCI 列
data['MCI_Class'] = data['MCI'].apply(classify_drought_intensity).astype(int)


# --------------------MCI指数--------------------
#%%单站逐时段MCI指数统计表
df = pd.DataFrame(columns=['站号', '时段', 'MCI指数', '干旱等级'])
# 干旱等级对应字典
drought_levels = {0: '无旱', 1: '轻旱', 2: '中旱', 3: '重旱', 4: '特旱'}

df['站号'] = data['V01000']
df['时段'] = data['V04000']
df['MCI指数'] = data['MCI']
df['干旱等级'] = data['MCI_Class'].map(drought_levels)
df.to_excel(r'D:\python\山西\Climate events\drought\data\山西省单站逐时段MCI指数统计表.xlsx', index=False)

#%%逐日单站干旱日数统计表
all_station_data = []

for station_id in station_ids:
    station_data = data[data['V01000'] == station_id]
    
    temp_df = pd.DataFrame({
        '站号': [station_id],
        '时段累计无旱日数': [station_data[station_data['MCI_Class'] == 0].shape[0]],
        '时段累计轻旱日数': [station_data[station_data['MCI_Class'] == 1].shape[0]],
        '时段累计中旱日数': [station_data[station_data['MCI_Class'] == 2].shape[0]],
        '时段累计重旱日数': [station_data[station_data['MCI_Class'] == 3].shape[0]],
        '时段累计特旱日数': [station_data[station_data['MCI_Class'] == 4].shape[0]],
        '时段累计中旱及以上日数': [station_data[station_data['MCI_Class'] >= 2].shape[0]],
        '时段累计重旱及以上日数': [station_data[station_data['MCI_Class'] >= 3].shape[0]]
    }, index=[0])
    all_station_data.append(temp_df)
df1 = pd.concat(all_station_data, ignore_index=True)
df1.to_excel(r'D:\python\山西\Climate events\drought\data\山西省逐日单站干旱日数统计表.xlsx', index=False)

#%% 逐日干旱站数统计表

all_station_data = []

for time in data['V04000'].unique():
    time_data = data[data['V04000'] == time]

    temp_df = pd.DataFrame({
       '区域':['四川省'],
        '时段': [time],  
        '无旱站数': [time_data[time_data['MCI_Class'] == 0]['V01000'].nunique()],
        '轻旱站数': [time_data[time_data['MCI_Class'] == 1]['V01000'].nunique()],
        '中旱站数': [time_data[time_data['MCI_Class'] == 2]['V01000'].nunique()],
        '重旱站数': [time_data[time_data['MCI_Class'] == 3]['V01000'].nunique()],
        '特旱站数': [time_data[time_data['MCI_Class'] == 4]['V01000'].nunique()],
        '中旱及以上站数': [time_data[time_data['MCI_Class'] >= 2]['V01000'].nunique()],
        '重旱及以上站数': [time_data[time_data['MCI_Class'] >= 3]['V01000'].nunique()]
    })
    all_station_data.append(temp_df)

# 合并所有站点数据
df2 = pd.concat(all_station_data, ignore_index=True)
df2.to_excel(r'D:\python\山西\Climate events\drought\data\山西省逐日干旱站数统计表.xlsx', index=False)



# --------------------单站过程--------------------
'''
当某站连续15天及以上出现轻旱及以上等级干旱,且
至少有一天干旱等级达到中旱及以上，则确定为发生一次干旱过程。干旱过程时
段内第一次出现轻旱的日期,为干旱过程开始日。干旱过程发生后,当连续5天
干旱等级为无旱等级，则干旱过程结束，干旱过程结束前最后一天干旱
等级为轻旱或以上的日期为干旱过程结束日。某站干旱过程开始日到结束日（含
结束日）的总天数为某站干旱过程日数。
'''
# %% 

# 定义函数来查找初始段
def find_initial_segments(station_data):
    segments = []
    current_segment = []
    current_has_severe = False
    consecutive_non_drought = 0
    valid_length = 0  # 有效干旱长度计数器
    
    for idx, row in station_data.iterrows():
        mci_class = row['MCI_Class']
        date = row['V04000']
        
        if mci_class >= 1:
            current_segment.append(date)
            if mci_class >= 2:
                current_has_severe = True
            valid_length += 1  # 只有干旱日才累计有效长度
            
            # 当有效长度达到15天后，允许最多4天无旱间断
            if valid_length >= 15:
                consecutive_non_drought = 0  # 重置无旱计数器
                
        else:
            # 仅当有效长度≥15天后才允许无旱间断
            if valid_length >= 15:
                consecutive_non_drought += 1
                
                # 当累计无旱超过4天时结束当前段
                if consecutive_non_drought > 4:
                    if current_has_severe:
                        segments.append({
                            'start': current_segment[0],
                            'end': current_segment[-1]
                        })
                    # 重置计数器但保留最后4天无旱日
                    current_segment = current_segment[-4:] if len(current_segment) >=4 else []
                    valid_length = 0
                    current_has_severe = False
                    consecutive_non_drought = 0
            else:
                # 未达到15天时遇到无旱直接重置
                current_segment = []
                valid_length = 0
                current_has_severe = False

    # 处理最后一个有效段
    if valid_length >= 15 and current_has_severe:
        segments.append({
            'start': current_segment[0],
            'end': current_segment[-1]
        })
    
    return segments

# 计算累计干旱强度
def calculate_cumulative_drought_intensity(mci_values):
    n = len(mci_values)
    a = 0.5
    if n == 0:
        return 0
    return (n ** (a - 1)) * abs(mci_values.sum())

# 计算过程强度
def calculate_drought_process_intensity(mci_series, date_series):
    max_zs = 0
    max_start = None
    max_end = None
    m = len(mci_series)
    
    for i in range(m):
        for j in range(i + 1, m + 1):
            subset = mci_series.iloc[i:j]
            current_zs = calculate_cumulative_drought_intensity(subset)
            
            # 更新最大值及对应时段
            if current_zs > max_zs or (current_zs == max_zs and (max_end - max_start) < (j - i)):
                max_zs = current_zs
                max_start = date_series.iloc[i]
                max_end = date_series.iloc[j-1]
    
    return max_zs, max_start, max_end

# 处理每个站点，统计干旱过程
process_list = []

for station_id in station_ids:
    # 获取站点数据并按时间排序
    station_data = data[data['V01000'] == station_id].sort_values('V04000')
    if len(station_data) == 0:
        continue
    
    # 查找所有符合条件的初始段
    segments = find_initial_segments(station_data)
    
    for seg in segments:
        start_date = seg['start']
        # 获取从start_date开始的数据
        mask = (station_data['V04000'] >= start_date)
        process_data = station_data.loc[mask]
        
        end_date = None
        consecutive_non_drought = 0
        last_drought_day = seg['end']  # 初始段的最后一天
        
        # 遍历从start_date开始的数据，寻找连续5天无旱
        for idx, row in process_data.iterrows():
            current_date = row['V04000']
            mci_class = row['MCI_Class']
            
            if mci_class >= 1:
                consecutive_non_drought = 0
                last_drought_day = current_date
            else:
                consecutive_non_drought += 1
                if consecutive_non_drought == 5:
                    end_date = last_drought_day
                    break  # 找到结束日期，退出循环
        
        # 如果找到结束日期
        if end_date is not None:
            # 计算持续时间
            duration = (end_date - start_date).days + 1
            # 筛选该时间段内的数据
            mask_duration = (process_data['V04000'] >= start_date) & (process_data['V04000'] <= end_date)
            process_days = process_data.loc[mask_duration]
            
            # 计算累计干旱强度和过程强度
            mci_values = process_days.loc[process_days['MCI_Class'] >= 1, 'MCI']
            date_series = process_days.loc[process_days['MCI_Class'] >= 1, 'V04000']
            cumulative_intensity = calculate_cumulative_drought_intensity(mci_values)
            process_intensity, max_zs_start, max_zs_end = calculate_drought_process_intensity(mci_values, date_series)
            
            # 添加过程信息
            process_list.append({
                '站号': station_id,
                '过程开始日期': start_date.strftime('%Y-%m-%d'),
                '过程结束日期': end_date.strftime('%Y-%m-%d'),
                '过程持续时间': duration,
                '累计干旱强度': cumulative_intensity,
                '干旱过程强度': process_intensity,
                '最大累计强度开始时间': max_zs_start.strftime('%Y-%m-%d') if max_zs_start else None,
                '最大累计强度结束时间': max_zs_end.strftime('%Y-%m-%d') if max_zs_end else None,
            })

# 转换为DataFrame并保存
if process_list:
    df_process = pd.DataFrame(process_list)
    df_process.to_excel(r'D:\python\山西\Climate events\drought\data\山西省单站干旱过程统计表.xlsx', index=False)
else:
    print("没有找到符合条件的干旱过程。")


# %%
