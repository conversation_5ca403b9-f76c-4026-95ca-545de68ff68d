// 省份筛选功能
document.addEventListener('DOMContentLoaded', function() {
    // 加载省份数据
    loadProvinces();
    
    // 更新统计信息
    updateStationStats();
    
    // 搜索功能
    const searchInput = document.getElementById('stationSearch');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            filterStations();
        });
    }
    
    // 省份筛选功能
    const provinceFilter = document.getElementById('provinceFilter');
    if (provinceFilter) {
        provinceFilter.addEventListener('change', function() {
            filterStations();
        });
    }
    
    // 重置筛选按钮
    const resetButton = document.getElementById('resetFilters');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            if (document.getElementById('stationSearch')) {
                document.getElementById('stationSearch').value = '';
            }
            if (document.getElementById('provinceFilter')) {
                document.getElementById('provinceFilter').value = '';
            }
            filterStations();
        });
    }
});

// 加载省份数据
function loadProvinces() {
    fetch('/api/provinces')
        .then(response => response.json())
        .then(provinces => {
            const provinceFilter = document.getElementById('provinceFilter');
            if (!provinceFilter) return;
            
            // 清空现有选项（保留"所有省份"选项）
            while (provinceFilter.options.length > 1) {
                provinceFilter.remove(1);
            }
            
            // 添加省份选项
            provinces.forEach(province => {
                if (province) { // 确保省份名称不为空
                    const option = document.createElement('option');
                    option.value = province;
                    option.textContent = province;
                    provinceFilter.appendChild(option);
                }
            });
        })
        .catch(error => console.error('加载省份数据失败:', error));
}

// 筛选站点
function filterStations() {
    const searchInput = document.getElementById('stationSearch');
    const provinceFilter = document.getElementById('provinceFilter');
    const tableRows = document.querySelectorAll('#stationTable tbody tr');
    
    if (!tableRows.length) return;
    
    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    const selectedProvince = provinceFilter ? provinceFilter.value : '';
    
    let visibleCount = 0;
    const totalCount = tableRows.length;
    
    tableRows.forEach(row => {
        // 跳过"暂无站点数据"行
        if (row.cells.length === 1 && row.cells[0].getAttribute('colspan')) {
            return;
        }
        
        const text = row.textContent.toLowerCase();
        const province = row.cells.length > 2 ? row.cells[2].textContent.trim() : ''; // 省份在第3列（索引为2）
        
        const matchesSearch = text.includes(searchTerm);
        const matchesProvince = selectedProvince === '' || province === selectedProvince;
        
        if (matchesSearch && matchesProvince) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // 更新统计信息
    updateStationStats(visibleCount, totalCount);
}

// 更新站点统计信息
function updateStationStats(visibleCount, totalCount) {
    const statsElement = document.getElementById('stationStats');
    if (!statsElement) return;
    
    const tableRows = document.querySelectorAll('#stationTable tbody tr');
    
    if (!visibleCount) {
        visibleCount = 0;
        tableRows.forEach(row => {
            if (row.style.display !== 'none' && row.cells.length > 1) {
                visibleCount++;
            }
        });
    }
    
    if (!totalCount) {
        totalCount = 0;
        tableRows.forEach(row => {
            if (row.cells.length > 1) { // 跳过"暂无站点数据"行
                totalCount++;
            }
        });
    }
    
    const searchInput = document.getElementById('stationSearch');
    const provinceFilter = document.getElementById('provinceFilter');
    
    const searchTerm = searchInput ? searchInput.value : '';
    const selectedProvince = provinceFilter ? provinceFilter.value : '';
    
    let filterInfo = '';
    
    if (searchTerm || selectedProvince) {
        filterInfo = ' (';
        if (selectedProvince) {
            filterInfo += `省份: ${selectedProvince}`;
            if (searchTerm) filterInfo += ', ';
        }
        if (searchTerm) {
            filterInfo += `搜索: ${searchTerm}`;
        }
        filterInfo += ')';
    }
    
    statsElement.innerHTML = `<i class="bi bi-info-circle"></i> 共 ${totalCount} 个站点，当前显示 ${visibleCount} 个${filterInfo}`;
}
