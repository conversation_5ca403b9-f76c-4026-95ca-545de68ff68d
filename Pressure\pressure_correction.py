
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文显示
plt.rcParams["font.family"] = ["Microsoft YaHei"]
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def correct_pressure(height_diff, reference_pressure, temperature):
    """计算气压订正"""
    # 物理常数
    R = 287.05  # 干空气气体常数，J/(kg·K)
    g = 9.81    # 重力加速度，m/s²
    
    # 将温度转换为开尔文
    T = temperature + 273.15
    
    # 计算订正后的气压
    exponent = (g * height_diff) / (R * T)
    corrected_pressure = reference_pressure / np.exp(exponent)
    
    return corrected_pressure

def correct_station_data(data, station_id, correction_year=2002):
    """订正单个观测站的数据"""
    # 筛选观测站数据
    station_data = data[data['区站号(字符)'] == station_id].copy()
    
    if len(station_data) == 0:
        print(f"错误：未找到观测站 {station_id} 的数据")
        return None
    
    # 计算高度差
    height_before = station_data[station_data['年'] < correction_year]['测站高度'].mean()
    height_after = station_data[station_data['年'] >= correction_year]['测站高度'].mean()
    height_diff = height_after - height_before
    
    # 计算参考气压
    reference_pressure = station_data[station_data['年'] < correction_year]['平均气压'].mean()
    
    temperature = station_data['平均气温'].mean()

    corrected_pressure = correct_pressure(height_diff, reference_pressure, temperature)

    # 计算气压差
    pressure_diff = reference_pressure - corrected_pressure

    # 订正气压
    for index, row in station_data.iterrows():
        station_data.loc[index, '订正后的平均气压'] = row['平均气压'] - pressure_diff
    
    # 2002年及之后的数据保持不变
    station_data.loc[station_data['年'] >= correction_year, '订正后的平均气压'] = \
        station_data.loc[station_data['年'] >= correction_year, '平均气压']
    
    return station_data

def visualize_correction(station_data, station_name, output_file=None):
    """可视化气压订正前后的对比"""
    plt.figure(figsize=(12, 6))
    
    # 绘制原始气压
    plt.plot(station_data['年'], station_data['平均气压'], 'b-', label='原始气压')
    
    # 绘制订正后气压
    plt.plot(station_data['年'], station_data['订正后的平均气压'], 'r--', label='订正后气压')
    
    plt.axvline(x=2002, color='g', linestyle='--', label='2002年分割线')
    
    plt.title(f'{station_name}气压订正前后对比')
    plt.xlabel('年份')
    plt.ylabel('气压 (hPa)')
    plt.legend()
    plt.grid(True)
    
    if output_file:
        plt.savefig(output_file)
        print(f"图表已保存至: {output_file}")
    
    plt.show()

def main():
    file_path = r'D:\python\Pressure\data\1951-2022年156站年数据.xlsx'
    data = pd.read_excel(file_path)
    # 获取所有观测站ID
    stations = data['区站号(字符)'].unique()
    print(f"数据中包含 {len(stations)} 个观测站")
    
    # 选择要处理的观测站
    print("\n观测站列表:")
    for i, station in enumerate(stations):
        station_name = data[data['区站号(字符)'] == station]['站名'].iloc[0]
        print(f"{i+1}. {station} ({station_name})")
    
    try:
        selection = input("\n请输入要处理的观测站编号 (例如: 1,3,5)或按Enter处理所有观测站: ")
        
        if selection.strip() == '':
            stations_to_process = stations
        else:
            indices = [int(x.strip())-1 for x in selection.split(',')]
            stations_to_process = [stations[i] for i in indices if 0 <= i < len(stations)]
    except:
        print("输入无效，将处理所有观测站")
        stations_to_process = stations
    
    # 处理每个观测站
    for station_id in stations_to_process:
        station_name = data[data['区站号(字符)'] == station_id]['站名'].iloc[0]
        print(f"\n处理观测站: {station_id} ({station_name})")
        corrected_data = correct_station_data(data, station_id)
        
        if corrected_data is not None:
            print("\n订正结果示例:")
            print(corrected_data[['年', '平均气压', '订正后的平均气压']].head())
            output_file = f"station_{station_id}_corrected.csv"
            corrected_data.to_csv(output_file, index=False)
            print(f"订正后的数据已保存至: {output_file}")
            plot_file = rf"D:\python\Pressure\result\station_{station_id}_correction.png"
            visualize_correction(corrected_data, f"{station_id}站({station_name})", plot_file)

if __name__ == "__main__":
    main()