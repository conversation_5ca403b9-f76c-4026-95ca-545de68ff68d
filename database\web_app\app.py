import os
import sys
import json
import time
from flask import Flask, render_template, request, jsonify, send_file, g
from werkzeug.utils import secure_filename
import threading

# 将父目录添加到系统路径，以便导入weather_database_fixed模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from weather_database_fixed import WeatherDatabase
try:
    from .config import Config
except ImportError:
    from config import Config

app = Flask(__name__)
app.config.from_object(Config)
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 使用线程本地存储，为每个线程提供独立的数据库连接
# 这解决了SQLite的线程安全问题
thread_local = threading.local()

# 获取当前线程的数据库连接
def get_db():
    if not hasattr(thread_local, 'db'):
        thread_local.db = WeatherDatabase()
    return thread_local.db

# 存储导入进度的全局变量
import_progress = {}

def clean_nan_values(obj):
    """递归清理对象中的NaN值，将其替换为None或0"""
    import math

    if isinstance(obj, dict):
        return {k: clean_nan_values(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [clean_nan_values(item) for item in obj]
    elif isinstance(obj, float):
        if math.isnan(obj):
            return 0.0
        return obj
    else:
        return obj

@app.route('/')
def system_home():
    """系统主页 - 小恐龙的超算单元"""
    return render_template('system_home.html')

@app.route('/datacenter')
def datacenter_home():
    """数据中心主页 - 原来的首页"""
    return render_template('index.html', amap_api_key=app.config['AMAP_API_KEY'])

@app.route('/compute')
def compute_home():
    """计算中心主页"""
    return render_template('compute_home.html')

@app.route('/compute/huaxi-autumn-rain')
def huaxi_autumn_rain():
    """华西秋雨分析计算"""
    return render_template('huaxi_autumn_rain.html', amap_api_key=app.config['AMAP_API_KEY'])

@app.route('/api/compute/huaxi-autumn-rain/available-years')
def api_huaxi_available_years():
    """获取华西秋雨分析可用的年份列表"""
    try:
        db_conn = get_db()
        cursor = db_conn.cursor

        # 查询数据库中有数据的年份
        cursor.execute('''
            SELECT DISTINCT substr(timestamp, 1, 4) as year
            FROM observations
            WHERE timestamp IS NOT NULL
            AND substr(timestamp, 6, 2) IN ('08', '09', '10', '11')
            ORDER BY year DESC
        ''')

        years = [int(row[0]) for row in cursor.fetchall()]

        return jsonify({
            'success': True,
            'years': years
        })

    except Exception as e:
        print(f"获取可用年份失败: {e}")
        return jsonify({
            'success': False,
            'years': [],
            'message': str(e)
        }), 500

@app.route('/api/compute/huaxi-autumn-rain', methods=['POST'])
def api_huaxi_autumn_rain():
    """华西秋雨分析API"""
    try:
        # 导入华西秋雨分析器
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from huaxi_autumn_rain_analyzer import HuaxiAutumnRainAnalyzer

        # 获取请求参数
        data = request.get_json()
        start_year = data.get('start_year', 2023)
        end_year = data.get('end_year', 2024)
        selected_stations = data.get('stations', [])
        analysis_options = data.get('options', {})

        # 创建分析器实例
        analyzer = HuaxiAutumnRainAnalyzer()

        # 分析结果列表
        results = []

        # 分析每一年
        for year in range(start_year, end_year + 1):
            year_result = analyzer.analyze_year(year)
            # 清理结果中的NaN值
            year_result = clean_nan_values(year_result)
            results.append(year_result)

        # 关闭分析器
        analyzer.close()

        return jsonify({
            'success': True,
            'message': '华西秋雨分析完成',
            'results': results,
            'analysis_options': analysis_options
        })

    except Exception as e:
        print(f"华西秋雨分析API错误: {e}")
        return jsonify({
            'success': False,
            'message': f'分析失败: {str(e)}',
            'results': []
        }), 500

@app.route('/datacenter/station_list')
def station_list():
    stations = get_db().get_all_stations()
    return render_template('station_list.html', stations=stations, amap_api_key=app.config['AMAP_API_KEY'])

@app.route('/api/stations')
def api_stations():
    stations = get_db().get_all_stations()
    return jsonify(stations)

@app.route('/api/provinces')
def api_provinces():
    provinces = get_db().get_all_provinces()
    return jsonify(provinces)

@app.route('/api/observations/<station_id>')
def api_observations(station_id):
    observations = get_db().get_observations_by_station_id(station_id)
    return jsonify(observations)

@app.route('/api/observations/<station_id>/statistics')
def api_observation_statistics(station_id):
    """获取站点观测数据统计信息"""
    db_conn = get_db()

    # 获取基本统计
    stats = db_conn.get_observation_statistics(station_id)

    return jsonify(stats)

@app.route('/api/observations/<station_id>/detailed')
def api_observation_detailed(station_id):
    """获取站点详细观测数据（用于弹窗显示）"""
    db_conn = get_db()

    # 获取参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 50, type=int)
    search = request.args.get('search', '', type=str)

    # 计算偏移量
    offset = (page - 1) * limit

    # 获取详细观测数据
    observations = db_conn.get_observations_paginated(station_id, limit, offset, search)
    total_count = db_conn.get_observations_count(station_id, search)

    return jsonify({
        'observations': observations,
        'total_count': total_count,
        'page': page,
        'limit': limit,
        'total_pages': (total_count + limit - 1) // limit
    })

@app.route('/api/observations/<int:observation_id>', methods=['DELETE'])
def delete_observation(observation_id):
    """删除指定的观测数据记录"""
    try:
        db_conn = get_db()

        # 首先检查观测记录是否存在
        db_conn.cursor.execute('SELECT station_id FROM observations WHERE id = ?', (observation_id,))
        result = db_conn.cursor.fetchone()

        if not result:
            return jsonify({'success': False, 'message': '观测记录不存在'})

        station_id = result[0]

        # 删除观测记录
        db_conn.cursor.execute('DELETE FROM observations WHERE id = ?', (observation_id,))
        db_conn.conn.commit()

        # 添加调试信息
        print(f"单个删除操作: 观测记录ID {observation_id}, 影响行数: {db_conn.cursor.rowcount}")

        if db_conn.cursor.rowcount > 0:
            return jsonify({
                'success': True,
                'message': '观测记录删除成功',
                'station_id': station_id
            })
        else:
            print(f"删除失败: 观测记录ID {observation_id} 可能不存在或已被删除")
            return jsonify({'success': False, 'message': '删除失败，记录可能已被删除'})

    except Exception as e:
        print(f"删除观测记录时出错: {e}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@app.route('/api/observations/batch_delete', methods=['POST'])
def batch_delete_observations():
    """批量删除观测数据记录"""
    try:
        data = request.get_json()
        observation_ids = data.get('observation_ids', [])

        if not observation_ids:
            return jsonify({'success': False, 'message': '没有选择要删除的记录'})

        db_conn = get_db()

        # 构建批量删除的SQL
        placeholders = ','.join(['?' for _ in observation_ids])
        query = f'DELETE FROM observations WHERE id IN ({placeholders})'

        db_conn.cursor.execute(query, observation_ids)
        db_conn.conn.commit()

        deleted_count = db_conn.cursor.rowcount

        # 添加调试信息
        print(f"批量删除操作: 请求删除 {len(observation_ids)} 条记录, 实际删除 {deleted_count} 条记录")
        print(f"删除的ID列表: {observation_ids}")

        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 条观测记录',
            'deleted_count': deleted_count
        })

    except Exception as e:
        print(f"批量删除观测记录时出错: {e}")
        return jsonify({'success': False, 'message': f'批量删除失败: {str(e)}'})

@app.route('/upload', methods=['POST'])
def upload_file():
    file_type = request.form.get('file_type')
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    if file:
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # 创建唯一的任务ID
        task_id = f"{file_type}_{int(time.time())}"
        import_progress[task_id] = {
            'progress': 0,
            'message': '准备导入...',
            'status': 'running'
        }
        
        # 启动导入线程
        thread = threading.Thread(target=process_file, args=(file_path, file_type, task_id))
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True, 
            'message': '文件上传成功，正在处理...', 
            'task_id': task_id
        })

def process_file(file_path, file_type, task_id):
    # 使用批量导入优化性能
    from weather_database_batch_optimized import WeatherDatabaseBatchOptimized
    from weather_database_fixed import WeatherDatabase
    import os
    import sys
    import traceback

    # 设置控制台输出编码为UTF-8
    if sys.stdout.encoding != 'utf-8':
        if hasattr(sys.stdout, 'reconfigure'):
            try:
                sys.stdout.reconfigure(encoding='utf-8')
            except Exception as e:
                print(f"设置控制台编码时出错: {e}")

    try:
        # 为这个线程创建新的数据库连接
        thread_db = WeatherDatabase()

        # 确保文件路径是绝对路径并且存在
        if not os.path.isabs(file_path):
            file_path = os.path.abspath(file_path)

        if not os.path.exists(file_path):
            error_msg = f"文件不存在: {file_path}"
            print(error_msg)
            import_progress[task_id]['status'] = 'error'
            import_progress[task_id]['message'] = error_msg
            return

        print(f"开始处理文件: {file_path}, 类型: {file_type}")

        def update_progress(current, total):
            progress = int((current / total) * 100) if total > 0 else 0
            message = f"批量处理中... {progress}% ({current}/{total})"
            import_progress[task_id]['progress'] = progress
            import_progress[task_id]['message'] = message
        
        # 检查文件类型
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # 检查文件是否是Excel文件
        is_excel = False
        if file_ext == '.xlsx' or file_ext == '.xls':
            is_excel = True
        elif file_ext == '':
            # 如果没有文件扩展名，尝试检测文件内容
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(8)
                    # Excel文件的头部特征
                    if header.startswith(b'\x50\x4B\x03\x04'):  # .xlsx
                        is_excel = True
                        file_ext = '.xlsx'
                    elif header.startswith(b'\xD0\xCF\x11\xE0'):  # .xls
                        is_excel = True
                        file_ext = '.xls'
            except Exception as e:
                print(f"检测文件类型时出错: {e}")
        
        print(f"文件类型检测结果: {file_path}, 扩展名={file_ext}, 是否Excel={is_excel}")
        
        # 根据文件类型和扩展名选择合适的导入方法 - 优先使用批量导入
        result = False
        try:
            if file_type == 'stations':
                if is_excel:
                    print(f"使用Excel导入站点数据: {file_path}")
                    result = thread_db.import_stations_from_excel(file_path, progress_callback=update_progress)
                else:
                    print(f"使用CSV批量导入站点数据: {file_path}")
                    # 使用批量导入优化
                    batch_db = WeatherDatabaseBatchOptimized(thread_db.db_name)
                    try:
                        result = batch_db.import_stations_from_csv_batch(file_path, progress_callback=update_progress)
                    finally:
                        batch_db.close()
            elif file_type == 'observations':
                if is_excel:
                    print(f"使用Excel导入观测数据: {file_path}")
                    result = thread_db.import_observations_from_excel(file_path, progress_callback=update_progress)
                else:
                    print(f"使用CSV批量导入观测数据: {file_path}")
                    # 使用批量导入优化
                    batch_db = WeatherDatabaseBatchOptimized(thread_db.db_name)
                    try:
                        result = batch_db.import_observations_from_csv_batch(file_path, progress_callback=update_progress)
                    finally:
                        batch_db.close()
            else:
                error_msg = f"未知的文件类型: {file_type}"
                print(error_msg)
                import_progress[task_id]['status'] = 'error'
                import_progress[task_id]['message'] = error_msg
                return
        except Exception as e:
            error_msg = f"导入数据时出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            import_progress[task_id]['status'] = 'error'
            import_progress[task_id]['message'] = error_msg
            return
        
        if result:
            # 导入成功
            success_msg = f"成功导入{file_type}数据"
            print(success_msg)
            import_progress[task_id]['status'] = 'completed'
            import_progress[task_id]['message'] = success_msg
            import_progress[task_id]['progress'] = 100
            import_progress[task_id]['result'] = {'success': True, 'message': success_msg}
        else:
            # 导入失败
            error_msg = f"导入{file_type}数据失败"
            print(error_msg)
            import_progress[task_id]['status'] = 'error'
            import_progress[task_id]['message'] = error_msg
            import_progress[task_id]['result'] = {'success': False, 'message': error_msg}
    except Exception as e:
        error_msg = f"从 '{file_path}' 导入{file_type}数据时发生严重错误: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        import_progress[task_id]['status'] = 'error'
        import_progress[task_id]['message'] = error_msg
    finally:
        # 关闭数据库连接
        if 'thread_db' in locals():
            try:
                thread_db.close()
                print("数据库连接已关闭")
            except Exception as e:
                print(f"关闭数据库连接时出错: {e}")

        # 清理上传的文件（本地使用，节省空间）
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"已清理上传文件: {file_path}")
        except Exception as e:
            print(f"清理上传文件时出错: {e}")

@app.route('/api/import_progress/<task_id>')
def get_import_progress(task_id):
    if task_id not in import_progress:
        return jsonify({'success': False, 'message': '任务不存在'})
    
    progress_data = import_progress[task_id].copy()
    
    # 如果任务已完成，可以从进度字典中移除（可选）
    if progress_data.get('status') in ['completed', 'error'] and 'result' in progress_data:
        result = progress_data.pop('result', {'success': False, 'message': '未知错误'})
        progress_data['result'] = result
    
    return jsonify(progress_data)

@app.route('/datacenter/station/<station_id>')
def station_detail(station_id):
    db_conn = get_db()
    station = db_conn.get_station_by_id(station_id)
    if not station:
        return render_template('error.html', message='站点不存在')

    # 获取观测数据统计信息
    observation_stats = db_conn.get_observation_statistics(station_id)

    # 打印调试信息，确认站点数据正确
    print(f"获取到站点信息: {station}")
    print(f"观测数据统计: {observation_stats['basic']}")

    return render_template('station_detail.html',
                         station=station,
                         observation_stats=observation_stats,
                         amap_api_key=app.config['AMAP_API_KEY'])



if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
