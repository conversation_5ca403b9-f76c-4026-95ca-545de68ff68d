{% extends "base.html" %}

{% block title %}首页 - 气象站点数据库管理系统{% endblock %}

{% block content %}
<!-- 现代化英雄区域 -->
<div class="hero-section mb-5 position-relative overflow-hidden">
    <div class="position-absolute w-100 h-100" style="background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(6, 182, 212, 0.8) 100%); z-index: 1;"></div>

    <!-- 动态背景粒子效果 -->
    <div class="position-absolute w-100 h-100" style="z-index: 1;">
        <div class="particle" style="position: absolute; width: 4px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 50%; top: 20%; left: 10%; animation: float 6s ease-in-out infinite;"></div>
        <div class="particle" style="position: absolute; width: 6px; height: 6px; background: rgba(255,255,255,0.2); border-radius: 50%; top: 60%; left: 80%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="particle" style="position: absolute; width: 3px; height: 3px; background: rgba(255,255,255,0.4); border-radius: 50%; top: 40%; left: 60%; animation: float 7s ease-in-out infinite;"></div>
        <div class="particle" style="position: absolute; width: 5px; height: 5px; background: rgba(255,255,255,0.25); border-radius: 50%; top: 80%; left: 20%; animation: float 9s ease-in-out infinite reverse;"></div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row py-5">
            <div class="col-md-12 text-center text-white py-5">
                <div class="fade-in">
                    <h1 class="display-3 fw-bold mb-3" style="text-shadow: 0 4px 20px rgba(0,0,0,0.3); font-weight: 800;">
                        气象站点数据库管理系统
                    </h1>
                    <p class="lead fs-4 mb-4 animate-delay-1" style="text-shadow: 0 2px 10px rgba(0,0,0,0.2); font-weight: 400;">
                        高效管理气象站点数据 · 智能分析 · 实时监控
                    </p>
                    <div class="d-flex justify-content-center gap-3 flex-wrap animate-delay-2">
                        <a href="/datacenter/station_list" class="btn btn-primary btn-lg px-4 py-3 hover-lift">
                            <i class="bi bi-geo-alt me-2"></i>浏览站点
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg px-4 py-3 hover-lift" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="stations">
                            <i class="bi bi-cloud-upload me-2"></i>导入数据
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 波浪效果 -->
    <div class="position-absolute bottom-0 w-100" style="z-index: 1;">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="#ffffff"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="#ffffff"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="#ffffff"></path>
        </svg>
    </div>
</div>

<!-- 现代化功能卡片区 -->
<div class="container mb-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="h1 fw-bold mb-3 fade-in">核心功能</h2>
            <p class="lead text-muted mb-5 fade-in animate-delay-1">强大的气象数据管理平台，为您提供全方位的数据解决方案</p>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow-soft h-100 hover-card fade-in animate-delay-1">
                <div class="card-body text-center p-4 position-relative">
                    <div class="icon-wrapper mb-4 mx-auto gradient-primary float-animation" style="width: 80px; height: 80px;">
                        <i class="bi bi-geo-alt text-white" style="font-size: 2.5rem;"></i>
                    </div>
                    <h3 class="card-title h4 mb-3 fw-bold">站点管理</h3>
                    <p class="card-text text-muted mb-4 lh-lg">高效管理全国气象站点信息，支持批量导入、编辑和查询操作，实现数据的统一管理</p>
                    <a href="/datacenter/station_list" class="btn btn-primary hover-lift">
                        <i class="bi bi-arrow-right me-2"></i>查看站点列表
                    </a>

                    <!-- 装饰性元素 -->
                    <div class="position-absolute top-0 end-0 p-3 opacity-25">
                        <i class="bi bi-geo-alt" style="font-size: 3rem; color: var(--primary-color);"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow-soft h-100 hover-card fade-in animate-delay-2">
                <div class="card-body text-center p-4 position-relative">
                    <div class="icon-wrapper mb-4 mx-auto gradient-warning float-animation" style="width: 80px; height: 80px; animation-delay: 1s;">
                        <i class="bi bi-cloud-upload text-white" style="font-size: 2.5rem;"></i>
                    </div>
                    <h3 class="card-title h4 mb-3 fw-bold">数据导入</h3>
                    <p class="card-text text-muted mb-4 lh-lg">支持多种格式文件导入，智能识别字段映射，实时显示导入进度，确保数据完整性</p>
                    <a href="#" class="btn btn-warning hover-lift" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="stations">
                        <i class="bi bi-upload me-2"></i>导入站点数据
                    </a>

                    <!-- 装饰性元素 -->
                    <div class="position-absolute top-0 end-0 p-3 opacity-25">
                        <i class="bi bi-cloud-upload" style="font-size: 3rem; color: var(--warning-color);"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow-soft h-100 hover-card fade-in animate-delay-3">
                <div class="card-body text-center p-4 position-relative">
                    <div class="icon-wrapper mb-4 mx-auto gradient-success float-animation" style="width: 80px; height: 80px; animation-delay: 2s;">
                        <i class="bi bi-cloud-drizzle text-white" style="font-size: 2.5rem;"></i>
                    </div>
                    <h3 class="card-title h4 mb-3 fw-bold">观测数据</h3>
                    <p class="card-text text-muted mb-4 lh-lg">查看各站点气象观测数据，支持多维度筛选分析，提供便捷的数据导出功能</p>
                    <a href="#" class="btn btn-success hover-lift" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="observations">
                        <i class="bi bi-database me-2"></i>导入观测数据
                    </a>

                    <!-- 装饰性元素 -->
                    <div class="position-absolute top-0 end-0 p-3 opacity-25">
                        <i class="bi bi-cloud-drizzle" style="font-size: 3rem; color: var(--success-color);"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据统计概览区域 -->
<div class="container mb-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="h1 fw-bold mb-3 fade-in">数据概览</h2>
            <p class="lead text-muted mb-5 fade-in animate-delay-1">实时统计数据，一目了然</p>
        </div>
    </div>

    <div class="row g-4 mb-5">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-soft hover-lift fade-in animate-delay-1">
                <div class="card-body text-center p-4">
                    <div class="icon-wrapper mb-3 mx-auto" style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--primary-color), var(--accent-color));">
                        <i class="bi bi-geo-alt text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <h3 class="h2 fw-bold text-primary mb-1" id="totalStations">-</h3>
                    <p class="text-muted mb-0">气象站点</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-soft hover-lift fade-in animate-delay-2">
                <div class="card-body text-center p-4">
                    <div class="icon-wrapper mb-3 mx-auto" style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--success-color), #059669);">
                        <i class="bi bi-cloud-drizzle text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <h3 class="h2 fw-bold text-success mb-1" id="totalObservations">-</h3>
                    <p class="text-muted mb-0">观测记录</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-soft hover-lift fade-in animate-delay-3">
                <div class="card-body text-center p-4">
                    <div class="icon-wrapper mb-3 mx-auto" style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--warning-color), #d97706);">
                        <i class="bi bi-map text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <h3 class="h2 fw-bold text-warning mb-1" id="totalProvinces">-</h3>
                    <p class="text-muted mb-0">覆盖省份</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-soft hover-lift fade-in animate-delay-4">
                <div class="card-body text-center p-4">
                    <div class="icon-wrapper mb-3 mx-auto" style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--danger-color), #dc2626);">
                        <i class="bi bi-calendar-check text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <h3 class="h2 fw-bold text-danger mb-1" id="latestUpdate">-</h3>
                    <p class="text-muted mb-0">最新更新</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近更新的站点区域 -->
<div class="container mb-5">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="fade-in">
                    <h2 class="h1 fw-bold mb-2">
                        <span class="gradient-primary px-3 py-2 text-white rounded-pill me-3 d-inline-flex align-items-center">
                            <i class="bi bi-geo-alt me-2"></i>
                        </span>
                        最近更新的站点
                    </h2>
                    <p class="text-muted">查看最新添加或更新的气象站点信息</p>
                </div>
                <a href="/datacenter/station_list" class="btn btn-primary hover-lift fade-in animate-delay-1">
                    <i class="bi bi-list me-2"></i>查看所有站点
                    <i class="bi bi-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-soft fade-in animate-delay-2">
                <div class="card-header bg-transparent border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">站点列表</h5>
                        <div class="d-flex gap-2">
                            <span class="badge badge-light">
                                <i class="bi bi-clock me-1"></i>实时更新
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="py-3 fw-bold">站点ID</th>
                                    <th class="py-3 fw-bold">站点名称</th>
                                    <th class="py-3 fw-bold">省份</th>
                                    <th class="py-3 fw-bold">纬度</th>
                                    <th class="py-3 fw-bold">经度</th>
                                    <th class="py-3 fw-bold">观测数量</th>
                                    <th class="py-3 fw-bold text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody id="recentStations">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <div class="spinner-modern me-3"></div>
                                            <div>
                                                <h6 class="mb-1">正在加载站点数据</h6>
                                                <small class="text-muted">请稍候...</small>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化页面
        initializePage();

        // 加载数据
        loadDashboardData();

        // 添加交互效果
        addInteractiveEffects();

        // 设置英雄区域背景
        setupHeroBackground();
    });

    // 初始化页面动画
    function initializePage() {
        // 添加页面加载动画
        const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in');

        // 使用 Intersection Observer 实现滚动动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) translateX(0) scale(1)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        animatedElements.forEach(el => {
            el.style.opacity = '0';
            if (el.classList.contains('slide-in-left')) {
                el.style.transform = 'translateX(-30px)';
            } else if (el.classList.contains('slide-in-right')) {
                el.style.transform = 'translateX(30px)';
            } else if (el.classList.contains('scale-in')) {
                el.style.transform = 'scale(0.9)';
            } else {
                el.style.transform = 'translateY(20px)';
            }
            el.style.transition = 'all 0.6s ease-out';
            observer.observe(el);
        });
    }

    // 加载仪表板数据
    async function loadDashboardData() {
        try {
            // 加载统计数据
            await Promise.all([
                loadStatistics(),
                fetchRecentStations()
            ]);
        } catch (error) {
            console.error('加载数据失败:', error);
            showNotification('数据加载失败，请刷新页面重试', 'error');
        }
    }

    // 加载统计数据
    async function loadStatistics() {
        try {
            const [stationsResponse, provincesResponse] = await Promise.all([
                fetch('/api/stations'),
                fetch('/api/provinces')
            ]);

            const stations = await stationsResponse.json();
            const provinces = await provincesResponse.json();

            // 计算统计数据
            const totalStations = stations.length;
            const totalProvinces = provinces.length;
            const totalObservations = stations.reduce((sum, station) => sum + (station.observation_count || 0), 0);

            // 动画更新数字
            animateNumber('totalStations', totalStations);
            animateNumber('totalObservations', totalObservations);
            animateNumber('totalProvinces', totalProvinces);

            // 更新最新更新时间
            const latestUpdateEl = document.getElementById('latestUpdate');
            if (latestUpdateEl) {
                latestUpdateEl.textContent = new Date().toLocaleDateString('zh-CN');
            }

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 数字动画效果
    function animateNumber(elementId, targetNumber) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const duration = 2000; // 2秒
        const startTime = performance.now();
        const startNumber = 0;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * easeOutQuart);

            element.textContent = currentNumber.toLocaleString('zh-CN');

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // 添加交互效果
    function addInteractiveEffects() {
        // 卡片悬停效果增强
        const hoverCards = document.querySelectorAll('.hover-card');
        hoverCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

                // 添加光晕效果
                this.style.boxShadow = '0 20px 40px rgba(99, 102, 241, 0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
            });
        });

        // 按钮点击波纹效果
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加视差滚动效果
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.float-animation');

            parallaxElements.forEach(element => {
                const speed = 0.5;
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });
    }

    // 设置英雄区域背景
    function setupHeroBackground() {
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            // 创建动态背景
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.zIndex = '0';
            canvas.style.opacity = '0.1';

            heroSection.appendChild(canvas);

            // 响应式画布
            function resizeCanvas() {
                canvas.width = heroSection.offsetWidth;
                canvas.height = heroSection.offsetHeight;
            }

            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);

            // 绘制动态粒子
            const particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1
                });
            }

            function animateParticles() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';

                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fill();
                });

                requestAnimationFrame(animateParticles);
            }

            animateParticles();
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
</script>

<!-- 添加波纹效果样式 -->
<style>
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
</style>
{% endblock %}
