// 省份分布图表功能
document.addEventListener('DOMContentLoaded', function() {
    // 如果页面上有省份分布图表容器，则初始化图表
    if (document.getElementById('provinceDistributionChart')) {
        initProvinceChart();
    }
});

// 初始化省份分布图表
function initProvinceChart() {
    fetch('/api/stations')
        .then(response => response.json())
        .then(stations => {
            // 按省份分组统计站点数量
            const provinceData = {};
            
            stations.forEach(station => {
                const province = station.country || '未知';
                if (!provinceData[province]) {
                    provinceData[province] = 0;
                }
                provinceData[province]++;
            });
            
            // 准备图表数据
            const labels = Object.keys(provinceData);
            const data = Object.values(provinceData);
            
            // 生成随机颜色
            const backgroundColors = labels.map(() => {
                return `rgba(${Math.floor(Math.random() * 200)}, ${Math.floor(Math.random() * 200)}, ${Math.floor(Math.random() * 200)}, 0.7)`;
            });
            
            // 创建图表
            const ctx = document.getElementById('provinceDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: backgroundColors,
                        borderColor: 'rgba(255, 255, 255, 0.7)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: '#333',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: '各省份站点分布',
                            color: '#333',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} 个站点 (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => console.error('加载站点数据失败:', error));
}
