/**
 * 现代化上传模态框功能
 * 提供拖拽上传、进度显示、文件预览等功能
 */

class UploadModal {
    constructor() {
        this.modal = null;
        this.dropZone = null;
        this.fileInput = null;
        this.progressBar = null;
        this.currentFileType = 'stations';
        this.init();
    }

    init() {
        this.loadStyles();
        this.createModal();
        this.bindEvents();
    }

    loadStyles() {
        // 添加自定义样式
        const style = document.createElement('style');
        style.textContent = `
            .data-type-card {
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .data-type-card:hover .data-type-label {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            }

            .data-type-card input[type="radio"]:checked + .data-type-label {
                border-color: var(--bs-primary) !important;
                background: linear-gradient(135deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.1));
            }

            .upload-zone {
                border: 2px dashed #dee2e6;
                border-radius: 15px;
                background: linear-gradient(135deg, #f8f9fa, #ffffff);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .upload-zone:hover {
                border-color: var(--bs-primary);
                background: linear-gradient(135deg, rgba(13, 110, 253, 0.02), rgba(13, 110, 253, 0.05));
            }

            .upload-zone.drag-over {
                border-color: var(--bs-success);
                background: linear-gradient(135deg, rgba(25, 135, 84, 0.05), rgba(25, 135, 84, 0.1));
                transform: scale(1.02);
            }

            .upload-icon-container {
                position: relative;
                display: inline-block;
            }

            .upload-pulse {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 80px;
                height: 80px;
                border: 2px solid var(--bs-primary);
                border-radius: 50%;
                opacity: 0;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% {
                    transform: translate(-50%, -50%) scale(0.8);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(1.5);
                    opacity: 0;
                }
            }

            .upload-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(25, 135, 84, 0.9), rgba(25, 135, 84, 0.8));
                border-radius: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
            }

            .format-card {
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .format-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }

            .format-card[data-format="stations"]:hover {
                border-color: var(--bs-primary) !important;
            }

            .format-card[data-format="observations"]:hover {
                border-color: var(--bs-success) !important;
            }

            .bg-gradient-primary {
                background: linear-gradient(135deg, #0d6efd, #0056b3) !important;
            }

            .bg-gradient-warning {
                background: linear-gradient(135deg, #ffc107, #e0a800) !important;
            }

            .cursor-pointer {
                cursor: pointer;
            }

            .modal-xl {
                max-width: 1200px;
            }

            @media (max-width: 768px) {
                .modal-xl {
                    max-width: 95%;
                    margin: 1rem;
                }
            }
        `;
        document.head.appendChild(style);
    }

    createModal() {
        const modalHTML = `
        <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-gradient-primary text-white">
                        <h5 class="modal-title fw-bold d-flex align-items-center" id="uploadModalLabel">
                            <div class="icon-wrapper me-3" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-cloud-upload" style="font-size: 1.2rem;"></i>
                            </div>
                            <div>
                                <div class="h5 mb-0">数据导入</div>
                                <small class="opacity-75" id="modalSubtitle">选择要导入的数据类型</small>
                            </div>
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-4">
                        <!-- 数据类型选择卡片 -->
                        <div class="mb-4">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="data-type-card" data-type="stations">
                                        <input class="form-check-input d-none" type="radio" name="fileType" id="stationsType" value="stations" checked>
                                        <label class="card h-100 border-2 cursor-pointer data-type-label" for="stationsType">
                                            <div class="card-body text-center p-4">
                                                <div class="icon-wrapper mx-auto mb-3 bg-primary bg-opacity-10" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                    <i class="bi bi-geo-alt text-primary" style="font-size: 1.8rem;"></i>
                                                </div>
                                                <h6 class="fw-bold mb-2">站点数据</h6>
                                                <p class="text-muted small mb-3">气象站点基本信息<br>包含位置、海拔等信息</p>
                                                <div class="badge bg-primary bg-opacity-10 text-primary">
                                                    <i class="bi bi-check-circle me-1"></i>支持批量导入
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="data-type-card" data-type="observations">
                                        <input class="form-check-input d-none" type="radio" name="fileType" id="observationsType" value="observations">
                                        <label class="card h-100 border-2 cursor-pointer data-type-label" for="observationsType">
                                            <div class="card-body text-center p-4">
                                                <div class="icon-wrapper mx-auto mb-3 bg-success bg-opacity-10" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                    <i class="bi bi-cloud-drizzle text-success" style="font-size: 1.8rem;"></i>
                                                </div>
                                                <h6 class="fw-bold mb-2">观测数据</h6>
                                                <p class="text-muted small mb-3">气象观测记录<br>包含温度、降水、风速等</p>
                                                <div class="badge bg-success bg-opacity-10 text-success">
                                                    <i class="bi bi-lightning me-1"></i>高性能导入
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 拖拽上传区域 -->
                        <div class="upload-section mb-4">
                            <div class="upload-zone position-relative" id="uploadZone">
                                <div class="upload-zone-content text-center py-5">
                                    <div class="upload-animation mb-4">
                                        <div class="upload-icon-container">
                                            <i class="bi bi-cloud-upload upload-icon" style="font-size: 3.5rem; color: var(--primary-color);"></i>
                                            <div class="upload-pulse"></div>
                                        </div>
                                    </div>
                                    <h5 class="fw-bold mb-2 text-primary">拖拽文件到此处上传</h5>
                                    <p class="text-muted mb-4">支持 CSV、Excel 格式文件，最大 50MB</p>
                                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                                        <button type="button" class="btn btn-primary btn-lg px-4" id="selectFileBtn">
                                            <i class="bi bi-folder2-open me-2"></i>选择文件
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-lg px-4" id="downloadTemplateBtn">
                                            <i class="bi bi-download me-2"></i>下载模板
                                        </button>
                                    </div>
                                    <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" style="display: none;">
                                </div>
                                <div class="upload-overlay d-none">
                                    <div class="text-center text-white">
                                        <i class="bi bi-cloud-upload" style="font-size: 4rem;"></i>
                                        <h5 class="mt-3">释放文件开始上传</h5>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 文件信息显示 -->
                        <div class="file-info d-none mb-4" id="fileInfo">
                            <div class="card border-0 shadow-sm bg-success bg-opacity-5">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <div class="file-icon-wrapper me-3">
                                                <i class="bi bi-file-earmark-spreadsheet text-success" style="font-size: 2.5rem;"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-bold text-success" id="fileName">文件名</h6>
                                                <div class="d-flex align-items-center gap-3">
                                                    <small class="text-muted" id="fileSize">文件大小</small>
                                                    <span class="badge bg-success bg-opacity-10 text-success">
                                                        <i class="bi bi-check-circle me-1"></i>格式正确
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm rounded-pill" id="removeFile" title="移除文件">
                                            <i class="bi bi-x-lg"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 进度条 -->
                        <div class="upload-progress d-none mb-4" id="uploadProgress">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-center justify-content-between mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <span class="fw-bold">导入进度</span>
                                        </div>
                                        <span class="badge bg-primary" id="progressText">0%</span>
                                    </div>
                                    <div class="progress mb-3" style="height: 12px; border-radius: 10px;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-gradient-primary"
                                             id="progressBar" role="progressbar" style="width: 0%; border-radius: 10px;"></div>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <small class="text-muted" id="statusText">准备导入...</small>
                                        <small class="text-muted" id="progressStats">0 / 0 条记录</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 批量导入优化提示 -->
                        <div class="performance-info mb-4">
                            <div class="card border-0 bg-gradient-warning text-white">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-lightning-charge me-3" style="font-size: 1.5rem;"></i>
                                        <div>
                                            <h6 class="fw-bold mb-1">🚀 批量导入优化</h6>
                                            <small>
                                                CSV文件使用高性能批量导入技术，大数据量导入速度提升10-100倍！
                                                <br>支持万级数据快速导入，自动事务管理，确保数据完整性。
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 格式说明 -->
                        <div class="format-info">
                            <div class="d-flex align-items-center mb-4">
                                <div class="icon-wrapper me-3" style="width: 40px; height: 40px; background: rgba(13, 110, 253, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="bi bi-info-circle text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1">文件格式要求</h6>
                                    <small class="text-muted">请确保文件格式符合以下要求</small>
                                </div>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm h-100 format-card" data-format="stations">
                                        <div class="card-body p-4">
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="bi bi-geo-alt text-primary me-2" style="font-size: 1.2rem;"></i>
                                                <h6 class="fw-bold mb-0 text-primary">站点数据格式</h6>
                                            </div>
                                            <div class="mb-3">
                                                <div class="badge bg-primary bg-opacity-10 text-primary mb-2">
                                                    <i class="bi bi-lightning me-1"></i>CSV批量优化
                                                </div>
                                            </div>
                                            <div class="format-details">
                                                <div class="mb-2">
                                                    <strong class="text-success">必需字段:</strong>
                                                    <div class="ms-3 small text-muted">
                                                        • 区站号 (唯一标识)<br>
                                                        • 站名 (站点名称)<br>
                                                        • 纬度 (地理坐标)<br>
                                                        • 经度 (地理坐标)
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong class="text-info">可选字段:</strong>
                                                    <div class="ms-3 small text-muted">
                                                        • 省名、地市、测站高度<br>
                                                        • 区县、乡镇等信息
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm h-100 format-card" data-format="observations">
                                        <div class="card-body p-4">
                                            <div class="d-flex align-items-center mb-3">
                                                <i class="bi bi-cloud-drizzle text-success me-2" style="font-size: 1.2rem;"></i>
                                                <h6 class="fw-bold mb-0 text-success">观测数据格式</h6>
                                            </div>
                                            <div class="mb-3">
                                                <div class="badge bg-success bg-opacity-10 text-success mb-2">
                                                    <i class="bi bi-lightning me-1"></i>高性能导入
                                                </div>
                                            </div>
                                            <div class="format-details">
                                                <div class="mb-2">
                                                    <strong class="text-success">必需字段:</strong>
                                                    <div class="ms-3 small text-muted">
                                                        • 区站号 (关联站点)<br>
                                                        • 资料时间 (观测时间)
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong class="text-info">可选字段:</strong>
                                                    <div class="ms-3 small text-muted">
                                                        • 气温、湿度、降水量<br>
                                                        • 风速、风向、气压等<br>
                                                        • <span class="text-warning">⚠️ 0值会正确保存</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 文件格式提示 -->
                            <div class="mt-4">
                                <div class="alert alert-info border-0 bg-info bg-opacity-10">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-lightbulb text-info me-2 mt-1"></i>
                                        <div>
                                            <strong>格式提示:</strong>
                                            <ul class="mb-0 mt-2 small">
                                                <li>支持 <code>.csv</code>、<code>.xlsx</code>、<code>.xls</code> 格式</li>
                                                <li>CSV文件建议使用UTF-8编码，支持中文</li>
                                                <li>Excel文件支持第一个工作表的数据</li>
                                                <li>文件大小限制：50MB以内</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light border-0 p-4">
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <div class="text-muted small">
                                <i class="bi bi-shield-check me-1"></i>
                                数据安全传输，支持事务回滚
                            </div>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-2"></i>取消
                                </button>
                                <button type="button" class="btn btn-primary px-4" id="uploadBtn" disabled>
                                    <i class="bi bi-upload me-2"></i>开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;

        // 如果模态框不存在，则创建
        if (!document.getElementById('uploadModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        this.modal = document.getElementById('uploadModal');
        this.dropZone = document.getElementById('uploadZone');
        this.fileInput = document.getElementById('fileInput');
        this.progressBar = document.getElementById('progressBar');
    }

    bindEvents() {
        // 文件类型选择
        const fileTypeRadios = document.querySelectorAll('input[name="fileType"]');
        fileTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentFileType = e.target.value;
                this.updateModalTitle();
                this.updateDataTypeSelection();
            });
        });

        // 数据类型卡片点击
        const dataTypeCards = document.querySelectorAll('.data-type-card');
        dataTypeCards.forEach(card => {
            card.addEventListener('click', () => {
                const radio = card.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                    this.currentFileType = radio.value;
                    this.updateModalTitle();
                    this.updateDataTypeSelection();
                }
            });
        });

        // 拖拽事件
        this.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
        this.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.dropZone.addEventListener('drop', this.handleDrop.bind(this));

        // 点击选择文件
        document.getElementById('selectFileBtn').addEventListener('click', () => {
            this.fileInput.click();
        });

        // 文件选择
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // 移除文件
        document.getElementById('removeFile').addEventListener('click', this.removeFile.bind(this));

        // 上传按钮
        document.getElementById('uploadBtn').addEventListener('click', this.startUpload.bind(this));

        // 模态框事件
        this.modal.addEventListener('show.bs.modal', this.onModalShow.bind(this));
        this.modal.addEventListener('hidden.bs.modal', this.onModalHidden.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        this.dropZone.classList.add('drag-over');
        const overlay = this.dropZone.querySelector('.upload-overlay');
        if (overlay) {
            overlay.classList.remove('d-none');
        }
    }

    handleDragLeave(e) {
        e.preventDefault();
        // 只有当鼠标真正离开dropZone时才移除样式
        if (!this.dropZone.contains(e.relatedTarget)) {
            this.dropZone.classList.remove('drag-over');
            const overlay = this.dropZone.querySelector('.upload-overlay');
            if (overlay) {
                overlay.classList.add('d-none');
            }
        }
    }

    handleDrop(e) {
        e.preventDefault();
        this.dropZone.classList.remove('drag-over');
        const overlay = this.dropZone.querySelector('.upload-overlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }

    handleFile(file) {
        // 验证文件类型
        const allowedTypes = ['.csv', '.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            this.showAlert('请选择CSV或Excel文件', 'warning');
            return;
        }

        // 验证文件大小 (16MB)
        if (file.size > 16 * 1024 * 1024) {
            this.showAlert('文件大小不能超过16MB', 'warning');
            return;
        }

        this.selectedFile = file;
        this.showFileInfo(file);
        this.enableUploadButton();
    }

    showFileInfo(file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = this.formatFileSize(file.size);
        document.getElementById('fileInfo').classList.remove('d-none');
        document.getElementById('uploadZone').style.display = 'none';
    }

    removeFile() {
        this.selectedFile = null;
        this.fileInput.value = '';
        document.getElementById('fileInfo').classList.add('d-none');
        document.getElementById('uploadZone').style.display = 'block';
        this.disableUploadButton();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    enableUploadButton() {
        document.getElementById('uploadBtn').disabled = false;
    }

    disableUploadButton() {
        document.getElementById('uploadBtn').disabled = true;
    }

    updateModalTitle() {
        const isStations = this.currentFileType === 'stations';
        const title = isStations ? '导入站点数据' : '导入观测数据';
        const subtitle = isStations ? '气象站点基本信息' : '气象观测记录数据';
        const icon = isStations ? 'bi-geo-alt' : 'bi-cloud-drizzle';

        document.getElementById('uploadModalLabel').innerHTML = `
            <div class="icon-wrapper me-3" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                <i class="bi ${icon}" style="font-size: 1.2rem;"></i>
            </div>
            <div>
                <div class="h5 mb-0">${title}</div>
                <small class="opacity-75" id="modalSubtitle">${subtitle}</small>
            </div>
        `;
    }

    updateDataTypeSelection() {
        // 更新数据类型卡片的选中状态
        const cards = document.querySelectorAll('.data-type-card');
        cards.forEach(card => {
            const radio = card.querySelector('input[type="radio"]');
            const label = card.querySelector('.data-type-label');
            if (radio && label) {
                if (radio.checked) {
                    label.style.borderColor = 'var(--bs-primary)';
                    label.style.background = 'linear-gradient(135deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.1))';
                } else {
                    label.style.borderColor = '';
                    label.style.background = '';
                }
            }
        });
    }

    async startUpload() {
        if (!this.selectedFile) return;

        const formData = new FormData();
        formData.append('file', this.selectedFile);
        formData.append('file_type', this.currentFileType);

        this.showProgress();
        this.disableUploadButton();

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.trackProgress(result.task_id);
            } else {
                this.showAlert(result.message || '上传失败', 'danger');
                this.hideProgress();
                this.enableUploadButton();
            }
        } catch (error) {
            this.showAlert('网络错误，请重试', 'danger');
            this.hideProgress();
            this.enableUploadButton();
        }
    }

    async trackProgress(taskId) {
        const checkProgress = async () => {
            try {
                const response = await fetch(`/api/import_progress/${taskId}`);
                const data = await response.json();

                // 为批量导入添加特殊的进度消息
                let message = data.message || '处理中...';
                if (message.includes('批量处理中')) {
                    message = `🚀 ${message} (高性能批量导入)`;
                }
                this.updateProgress(data.progress || 0, message);

                if (data.status === 'completed') {
                    this.updateProgress(100, '✅ 批量导入完成！数据已成功导入数据库');
                    setTimeout(() => {
                        this.hideModal();
                        this.showAlert('🎉 数据批量导入成功！性能优化生效，导入速度大幅提升！', 'success');
                        // 刷新页面数据
                        if (typeof loadDashboardData === 'function') {
                            loadDashboardData();
                        } else {
                            window.location.reload();
                        }
                    }, 1500);
                } else if (data.status === 'error') {
                    this.showAlert(data.message || '导入失败', 'danger');
                    this.hideProgress();
                    this.enableUploadButton();
                } else {
                    setTimeout(checkProgress, 1000);
                }
            } catch (error) {
                this.showAlert('检查进度失败', 'danger');
                this.hideProgress();
                this.enableUploadButton();
            }
        };

        checkProgress();
    }

    showProgress() {
        document.getElementById('uploadProgress').classList.remove('d-none');
    }

    hideProgress() {
        document.getElementById('uploadProgress').classList.add('d-none');
    }

    updateProgress(percent, message) {
        document.getElementById('progressBar').style.width = percent + '%';
        document.getElementById('progressText').textContent = percent + '%';
        document.getElementById('statusText').textContent = message;
    }

    showAlert(message, type) {
        // 创建临时警告框
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.style.minWidth = '300px';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }

    hideModal() {
        const modal = bootstrap.Modal.getInstance(this.modal);
        if (modal) {
            modal.hide();
        }
    }

    onModalShow(e) {
        // 设置文件类型
        const button = e.relatedTarget;
        if (button && button.dataset.fileType) {
            this.currentFileType = button.dataset.fileType;
            document.getElementById(this.currentFileType + 'Type').checked = true;
            this.updateModalTitle();
        }
    }

    onModalHidden() {
        // 重置模态框状态
        this.removeFile();
        this.hideProgress();
        this.enableUploadButton();
    }
}

// 初始化上传模态框
document.addEventListener('DOMContentLoaded', function() {
    new UploadModal();
});
