from scipy.spatial import cKDTree
import pandas as pd
import numpy as np
import rasterio


# 读取Excel文件
excel_file1 = r'D:\python\Disaster_risk_assessment\drought\data\2022年成都参证站列表逐日MCI指数统计图.xlsx'
excel_file2 = r'D:\python\Disaster_risk_assessment\drought\data\drought_process_results.xlsx'
df1 = pd.read_excel(excel_file1, sheet_name='sheet2')
df2 = pd.read_excel(excel_file2, sheet_name='危险性指数')
lon = df1['经度'].values
lat = df1['纬度'].values
risk_level = df2['危险性指数'].values

# 读取TIFF文件获取地理信息
tiff_path = r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\tif\hazard_level.tif'
with rasterio.open(tiff_path) as src:
    data = src.read(1)
    data[data == -999] = 0
    meta = src.meta
    bounds = src.bounds
    height = src.height
    width = src.width
    transform = src.transform  # 获取transform
    
    # 生成网格坐标
    x = np.linspace(bounds.left, bounds.right, width)
    y = np.linspace(bounds.top, bounds.bottom, height)
    grid_x, grid_y = np.meshgrid(x, y)  



# IDW插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values)-1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)

# 执行插值
interpolated = safe_idw_interpolation(
    np.column_stack((lon, lat)), 
    risk_level, grid_x, grid_y, power=2, k=10
)


# 自然断点法分类
def classify_risk_level(risk_score):
    import jenkspy
    breaks = jenkspy.jenks_breaks(risk_score, n_classes=5)
    risk_level = []
    for score in risk_score:
        if score <= breaks[1]:
            risk_level.append(1)
        elif breaks[1] < score <= breaks[2]:
            risk_level.append(2)
        elif breaks[2] < score <= breaks[3]:
            risk_level.append(3)
        elif breaks[3] < score <= breaks[4]:
            risk_level.append(4)
        else:
            risk_level.append(5)
    return risk_level

risk_level_classes = np.array(classify_risk_level(interpolated.flatten())).reshape(height, width)

# 保存为TIFF文件
output_file = r'D:\python\Disaster_risk_assessment\drought\data\tif\hazard_level_self.tif'
with rasterio.open(
    output_file, 'w',
    driver='GTiff',
    height=height, 
    width=width,
    count=1,
    dtype=rasterio.float32,
    crs=meta['crs'],
    transform=meta['transform']
) as dst:
    dst.write(risk_level_classes.astype(rasterio.float32), 1)
    dst.update_tags(
        title='Hazard Level',
        units='unitless',
        nodata=0,  # 设置nodata值为0
        description='Hazard Level'  # 添加描述信息
    )


with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\year\data\czt\人口_裁剪.tif') as src:  
    meta = src.meta
    data2 = src.read(1)
    # 获取经纬度范围
    bounds = src.bounds
    min_x, min_y, max_x, max_y = bounds.left, bounds.bottom, bounds.right, bounds.top
    #print(f"最小经度: {min_x}, 最小纬度: {min_y}, 最大经度: {max_x}, 最大纬度: {max_y}")  
    #print(meta)
    #print(data.shape)
'''
print(risk_level_classes.shape)
print(data2.shape)
'''


# 计算每个网格点的风险等级
R = 0
c = 1/(1+R)
risk_level_index = interpolated * data2 * c

# 转换为风险等级
risk_level_index_classes = classify_risk_level(risk_level_index.flatten())
# 将等级转换为整数
risk_level_index_classes = [int(level) for level in risk_level_index_classes]
# 将等级转换为二维数组
risk_level_index_classes = np.array(risk_level_index_classes).reshape(height, width)


'''
print(risk_level_index_classes.shape)
print(risk_level_index_classes.max())
print(risk_level_index_classes.min())
'''


# 将结果保存为TIFF文件
output_file = r'D:\python\Disaster_risk_assessment\drought\data\tif\assess_level_self.tif'
with rasterio.open(
    output_file, 'w',
    driver='GTiff',
    height=height,
    width=width,
    count=1,
    dtype=rasterio.float32,
    crs=meta['crs'],
    transform=meta['transform']
) as dst:
    dst.write(risk_level_index_classes.astype(rasterio.float32), 1)
    dst.update_tags(
        title='Assess Level',
        units='unitless',
        nodata=0,  # 设置nodata值为0
        description='Assess Level',  # 添加描述信息        
        min=risk_level_index_classes.min(),  # 设置最小值
        max=risk_level_index_classes.max()  # 设置最大值 
    )
