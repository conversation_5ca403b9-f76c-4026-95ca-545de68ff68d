from collections import Counter
import numpy as np
import pandas as pd

def read_data():
    """读取并预处理数据"""
    # 从Excel文件读取数据
    data = pd.read_excel(r'D:\python\High_impact_weather\data\中国地面日值数据(SURF_CHN_MUL_DAY).xlsx')
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']
    data = data[data['年'] == 2024]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d')
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    # 按日期排序
    data.sort_index(inplace=True)
    return data

# 定义信息输出函数
def print_info(a,b):
    """格式化输出信息"""
    data = read_data()
    temperature_mask = (data['最高气温'] >= a) & (data['最高气温'] < b)
    # 获取所有符合条件的日期
    temp_dates = data[temperature_mask].index
    # 提取月份信息
    temp_months = temp_dates.month
    # 统计每个月份的高温天气天数
    month_counts = Counter(temp_months)
    # 生成完整月份数据（1-12月）
    all_months = range(1, 13)
    month_data = {f"{m}月": int((month_counts.get(m, 0))) for m in all_months}
    #print(full_month_data)
    temp_days = temperature_mask.sum()
    return month_data, int(temp_days/5)



# 计算高温天气天数(轻度高温 ≥ 35℃)
def calculate_high_temperature_weather():
    """
    计算高温天气天数
    轻度高温 35~37℃,包含35℃
    中度高温 37~40℃,包含37℃
    重度高温 ≥ 40℃
    """
    #轻度高温
    light_temp,light_days = print_info(35,37)
    #中度高温
    moderate_temp,moderate_days = print_info(37,40)
    #重度高温
    severe_temp,severe_days = print_info(40,100)
    # 计算高温天气总天数
    total_days = light_days + moderate_days + severe_days
    return light_temp,moderate_temp,severe_temp,total_days



if __name__ == "__main__":

    # 计算高温天气
    light_temp,moderate_temp,severe_temp, total_days = calculate_high_temperature_weather()
    #print("高温总日数:", int(total_days))
    print("轻度高温:", light_temp)
    print("中度高温:", moderate_temp)
    print("重度高温:", severe_temp)
