import pandas as pd
import numpy as np
import os
import time
from matplotlib.font_manager import FontProperties

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\Microsoft YaHei.ttf")
except:
    print("未找到中文字体，图表中文可能无法正确显示")
    font = FontProperties()

# 数据路径
file_path = r"D:\python\四川\monitoring\Climate events\cold\data\2024-2025年156站最低气温.xlsx"

def read_data(file_path):
    """
    读取气象站点最低气温数据
    """
    print("正在读取数据...")
    
    df = pd.read_excel(file_path)

    # 确保列名正确
    expected_columns = ['区站号(字符)', '站名', '资料时间', '最低气温']
    for col in expected_columns:
        if col not in df.columns:
            print(f"警告: 未找到列 '{col}'，可用列: {df.columns.tolist()}")

    # 确保资料时间列为日期时间格式
    df['资料时间'] = pd.to_datetime(df['资料时间'])

    # 按站点和日期排序
    df = df.sort_values(by=['区站号(字符)', '资料时间'])

    # 处理缺失值
    if df['最低气温'].isna().sum() > 0:
        print(f"警告: 发现 {df['最低气温'].isna().sum()} 条缺失的最低气温数据")

    print(f"数据读取完成，共 {len(df)} 条记录，{df['区站号(字符)'].nunique()} 个站点")
    return df

def find_continuous_cooling_processes(df):
    """
    寻找连续降温过程
    """
    print("寻找连续降温过程...")
    process_results = []

    # 为每个站点单独识别过程
    for station_id, station_data in df.groupby('区站号(字符)'):
        station_data = station_data.sort_values('资料时间').reset_index(drop=True)
        station_name = station_data['站名'].iloc[0]

        # 使用向量化操作识别降温
        temps = station_data['最低气温'].values
        if len(temps) < 2:
            continue

        # 计算温度差异（当前温度 - 前一天温度）
        temp_diffs = temps[1:] - temps[:-1]
        cooling_mask = temp_diffs < 0  # True表示降温

        # 识别连续降温段
        processes = []
        start_idx = None

        for i, is_cooling in enumerate(cooling_mask):
            if is_cooling:
                if start_idx is None:
                    start_idx = i  # 降温开始（注意i对应的是第i+1天）
            else:
                if start_idx is not None:
                    # 降温结束，检查是否满足最少2天的要求
                    if i - start_idx >= 1:  # 至少连续降温1次（即2天）
                        # 过程包括降温开始前一天到降温结束当天
                        process_data = station_data.iloc[start_idx:i+1].copy()
                        processes.append(process_data)
                    start_idx = None

        # 处理最后一个过程（如果数据在降温中结束）
        if start_idx is not None and len(cooling_mask) - start_idx >= 1:
            process_data = station_data.iloc[start_idx:].copy()
            processes.append(process_data)

        # 为每个过程生成结果
        for process_data in processes:
            if len(process_data) >= 2:  # 确保至少有2天数据
                start_date = process_data.iloc[1]['资料时间']  # 降温开始的第二日
                end_date = process_data.iloc[-1]['资料时间']   # 降温结束日
                process_results.append({
                    '站号': station_id,
                    '站名': station_name,
                    '开始时间': start_date,
                    '结束时间': end_date,
                    '过程数据': process_data
                })

    print(f"共找到 {len(process_results)} 个连续降温过程")
    return pd.DataFrame(process_results)

def calculate_temperature_drops_for_processes(process_df):
    """
    基于连续降温过程计算降温幅度
    """
    print("计算降温幅度...")

    # 预分配结果列
    process_df['24小时温差'] = 0.0
    process_df['48小时温差'] = 0.0
    process_df['72小时温差'] = 0.0

    for index, row in process_df.iterrows():
        process_data = row['过程数据']
        temps = process_data['最低气温'].values 

        if len(temps) < 2:
            continue

        # 计算24小时降温幅度
        if len(temps) >= 2:
            dT24_values = temps[:-1] - temps[1:]  # 向量化计算
            dT24_values = dT24_values[dT24_values > 0]  # 只保留正值
            max_dT24 = dT24_values.max() if len(dT24_values) > 0 else 0
            process_df.at[index, '24小时温差'] = round(max_dT24, 1)

        # 计算48小时降温幅度
        if len(temps) >= 3:
            # 计算未来2天的最低温度
            min_temps_2days = np.minimum(temps[1:-1], temps[2:])
            dT48_values = temps[:-2] - min_temps_2days
            dT48_values = dT48_values[dT48_values > 0]
            max_dT48 = dT48_values.max() if len(dT48_values) > 0 else 0
            process_df.at[index, '48小时温差'] = round(max_dT48, 1)

        # 计算72小时降温幅度
        if len(temps) >= 4:
            # 计算未来3天的最低温度
            min_temps_3days = np.minimum.reduce([temps[1:-2], temps[2:-1], temps[3:]])
            dT72_values = temps[:-3] - min_temps_3days
            dT72_values = dT72_values[dT72_values > 0]
            max_dT72 = dT72_values.max() if len(dT72_values) > 0 else 0
            process_df.at[index, '72小时温差'] = round(max_dT72, 1)

    print("降温幅度计算完成")
    return process_df

def determine_cold_air_level_for_processes(process_df):
    """
    根据降温幅度和最低气温判断单站冷空气等级
    等级: 0-无冷空气, 1-中等强度冷空气, 2-强冷空气, 3-寒潮
    """
    print("判断单站冷空气等级...")

    # 创建冷空气等级列，默认为0（无冷空气）
    process_df['cold_air_level'] = 0

    for index, row in process_df.iterrows():
        process_data = row['过程数据']
        min_temp_next_1 = process_data['最低气温'].min()
        min_temp_next_2 = process_data['最低气温'].min()
        min_temp_next_3 = process_data['最低气温'].min()

        # 1. 优先判断寒潮 (Level 3)
        # 寒潮: 满足三个条件之一且最低气温≤4℃
        # 1.1. 单站∆T24≥8℃
        mask_severe_1 = (row['24小时温差'] >= 8) & (min_temp_next_1 <= 4)
        # 1.2. 单站∆T48≥10℃且48h是日最低气温必须是连续下降的
        mask_severe_2 = (
            (row['48小时温差'] >= 10) &
            (min_temp_next_2 <= 4)
        )
        # 1.3. 单站∆T72≥12℃且72h是日最低气温必须是连续下降的
        mask_severe_3 = (
            (row['72小时温差'] >= 12) &
            (min_temp_next_3 <= 4)
        )
        mask_severe = mask_severe_1 | mask_severe_2 | mask_severe_3
        if mask_severe:
            process_df.at[index, 'cold_air_level'] = 3

        # 2. 其次判断强冷空气 (Level 2)
        # 强冷空气: 单站∆T48≥8℃的冷空气, 且尚未被评为寒潮
        mask_strong = (row['48小时温差'] >= 8)
        if mask_strong and process_df.at[index, 'cold_air_level'] == 0:
            process_df.at[index, 'cold_air_level'] = 2

        # 3. 最后判断中等强度冷空气 (Level 1)
        # 中等强度冷空气: 8℃>单站∆T48≥6℃的冷空气, 且尚未被评为更高等级
        mask_medium = (row['48小时温差'] >= 6) & (row['48小时温差'] < 8)
        if mask_medium and process_df.at[index, 'cold_air_level'] == 0:
            process_df.at[index, 'cold_air_level'] = 1

    # 将冷空气等级映射为文字描述
    level_mapping = {
        0: '无冷空气',
        1: '中等强度冷空气',
        2: '强冷空气',
        3: '寒潮'
    }
    process_df['冷空气强度'] = process_df['cold_air_level'].map(level_mapping)

    print("单站冷空气等级判断完成")
    return process_df

def create_daily_cold_air_statistics(df, process_df):

    """
    创建每日冷空气站点统计
    基于单站冷空气过程结果，统计每日各等级冷空气影响的站点数
    """

    print("创建每日冷空气站点统计...")

    # 获取所有日期范围
    all_dates = pd.date_range(start=df['资料时间'].min(), end=df['资料时间'].max(), freq='D')
    total_stations = df['区站号(字符)'].nunique()

    # 初始化每日统计字典，使用字典提高查找效率
    daily_stats_dict = {date: {'中等强度站点数': 0, '强冷空气站点数': 0, '寒潮站点数': 0}
                       for date in all_dates}

    # 一次性处理所有过程，避免嵌套循环
    for _, row in process_df.iterrows():
        process_data = row['过程数据']
        level = row['cold_air_level']

        # 只处理有冷空气等级的过程
        if level >= 1:
            # 获取过程中的所有日期
            process_dates = process_data['资料时间'].dt.normalize()

            # 为每个日期增加对应等级的计数
            for process_date in process_dates:
                if process_date in daily_stats_dict:
                    if level == 1:
                        daily_stats_dict[process_date]['中等强度站点数'] += 1
                    elif level == 2:
                        daily_stats_dict[process_date]['强冷空气站点数'] += 1
                    elif level == 3:
                        daily_stats_dict[process_date]['寒潮站点数'] += 1

    # 转换为DataFrame格式
    daily_stats = []
    for date in all_dates:
        stats = daily_stats_dict[date]
        total_affected = stats['中等强度站点数'] + stats['强冷空气站点数'] + stats['寒潮站点数']

        daily_stats.append({
            '日期': date,
            '中等强度站点数': stats['中等强度站点数'],
            '强冷空气站点数': stats['强冷空气站点数'],
            '寒潮站点数': stats['寒潮站点数'],
            '总影响站点数': total_affected,
            '影响站点比例': round(total_affected / total_stations * 100, 1) if total_stations > 0 else 0
        })

    daily_stats_df = pd.DataFrame(daily_stats)
    print(f"每日冷空气站点统计完成，共 {len(daily_stats_df)} 天")
    return daily_stats_df

def identify_regional_cold_air_processes(daily_stats_df):
    """
    识别区域性冷空气过程
    基于每日冷空气站点统计，按照区域冷空气判定标准识别过程
    """
    print("识别区域性冷空气过程...")

    # 计算20%阈值（中等及以上强度冷空气站点数）
    total_stations = 156  # 根据文件名推断的站点总数
    threshold_20_percent = total_stations * 0.2

    # 标记满足区域冷空气条件的日期（≥20%站点出现中等及以上冷空气）
    daily_stats_df['满足区域条件'] = (daily_stats_df['总影响站点数'] >= threshold_20_percent)

    # 识别连续的区域冷空气过程
    regional_processes = []
    current_process = None

    for _, row in daily_stats_df.iterrows():
        if row['满足区域条件']:
            if current_process is None:
                # 开始新的区域冷空气过程
                current_process = {
                    '开始日期': row['日期'],
                    '结束日期': row['日期'],
                    '过程数据': [row],
                    '持续天数': 1
                }
            else:
                # 继续当前过程
                current_process['结束日期'] = row['日期']
                current_process['过程数据'].append(row)
                current_process['持续天数'] += 1
        else:
            if current_process is not None:
                # 结束当前过程（如果持续≥2天）
                if current_process['持续天数'] >= 2:
                    regional_processes.append(current_process)
                current_process = None

    # 处理最后一个过程
    if current_process is not None and current_process['持续天数'] >= 2:
        regional_processes.append(current_process)

    # 应用过程分割规则：站点数先减少后增加时分割过程
    final_processes = []
    for process in regional_processes:
        split_processes = split_process_by_station_count(process)
        final_processes.extend(split_processes)

    print(f"识别出 {len(final_processes)} 个区域性冷空气过程")
    return final_processes

def split_process_by_station_count(process):
    """
    根据站点数变化规律分割区域冷空气过程
    如果站点数先减少后增加，则分割为多个过程
    """
    process_data = process['过程数据']
    if len(process_data) <= 2:
        return [process]

    # 分析站点数变化趋势
    station_counts = [day['总影响站点数'] for day in process_data]
    split_points = []

    # 寻找先减少后增加的转折点
    for i in range(1, len(station_counts) - 1):
        # 检查是否存在先减少后增加的模式
        if i > 0 and station_counts[i] < station_counts[i-1] and station_counts[i+1] > station_counts[i]:
            split_points.append(i)

    if not split_points:
        return [process]

    # 根据分割点创建多个过程
    split_processes = []
    start_idx = 0

    for split_idx in split_points:
        # 前一个过程结束于分割点
        if start_idx <= split_idx:
            split_process = {
                '开始日期': process_data[start_idx]['日期'],
                '结束日期': process_data[split_idx]['日期'],
                '过程数据': process_data[start_idx:split_idx+1],
                '持续天数': split_idx - start_idx + 1
            }
            split_processes.append(split_process)

        # 下一个过程从分割点后一天开始
        start_idx = split_idx + 1

    # 添加最后一个过程
    if start_idx < len(process_data):
        split_process = {
            '开始日期': process_data[start_idx]['日期'],
            '结束日期': process_data[-1]['日期'],
            '过程数据': process_data[start_idx:],
            '持续天数': len(process_data) - start_idx
        }
        split_processes.append(split_process)

    return split_processes

def calculate_station_max_levels_in_process(process, process_df):
    """
    计算某个区域冷空气过程中每个站点能达到的最高等级
    """
    start_date = process['开始日期']
    end_date = process['结束日期']

    # 找到在该过程时间范围内的所有单站冷空气过程
    station_max_levels = {}

    for _, row in process_df.iterrows():
        # 检查单站过程是否与区域过程时间重叠
        station_start = row['开始时间']
        station_end = row['结束时间']

        # 判断时间重叠
        if (station_start <= end_date and station_end >= start_date):
            station_id = row['站号']
            level = row['cold_air_level']

            # 记录该站点在此过程中的最高等级
            if station_id not in station_max_levels or level > station_max_levels[station_id]:
                station_max_levels[station_id] = level

    # 统计各等级站点数
    N1 = sum(1 for level in station_max_levels.values() if level == 1)  # 中等强度
    N2 = sum(1 for level in station_max_levels.values() if level == 2)  # 强冷空气
    N3 = sum(1 for level in station_max_levels.values() if level == 3)  # 寒潮

    return N1, N2, N3

def calculate_intensity_index(N1, N2, N3):
    """
    计算区域冷空气过程强度指数
    I = (3*N3 + 2*N2 + N1) / (N3 + N2 + N1)
    """
    total_stations = N1 + N2 + N3
    if total_stations == 0:
        return 0

    intensity_index = (3 * N3 + 2 * N2 + N1) / total_stations
    return round(intensity_index, 3)

def determine_intensity_level_by_index(intensity_index):
    """
    根据强度指数确定强度等级
    """
    if intensity_index >= 1.95:
        return '寒潮过程'
    elif intensity_index >= 1.7:
        return '强冷空气过程'
    elif intensity_index >= 1.0:
        return '中等强度冷空气过程'
    else:
        return '无冷空气过程'

def calculate_comprehensive_intensity_index(intensity_index, N1, N2, N3, total_stations=156):
    """
    计算综合强度指数
    M = I × √((N3 + N2 + N1) / N)
    """
    affected_stations = N1 + N2 + N3
    if affected_stations == 0 or total_stations == 0:
        return 0

    comprehensive_index = intensity_index * np.sqrt(affected_stations / total_stations)
    return round(comprehensive_index, 3)

def analyze_regional_processes(regional_processes, process_df):
    """
    分析区域性冷空气过程特征（包含强度指数计算）
    """
    print("分析区域性冷空气过程特征...")

    process_summary = []
    total_stations = 156  # 监测区域内总站点数

    for i, process in enumerate(regional_processes, 1):
        process_data = process['过程数据']

        # 计算过程统计信息
        max_affected_stations = max([day['总影响站点数'] for day in process_data])
        max_affected_ratio = max([day['影响站点比例'] for day in process_data])
        avg_affected_stations = round(sum([day['总影响站点数'] for day in process_data]) / len(process_data), 1)

        # 计算该过程中各站点的最高等级
        N1, N2, N3 = calculate_station_max_levels_in_process(process, process_df)

        # 计算强度指数
        intensity_index = calculate_intensity_index(N1, N2, N3)

        # 根据强度指数确定等级
        intensity_level = determine_intensity_level_by_index(intensity_index)

        # 计算综合强度指数
        comprehensive_index = calculate_comprehensive_intensity_index(intensity_index, N1, N2, N3, total_stations)

        process_summary.append({
            '过程编号': i,
            '开始日期': process['开始日期'].strftime('%Y-%m-%d'),
            '结束日期': process['结束日期'].strftime('%Y-%m-%d'),
            '持续天数': process['持续天数'],
            '最大影响站点数': max_affected_stations,
            '最大影响比例(%)': max_affected_ratio,
            '平均影响站点数': avg_affected_stations,
            '中等强度站点数(N1)': N1,
            '强冷空气站点数(N2)': N2,
            '寒潮站点数(N3)': N3,
            '强度指数(I)': intensity_index,
            '强度等级': intensity_level,
            '综合强度指数(M)': comprehensive_index
        })

    return pd.DataFrame(process_summary)

def export_results(process_df, daily_stats_df=None, regional_summary_df=None, output_dir=None):
    """
    导出单站和区域冷空气过程结果
    """
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(file_path), "results")

    os.makedirs(output_dir, exist_ok=True)

    # 导出单站冷空气过程
    output_file = os.path.join(output_dir, "单站冷空气过程.csv")
    result_df = process_df.drop(columns=['过程数据'])
    result_df['开始时间'] = result_df['开始时间'].dt.strftime('%Y-%m-%d')
    result_df['结束时间'] = result_df['结束时间'].dt.strftime('%Y-%m-%d')
    result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"单站冷空气过程结果已导出至: {output_file}")

    # 导出每日冷空气站点统计
    if daily_stats_df is not None:
        daily_output_file = os.path.join(output_dir, "每日冷空气站点统计.csv")
        daily_export_df = daily_stats_df.copy()
        daily_export_df['日期'] = daily_export_df['日期'].dt.strftime('%Y-%m-%d')
        daily_export_df.to_csv(daily_output_file, index=False, encoding='utf-8-sig')
        print(f"每日冷空气站点统计已导出至: {daily_output_file}")

    # 导出区域冷空气过程汇总
    if regional_summary_df is not None and not regional_summary_df.empty:
        regional_output_file = os.path.join(output_dir, "区域冷空气过程汇总.csv")
        regional_summary_df.to_csv(regional_output_file, index=False, encoding='utf-8-sig')
        print(f"区域冷空气过程汇总已导出至: {regional_output_file}")

    return output_file

def main():
    """
    主函数
    """
    start_time = time.time()
    print("开始执行冷空气过程分析...")

    # 读取数据
    step_start = time.time()
    df = read_data(file_path)
    print(f"数据读取耗时: {time.time() - step_start:.2f}秒")

    # 寻找连续降温过程
    step_start = time.time()
    process_df = find_continuous_cooling_processes(df)
    print(f"连续降温过程识别耗时: {time.time() - step_start:.2f}秒")

    # 计算降温幅度
    step_start = time.time()
    process_df = calculate_temperature_drops_for_processes(process_df)
    print(f"降温幅度计算耗时: {time.time() - step_start:.2f}秒")

    # 判断单站冷空气等级
    step_start = time.time()
    process_df = determine_cold_air_level_for_processes(process_df)
    print(f"单站冷空气等级判断耗时: {time.time() - step_start:.2f}秒")

    # 显示单站冷空气过程结果
    if not process_df.empty:
        print("\n单站冷空气过程汇总:")
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        result_df = process_df.drop(columns=['过程数据'])
        result_df['开始时间'] = result_df['开始时间'].dt.strftime('%Y-%m-%d')
        result_df['结束时间'] = result_df['结束时间'].dt.strftime('%Y-%m-%d')

        # 统计信息
        print(f"\n单站冷空气过程统计信息:")
        print(f"总过程数: {len(process_df)}")
        print(f"涉及站点数: {process_df['站号'].nunique()}")
        print("\n各强度等级过程数:")
        intensity_counts = process_df['冷空气强度'].value_counts()
        for intensity, count in intensity_counts.items():
            print(f"  {intensity}: {count}次")

        # 创建每日冷空气站点统计
        step_start = time.time()
        daily_stats_df = create_daily_cold_air_statistics(df, process_df)
        print(f"每日冷空气站点统计耗时: {time.time() - step_start:.2f}秒")

        # 识别区域性冷空气过程
        step_start = time.time()
        regional_processes = identify_regional_cold_air_processes(daily_stats_df)
        print(f"区域性冷空气过程识别耗时: {time.time() - step_start:.2f}秒")

        # 分析区域性冷空气过程
        if regional_processes:
            step_start = time.time()
            regional_summary_df = analyze_regional_processes(regional_processes, process_df)
            print(f"区域性冷空气过程分析耗时: {time.time() - step_start:.2f}秒")

            print(f"\n区域性冷空气过程汇总:")
            print(regional_summary_df.to_string(index=False))

            print(f"\n区域性冷空气过程统计信息:")
            print(f"总过程数: {len(regional_summary_df)}")
            print("\n各强度等级过程数:")
            regional_intensity_counts = regional_summary_df['强度等级'].value_counts()
            for intensity, count in regional_intensity_counts.items():
                print(f"  {intensity}: {count}次")
        else:
            regional_summary_df = pd.DataFrame()
            print("\n未识别出区域性冷空气过程")

        # 导出所有结果
        step_start = time.time()
        export_results(process_df, daily_stats_df, regional_summary_df)
        print(f"结果导出耗时: {time.time() - step_start:.2f}秒")

        total_time = time.time() - start_time
        print(f"\n总执行时间: {total_time:.2f}秒")

    else:
        print("未识别出单站冷空气过程")

if __name__ == "__main__":
    main()