
import requests
import json
 
# 调用coze api函数
def call_coze_api(input_text):
    # 个人访问令牌
    personal_access_token = "pat_VcyXayJ4YEmPyBwus8L1E4RlrcLzOJiPcWmbXg26gqDjtZYCCrHVRTqnxQ7634Db"
 
    # bot_ai
    bot_id = "7472302303190843401"
 
    # 构造请求头
    headers = {
        "Authorization": f'Bearer {personal_access_token}',
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Connection": "keep-alive"
    }
 
    # 构造请求体
    payload = {
        "bot_id": bot_id,
        "user": "换成自己用户名",
        "query": input_text,
        "stream": False
    }
 
    # 发送POST请求
    response = requests.post(
        "https://api.coze.cn/open_api/v2/chat",
        headers=headers, 
        json=payload)
 
    # 打印响应
    if response.ok:
        print("请求成功，返回数据：")
 
        response_data = response.json()
        print(f"Conversation ID: {response_data['conversation_id']}")
        print(f"Status: {response_data['msg']}")
 
        for message in response_data['messages']:
            print("-" * 40)
            print(f"Message: {message['content']}")
    else:
        print("请求失败，状态码：", response.status_code)
        print("错误信息：", response.text)

if __name__ == "__main__":
    call_coze_api("你好")