import pandas as pd

# 读取Excel文件
df1 = pd.read_excel(r'D:\python\monitoring\data\station\station_info.xlsx')
df2 = pd.read_excel(r'D:\python\monitoring\data\station\original_data\中国地面逐小时全要素数据(国家站)(SURF_CHN_MUL_HOR_N).xlsx')

# 获取df1中的station_id
station_ids = df1['station_id'].astype(str)

# 使用station_ids筛选df2中的数据
df2_filtered = df2[(df2['区站号(字符)'].astype(str)).isin(station_ids)]

df2_filtered.to_excel(r'D:\python\monitoring\data\station\SURF_CHN_MUL_HOR_N.xlsx', index=False)
