import pandas as pd
import numpy as np
from scipy.stats import norm

def normal_mle_fit(data):
    """
    使用最大似然估计拟合正态分布参数
    :param data: 年最大降水序列
    :return: 均值mu, 标准差sigma
    """
    mu = np.mean(data)
    sigma = np.std(data)
    return mu, sigma

def calculate_normal_return_level(mu, sigma, return_period):
    """
    计算给定重现期的降水极值
    :param mu: 均值
    :param sigma: 标准差
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 计算对应的分位数
    return norm.ppf(non_exceedance_prob, loc=mu, scale=sigma)


# 示例使用
if __name__ == "__main__":
    # 读取数据
    file_path = r'D:\python\Return_period_extremum\test_data\1991~2020年德阳降水年际变化统计图.xlsx'
    T = pd.read_excel(file_path)

    year = T['年份']
    precip = T['降水']
    
    # 拟合正态分布
    mu, sigma = normal_mle_fit(precip)
    print(f"Estimated parameters: mu = {mu:.2f}, sigma = {sigma:.2f}")
    
    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\End_Result\normal.txt", "w", encoding='utf-8') as file:
        file.write(f"位置参数 = {mu:.3f}, 尺度参数 = {sigma:.3f} \n")
        for rp in return_periods:
            rl = calculate_normal_return_level(mu, sigma, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}a : {rl:.1f} \n")
    
