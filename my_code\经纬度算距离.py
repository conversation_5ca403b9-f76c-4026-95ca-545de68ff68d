def distance(lat1, lon1, lat2, lon2):
    import math
    EARTH_RADIUS = 6371.0
    def to_radians(degree):
        return degree * math.pi / 180.0
    def calculate_distance(lat1, lon1, lat2, lon2):
        d_lat = to_radians(lat2 - lat1)
        d_lon = to_radians(lon2 - lon1)
        a = math.sin(d_lat / 2) ** 2 + math.cos(to_radians(lat1)) * math.cos(to_radians(lat2)) * math.sin(d_lon / 2) ** 2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        return EARTH_RADIUS * c
    return calculate_distance(lat1, lon1, lat2, lon2)


distance1 = distance(30.6097, 106.1224, 31.53, 106.47)

print(f"Distance between the two coordinates: {distance1} km")
