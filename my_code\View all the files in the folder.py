import xarray as xr
import os  # 新增导入os模块

# 设置文件夹路径
folder_path = r'E:\Data_wrangling\data\Forcast\CPSv3'

# 遍历文件夹中的所有文件
for filename in os.listdir(folder_path):
    # 构建完整文件路径
    file_path = os.path.join(folder_path, filename)
    
    # 打开NetCDF文件
    file1 = xr.open_dataset(file_path)
    
    # 查看文件内容
    print(f"\n正在处理文件: {filename}")
    print(file1)
    
    # 查看文件的变量单位
    for var_name in file1.variables:
        print(f"变量 {var_name} 的单位是: {file1[var_name].attrs.get('units', '无单位信息')}")