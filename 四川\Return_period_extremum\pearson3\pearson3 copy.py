import pandas as pd
import numpy as np
from scipy.stats import pearson3


def pearson3_mle_fit(data):
    """
    使用最大似然估计拟合耿贝尔分布参数
    """
    params = pearson3.fit(data)
    return params[0], params[1], params[2]


def calculate_return_level(shape, loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param shape: 形状参数
    :param loc: 位置参数
    :param scale: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return pearson3.ppf(non_exceedance_prob, shape, loc=loc, scale=scale)


# 示例使用
if __name__ == "__main__":
    # 读取数据

    file_path = r'D:\python\Return_period_extremum\Bureau_data\56247_1440min.txt'
    T = pd.read_csv(file_path,sep='\t',engine='python').values

    precip = T[:,1]

    # 拟合耿贝尔分布
    shape, loc, scale = pearson3_mle_fit(precip)
    print(f"Estimated parameters: shape = {shape:.3f}, loc = {loc:.3f}, scale = {scale:.3f}")

    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\Bureau_data\End_result\pearson3.txt", "w", encoding='utf-8') as file:
        file.write(f"alpha = {shape:.3f}, beta = {scale:.3f}, gamma = {loc:.3f} \n")
        for rp in return_periods:
            rl = calculate_return_level(shape, loc, scale, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")