import numpy as np
import pandas as pd
from collections import Counter

def read_data():
    """读取并预处理数据"""
    # 从Excel文件读取数据
    data = pd.read_excel(r'D:\python\High_impact_weather\data\中国地面日值数据(SURF_CHN_MUL_DAY).xlsx')
    
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']

    data = data[data['年'] == 2024]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d')
    
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    
    # 按日期排序
    data.sort_index(inplace=True)
    
    return data

def calculate_wind_weather(method):
    """
    计算大风天气天数(风速 ≥ 17.2 m/s)并生成统计表
    
    参数:
    method: 整数 (0: 平均10分钟风速, 1: 最大风速, 2: 极大风速)
    
    返回:
    tuple: (总天数, 月份数组)
    """
    data = read_data()
    
    # 根据方法选择风速列
    wind_columns = ['平均10min风速', '最大风速', '极大风速']
    wind_data = data[wind_columns[method]]
    wind_data = wind_data.replace(999999, 0)
    
    # 创建大风天气的布尔掩码(风速≥17.2m/s)
    high_wind_mask = wind_data >= 17.2
    
    # 获取所有符合条件的日期
    wind_dates = data[high_wind_mask].index
    
    # 统计总天数
    total_days = len(wind_dates)
    
    # 统计月份分布
    month_counts = Counter(wind_dates.month)
    
    # 生成完整月份数据（1-12月）
    all_months = range(1, 13)
    full_month_data = {f"{m}月": month_counts.get(m, 0) for m in all_months}
    
    # 生成统计表格
    generate_statistics_table(full_month_data, wind_dates)
    
    return total_days, wind_dates.month.values

def generate_statistics_table(month_data, wind_dates):
    """生成统计表格并保存为CSV"""
    # 创建表格数据
    table_data = []
    for idx, (month_str, count) in enumerate(month_data.items(), start=1):
        table_data.append({
            "序号": idx,
            "月份": month_str,
            "大风日数": count
        })
    
    # 创建DataFrame
    df = pd.DataFrame(table_data)
    
    # 获取数据时间范围
    start_year = wind_dates.year.min() if not wind_dates.empty else "N/A"
    end_year = wind_dates.year.max() if not wind_dates.empty else "N/A"
    
    # 设置表格标题
    title = f"{start_year}-{end_year}年1-12月大风>=17.2m/s月变化统计表"
    
    # 保存为CSV文件
    filename = f"大风统计_{start_year}-{end_year}.csv"
    #df.to_csv(filename, index=False, encoding="utf-8-sig")
    
    # 打印结果
    print("\n" + "="*50)
    print(f"成功生成统计表格：{filename}")
    print("="*50)
    print(df.to_string(index=False))
    print("="*50)

if __name__ == "__main__":
    # 示例使用（使用极大风速统计）
    total_days, months = calculate_wind_weather(2)
    print(f"\n大风总日数：{total_days}")
    print(f"大风发生月份：{months}")