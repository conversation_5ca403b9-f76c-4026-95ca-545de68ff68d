PRE_file_path = r'D:\python\monitoring\data\reanalysis\CRA40_DAILY-PRE-NUL-20241231000000-OVL-0.25.nc'
T_file_path = r'D:\python\monitoring\data\reanalysis\CRA40_DAILY-T-NUL-20241231000000-OVL-0.25.nc'
PRE_data = xr.open_dataset(PRE_file_path).sel(lon=slice(95, 109), lat=slice(25.3, 34.88)).sel(time=slice('2024-04-01', '2024-06-30'))
T_data = xr.open_dataset(T_file_path).sel(lon=slice(95, 109), lat=slice(25.3, 34.88)).sel(time=slice('2024-03-01', '2024-06-30'))

preci = PRE_data['pre'].sum(dim='time')
temp = T_data['t2m'].mean(dim='time')

# 打开站点信息文件
station_info = pd.read_excel(r'D:\python\monitoring\data\station\station_info.xlsx')
station_ids = station_info['station_id'].astype(str)
# 遍历站点 ID 列表，获取每个站点的经纬度信息
station_lon_lat = []
for station_id in station_ids:
    station_row = station_info[station_info['station_id'].astype(str) == station_id]
    lon = station_row['lon'].values[0]
    lat = station_row['lat'].values[0]
    station_lon_lat.append((station_id, lon, lat))