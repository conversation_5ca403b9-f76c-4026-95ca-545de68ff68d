#In[0]: 数据加载
import pandas as pd

file_path = r'D:\python\四川\monitoring\Climate events\drought\data\T_DPW_CP_RINDEX_202505201004.xlsx'
data = pd.read_excel(file_path)
data = data.fillna(0)
station_ids = data['V01000'].unique()  
data['V04000'] = pd.to_datetime(data['V04000'], format='%Y%m%d')
data = data[data['V04000'] > '2023-12-31']


# %%
# 判断无旱、轻旱、中旱、重旱、特旱(返回整数分类)
def classify_drought_intensity(mci_value):
    if -0.5 < mci_value:
        return 0
    elif -1.0 < mci_value <= -0.5:
        return 1
    elif -1.5 < mci_value <= -1.0:
        return 2
    elif -2.0 < mci_value <= -1.5:
        return 3
    else:
        return 4
    
# 应用函数到 MCI 列
data['MCI_Class'] = data['MCI'].apply(classify_drought_intensity).astype(int)



#%% --------------------区域过程--------------------
import numpy as np
from tqdm import tqdm

station_info = pd.read_excel(r'D:\python\四川\monitoring\Climate events\drought\data\station_info.xlsx')
stations = station_info[['站号', '纬度', '经度']].values

# 经纬度计算距离
def distance(lat1, lon1, lat2, lon2):
    import math
    EARTH_RADIUS = 6371.0
    def to_radians(degree):
        return degree * math.pi / 180.0
    def calculate_distance(lat1, lon1, lat2, lon2):
        d_lat = to_radians(lat2 - lat1)
        d_lon = to_radians(lon2 - lon1)
        a = math.sin(d_lat / 2) ** 2 + math.cos(to_radians(lat1)) * math.cos(to_radians(lat2)) * math.sin(d_lon / 2) ** 2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        return EARTH_RADIUS * c
    return calculate_distance(lat1, lon1, lat2, lon2)


# 计算所有站点之间的距离矩阵
print("正在计算站点间距离...")
distance_matrix = np.zeros((len(stations), len(stations)))
for i, (id1, lat1, lon1) in tqdm(enumerate(stations)):
    for j, (id2, lat2, lon2) in enumerate(stations):
        if i < j:
            dist = distance(lat1, lon1, lat2, lon2)
            distance_matrix[i][j] = dist
            distance_matrix[j][i] = dist

# 构建相邻站点字典（200km）
adjacent_stations = {}
threshold = 200 
for i, (id1, _, _) in enumerate(stations):
    adjacent = stations[distance_matrix[i] <= threshold, 0].tolist()
    adjacent.remove(id1)
    adjacent_stations[id1] = adjacent

print("相邻站点构建完成。")



# 2. 构建监测区域（所有站点组成一个大区域）
all_stations = station_info['站号'].unique().tolist()
print(f"监测区域包含{len(all_stations)}个站点。")

# 3. 预处理干旱数据
print('预处理干旱数据...')
data_regional = data[['V01000', 'V04000', 'MCI', 'MCI_Class']].copy()
data_regional['中旱及以上'] = data_regional['MCI_Class'] >= 2
print("数据预处理完成。")

# 4. 计算每日区域干旱情况（修复版）
def find_drought_clusters(drought_stations, adjacent_stations):
    """使用深度优先搜索找到所有连通的干旱站点群组"""
    visited = set()
    clusters = []

    def dfs(station, current_cluster):
        """深度优先搜索，找到连通的干旱站点"""
        if station in visited or station not in drought_stations:
            return
        visited.add(station)
        current_cluster.add(station)

        # 遍历所有相邻站点
        for neighbor in adjacent_stations.get(station, []):
            if neighbor in drought_stations and neighbor not in visited:
                dfs(neighbor, current_cluster)

    # 对每个未访问的干旱站点进行DFS
    for station in drought_stations:
        if station not in visited:
            cluster = set()
            dfs(station, cluster)
            if len(cluster) >= 2:  # 至少2个相邻站点才算群组
                clusters.append(list(cluster))
            # 注意：孤立的单个干旱站点不会被加入clusters

    return clusters

print("开始计算每日区域干旱情况...")
daily_stats = []
for date in tqdm(data_regional['V04000'].unique()):
    daily_data = data_regional[data_regional['V04000'] == date]

    # 获取当日有中旱以上的站点
    drought_stations = daily_data[daily_data['中旱及以上']]['V01000'].unique().tolist()

    # 找到所有连通的干旱站点群组
    drought_clusters = find_drought_clusters(drought_stations, adjacent_stations)

    # 选择最大的群组作为代表
    if drought_clusters:
        best_group = max(drought_clusters, key=len)
        cluster_count = len(drought_clusters)
    else:
        best_group = []
        cluster_count = 0

    # 判断是否达到5%阈值
    is_regional_day = len(best_group) / len(all_stations) >= 0.05

    daily_stats.append({
        '日期': date,
        '干旱站点数': len(drought_stations),
        '有效群组站点数': len(best_group),
        '群组总数': cluster_count,
        '是否区域干旱日': is_regional_day,
        '干旱站点列表': best_group,
        '所有群组': drought_clusters  # 保存所有群组信息，便于调试
    })

daily_df = pd.DataFrame(daily_stats)
print("每日区域干旱情况计算完成。")
print(f"总共处理了 {len(daily_df)} 天的数据")
print(f"其中区域干旱日有 {daily_df['是否区域干旱日'].sum()} 天")



# 5. 区域性干旱过程识别
# 先计算每日必要指标
daily_df['站点比例'] = daily_df['有效群组站点数'] / len(all_stations)
daily_df['前一天站点列表'] = daily_df['干旱站点列表'].shift(1)

# 计算每日站点重合率
def calculate_overlap(row):
    if not row['前一天站点列表'] or len(row['前一天站点列表']) == 0:
        return 0.0
    # 添加当前站点列表空值检查
    if not row['干旱站点列表'] or len(row['干旱站点列表']) == 0:
        return 0.0
    prev = set(row['前一天站点列表'])
    current = set(row['干旱站点列表'])
    return len(prev & current) / len(current)

daily_df['站点重合率'] = daily_df.apply(calculate_overlap, axis=1)

# 过程识别参数
END_CONDITION_DAYS = 5        
SITE_PERCENT_THRESH = 0.05   
OVERLAP_THRESH = 0.5
START_CONSECUTIVE_DAYS = 15    

processes = []
current_process = None
end_condition_counter = 0
start_condition_counter = 0

for i in tqdm(range(len(daily_df))):
    row = daily_df.iloc[i]
    
    # 过程开始检测
    if current_process is None:
        if row['是否区域干旱日'] and row['站点重合率'] >= OVERLAP_THRESH:
            start_condition_counter += 1
            if start_condition_counter >= START_CONSECUTIVE_DAYS:
                start_date_idx = max(0, i - (START_CONSECUTIVE_DAYS-1))
                start_date = daily_df.iloc[start_date_idx]['日期']
            # 开始新过程
                current_process = {
                    'start_date': start_date,
                    'end_date': start_date,
                    'consecutive_days': 1,
                    'active_days': 1,
                    'current_stations': set(row['干旱站点列表'])
                }
        else:
            start_condition_counter = 0
        continue
    
    # 过程持续监测
    else:
        current_process['end_date'] = row['日期']
        current_process['consecutive_days'] += 1
        
        # 检测结束条件
        condition1 = row['站点比例'] < SITE_PERCENT_THRESH
        condition2 = row['站点重合率'] < OVERLAP_THRESH
        
        # 满足任意结束条件时计数
        if condition1 or condition2:
            end_condition_counter += 1
        else:
            end_condition_counter = 0  # 重置计数器
            
        # 达到结束条件持续天数
        if end_condition_counter >= END_CONDITION_DAYS:
            # 确定过程结束日为满足条件的前一天
            end_date_index = max(0, i - END_CONDITION_DAYS)
            end_date = daily_df.iloc[end_date_index]['日期']
            
            # 记录有效过程（持续时间需包含有效时段）
            if current_process['consecutive_days'] >= 15:
                processes.append({
                    'start_date': current_process['start_date'].strftime('%Y%m%d'),
                    'end_date': end_date.strftime('%Y%m%d'),
                    'duration': (end_date - current_process['start_date']).days + 1,
                    'trigger_condition': '站点比例不足' if condition1 else '重合率不足'
                })
            
            # 重置状态
            current_process = None
            end_condition_counter = 0

# 处理最后一个未结束的过程
if current_process and current_process['consecutive_days'] >= 15:
    processes.append({
        'start_date': current_process['start_date'].strftime('%Y%m%d'),
        'end_date': current_process['end_date'].strftime('%Y%m%d'),
        'duration': (current_process['end_date'] - current_process['start_date']).days + 1,
        'trigger_condition': '自然结束'
    })

#  6. 过程强度计算
def calculate_process_intensity(start_date, end_date, stations):
    mask = (data_regional['V04000'] >= start_date) & \
           (data_regional['V04000'] <= end_date)
    
    # 影响范围（逐日达到中旱及以上站点数的平均值）
    A = daily_df[(daily_df['日期'] >= start_date) & 
                (daily_df['日期'] <= end_date)]['干旱站点数'].mean()
    
    # 干旱强度（MCI绝对值平均）
    process_data = data_regional[mask & data_regional['V01000'].isin(stations)]
    I = process_data['MCI'].abs().mean()
    
    # 持续时间
    T = (end_date - start_date).days + 1
    
    return I * np.sqrt(A/T) * np.sqrt(T)  # 强度Z

# 为每个过程补充完整信息
for process in processes:
    start = pd.to_datetime(process['start_date'])
    end = pd.to_datetime(process['end_date'])
    
    # 获取过程期间所有干旱站点
    mask = (daily_df['日期'] >= start) & (daily_df['日期'] <= end)
    all_stations = set()
    daily_df[mask]['干旱站点列表'].apply(lambda x: all_stations.update(x))
    
    process['影响站数'] = len(all_stations)
    process['强度Z'] = calculate_process_intensity(start, end, all_stations)


# 7. 结果输出
output_columns = [
    'start_date', 'end_date', 'duration', '影响站数','强度Z'
]
df_region_process = pd.DataFrame(processes)[output_columns]

df_region_process.columns = [
    '开始日期', '结束日期', '持续天数', '影响站点数',
    '过程强度Z'
]

# 保存结果
df_region_process.to_csv(
    r'D:\python\四川\monitoring\Climate events\drought\data\四川省区域性干旱过程统计表.csv',
    index=False
)

print("区域性干旱过程分析完成！生成过程记录{}条".format(len(df_region_process)))
print(df_region_process)

