from matplotlib.colors import ListedColormap
import rasterio


with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\2022年危险性等级.tif') as src:
    data = src.read(1)  
    meta = src.meta     
    print(meta)
    # 处理-999值
    data[data == -999] = 0

    # 绘图
    import matplotlib.pyplot as plt
    # 定义风险等级对应的颜色
    #colors = ['#FFFF00', '#FFB226', '#FF7333', '#CC2600', '#A31447']
    #cmap = ListedColormap(colors)
    plt.imshow(data, cmap='jet')
    plt.colorbar()
    plt.title('Hazard Level')
    #plt.savefig(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\photo\Hazard Level system.png', dpi=400)
    plt.show()