import struct
import numpy as np
from netCDF4 import Dataset
import os

def parse_noaa1b_to_nc(bin_path, nc_path):
    record_len = 22016
    channel_count = 5
    pix_per_channel = 2048
    scanline_data = []
    utc_times = []

    with open(bin_path, 'rb') as f:
        # 读取主记录（文件头）
        main_record = f.read(record_len)
        # 计算总扫描线条数
        file_size = os.path.getsize(bin_path)
        nscan = (file_size - record_len) // record_len

        # 预分配数组（节省内存，可选）
        ch_data = np.zeros((nscan, channel_count, pix_per_channel), dtype=np.int16)
        utc_time_arr = np.zeros(nscan, dtype=np.uint32)

        # 预分配经纬度数组
        lon_data = np.zeros((nscan, pix_per_channel), dtype=np.float32)
        lat_data = np.zeros((nscan, pix_per_channel), dtype=np.float32)

        for i in range(nscan):
            chunk = f.read(record_len)
            if not chunk or len(chunk) < record_len:
                break

            # 9-12字节：UTC时间（秒）
            utc_time = struct.unpack('>I', chunk[8:12])[0]
            try:
                utc_time_arr[i] = utc_time
            except OverflowError:
                print(f"警告：第{i}条扫描线UTC时间溢出，值为{utc_time}")
                utc_time_arr[i] = 0

            # 49字节起，连续5个通道，每通道2048点，每点2字节（I*2）
            offset = 48  # 49字节（索引48）
            for ch in range(channel_count):
                ch_data[i, ch, :] = np.frombuffer(chunk[offset:offset+2*pix_per_channel], dtype='>i2')
                offset += 2*pix_per_channel

            # 解析地定位点经纬度（329-634字节，索引328-633，每点6字节，经度2+纬度2+高程2）
            geo_offset = 328
            lons_51 = np.zeros(51, dtype=np.float32)
            lats_51 = np.zeros(51, dtype=np.float32)
            for k in range(51):
                lons_51[k] = struct.unpack('>h', chunk[geo_offset:geo_offset+2])[0] / 100.0
                lats_51[k] = struct.unpack('>h', chunk[geo_offset+2:geo_offset+4])[0] / 100.0
                geo_offset += 6  # 跳过高度2字节
            # 插值到2048像元
            lon_data[i, :] = np.interp(np.linspace(0, 50, pix_per_channel), np.arange(51), lons_51)
            lat_data[i, :] = np.interp(np.linspace(0, 50, pix_per_channel), np.arange(51), lats_51)

    # 写入NetCDF文件
    with Dataset(nc_path, 'w', format='NETCDF4') as nc:
        nc.createDimension('scanline', nscan)
        nc.createDimension('channel', channel_count)
        nc.createDimension('pix', pix_per_channel)

        times = nc.createVariable('utc_time', 'i4', ('scanline',))
        ch_brightness = nc.createVariable('brightness', 'i4', ('scanline', 'channel', 'pix'), zlib=True)
        ch_brightness.units = 'counts'
        times.units = 'seconds since 1970-01-01 00:00:00'
        times.long_name = 'scanline UTC time'

        times[:] = utc_time_arr
        ch_brightness[:, :, :] = ch_data
        # 写入经纬度变量
        lon_var = nc.createVariable('longitude', 'f4', ('scanline', 'pix'), zlib=True)
        lat_var = nc.createVariable('latitude', 'f4', ('scanline', 'pix'), zlib=True)
        lon_var.units = 'degrees_east'
        lat_var.units = 'degrees_north'
        lon_var[:, :] = lon_data
        lat_var[:, :] = lat_data

    print(f"保存完成：{nc_path}")

if __name__ == '__main__':
    parse_noaa1b_to_nc(
        r'd:\python\wxsj\data\NOAA19_AVHRR_2025_01_07_21_45_A_G.L1b',
        r'd:\python\wxsj\data\NOAA19_AVHRR_2025_01_07_21_45_A_G.nc'
    )
