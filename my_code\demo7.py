import xarray as xr
import numpy as np
import glob
import os

# 基础路径
base_input_folder = r'D:\python\data\precipitation_total\precipitation_daily'
yearly_output_folder = r'D:\python\data\precipitation_total\precipitation_yearly'  # 只保留年度数据输出目录

# 确保年度输出目录存在
os.makedirs(yearly_output_folder, exist_ok=True)

# 年份循环
for year in range(1997, 2021):
    print(f"\nProcessing year {year}...")
    
    # 构建输入文件夹路径
    input_folder = os.path.join(base_input_folder, str(year))
    
    # 检查输入文件夹
    if not os.path.exists(input_folder):
        print(f"Warning: Input folder for year {year} does not exist, skipping...")
        continue
    
    # 获取该年份所有nc文件
    nc_files = glob.glob(os.path.join(input_folder, '*.nc'))
    
    if not nc_files:
        print(f"No nc files found for year {year}, skipping...")
        continue

    processed_datasets = []  # 存储处理后的数据集
        
    for file_path in nc_files:
        try:
            # 打开并预处理数据集
            dataset = xr.open_dataset(file_path)
            dataset_reduced = dataset.drop_vars(['lat_bounds', 'lon_bounds', 'time_bounds'])
            
            # 创建新的经纬度网格（0.1度分辨率）
            new_lats = np.arange(dataset_reduced.latitude.min(), 
                               dataset_reduced.latitude.max() + 0.1, 0.1)
            new_lons = np.arange(dataset_reduced.longitude.min(), 
                               dataset_reduced.longitude.max() + 0.1, 0.1)
            
            # 进行插值
            interpolated_ds = dataset_reduced.interp(latitude=new_lats, longitude=new_lons)
            
            # 将处理后的数据集添加到列表中
            processed_datasets.append(interpolated_ds)
            
            print(f"Processed and interpolated {os.path.basename(file_path)}")
            
        except Exception as e:
            print(f"Error processing {os.path.basename(file_path)}: {str(e)}")
            continue
    
    if processed_datasets:
        try:
            # 合并该年份的所有数据集
            yearly_dataset = xr.concat(processed_datasets, dim='time')
            
            # 保存年度合并文件
            yearly_file_path = os.path.join(yearly_output_folder, f'precipitation_{year}.nc')
            yearly_dataset.to_netcdf(yearly_file_path)
            print(f"Created yearly file: {yearly_file_path}")
            
            # 清理内存
            del yearly_dataset
            del processed_datasets
            
        except Exception as e:
            print(f"Error creating yearly file for {year}: {str(e)}")

print("\nAll years have been processed and merged!")
