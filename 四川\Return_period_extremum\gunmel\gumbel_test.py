
import pandas as pd
import numpy as np
from scipy.stats import gumbel_r, kstest, chisquare,anderson


def gumbel_mle_fit(data):
    """
    使用最大似然估计拟合耿贝尔分布参数
    """
    params = gumbel_r.fit(data)
    return params[0], params[1]  # mu, beta


def calculate_return_level(mu, beta, return_period):
    """
    计算给定重现期的降水极值
    :param mu: 位置参数
    :param beta: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return gumbel_r.ppf(non_exceedance_prob, loc=mu, scale=beta)


#%%

# 示例使用
if __name__ == "__main__":
    # 读取数据
    file_path = r'D:\python\四川\Return_period_extremum\中国地面年值数据(SURF_CHN_MUL_YER)(1).xlsx'
    T = pd.read_excel(file_path)

    precip = T['极大风速']

    mu, beta = gumbel_mle_fit(precip)
    print(f"Estimated parameters: mu = {mu:.2f}, beta = {beta:.2f}")


    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100, 200, 300, 500, 550, 1000]
    for rp in return_periods:
        rl = calculate_return_level(mu, beta, rp)
        print(f"{rp}-year return level: {rl:.2f} M/s")

# %%
