import pandas as pd
import numpy as np
from tqdm import tqdm


file_path1 = r'D:\python\四川\monitoring\Climate events\cold\data\SURF_CHN_MUL_DAY.xlsx'
file_path2 = r'D:\python\四川\monitoring\Climate events\cold\data\SURF_CHN_MUL_DAY (all_station).xlsx'
file_path3 = r'D:\python\四川\monitoring\Climate events\cold\data\SURF_CHN_MUL_DAY(2024).xlsx'

data = pd.read_excel(file_path3)
data['最低气温'] = data['最低气温'].replace(999999,np.nan)

data['资料时间'] = pd.to_datetime(data['资料时间'])
data.sort_values(by='资料时间', inplace=True)

#%%

def delattr_T(data):
    result = pd.DataFrame()
    for station_id in tqdm(data['区站号(字符)'].unique()):
        station_data = data[data['区站号(字符)'] == station_id].copy()

        # 计算24小时
        station_data_t24 = station_data['最低气温'].shift(-1)
        station_data['∆T24'] = -(station_data_t24 - station_data['最低气温'])

        # 计算48小时
        station_data_t48 = station_data['最低气温'].shift(-2)
        station_data['∆T48'] = pd.concat([
            station_data_t24,
            station_data_t48], axis=1).min(axis=1) - station_data['最低气温']
        station_data['∆T48'] = -station_data['∆T48']

        # 计算72小时
        station_data_t72 = station_data['最低气温'].shift(-3)
        station_data['∆T72'] = pd.concat([
            station_data_t24,
            station_data_t48,
            station_data_t72], axis=1).min(axis=1) - station_data['最低气温']
        station_data['∆T72'] = -station_data['∆T72']

        # 合并结果
        result = pd.concat([result, station_data], ignore_index=True)

    return result

data = delattr_T(data)


# %%

def intensity_level(data):
    data['T_min_24h'] = data.groupby('区站号(字符)')['最低气温'].shift(-1)
    data['T_min_48h'] = data.groupby('区站号(字符)')['最低气温'].shift(-2)
    data['T_min_72h'] = data.groupby('区站号(字符)')['最低气温'].shift(-3)

    # 判断连续下降条件
    data['连续下降48h'] = (data['最低气温'] > data['T_min_24h']) & \
                          (data['T_min_24h'] > data['T_min_48h'])
    data['连续下降72h'] = (data['最低气温'] > data['T_min_24h']) & \
                          (data['T_min_24h'] > data['T_min_48h']) & \
                          (data['T_min_48h'] > data['T_min_72h'])

    # 初始化强度等级时指定dtype
    data['强度等级'] = pd.Series(index=data.index, dtype='object')

    # 寒潮条件判断
    mask_24h = (data['∆T24'] >= 8) & (data['T_min_24h'] <= 4)
    mask_48h = (data['∆T48'] >= 10) & (data['连续下降48h']) & (data['T_min_48h'] <= 4)
    mask_72h = (data['∆T72'] >= 12) & (data['连续下降72h']) & (data['T_min_72h'] <= 4)

    data.loc[mask_24h | mask_48h | mask_72h, '强度等级'] = '寒潮'
    data.loc[data['∆T48'] >= 8, '强度等级'] = '强冷空气'
    data.loc[data['∆T48'].between(6, 8, inclusive='left'), '强度等级'] = '中等强度冷空气'

    data.drop(['T_min_24h', 'T_min_48h', 'T_min_72h', '连续下降48h', '连续下降72h'], axis=1, inplace=True)

    return data

data = intensity_level(data)
# %%
data_56371 = data[data['区站号(字符)'] == 56371]
print(data_56371)
# %%
data_56038 = data[data['区站号(字符)'] == 56038]
print(data_56038)
# %%
