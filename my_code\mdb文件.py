import pandas as pd
import pyodbc

def read_mdb(file_path, table_name):
    """
    读取Access数据库文件
    :param file_path: mdb文件路径
    :param table_name: 要读取的表名
    :return: 返回DataFrame
    """
    try:
        # 创建连接字符串
        conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={file_path};'
        
        # 建立连接并读取数据
        with pyodbc.connect(conn_str) as conn:
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            return df
            
    except pyodbc.Error as e:
        print(f"数据库连接错误: {str(e)}")
        return None
    except Exception as e:
        print(f"读取数据时出错: {str(e)}")
        return None

def main():
    # 示例用法
    file_path = r'D:\python\data\mdb\2006.mdb'
    table_name = '2006'
    
    df = read_mdb(file_path, table_name)
    if df is not None:
        print(df.head())

if __name__ == '__main__':
    main()