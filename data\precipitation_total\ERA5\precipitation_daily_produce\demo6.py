import xarray as xr
import numpy as np
import glob
import os

# 基础路径
base_input_folder = r'D:\python\data\precipitation_total\precipitation_daily'
base_output_folder = r'D:\python\data\precipitation_total\precipitation_daily_produce'  # 直接保存到最终目录

# 年份循环
for year in range(1997, 2021):
    print(f"\nProcessing year {year}...")
    
    # 构建文件夹路径
    input_folder = os.path.join(base_input_folder, str(year))
    output_folder = os.path.join(base_output_folder, str(year))
    
    # 检查输入文件夹
    if not os.path.exists(input_folder):
        print(f"Warning: Input folder for year {year} does not exist, skipping...")
        continue
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 获取该年份所有nc文件
    nc_files = glob.glob(os.path.join(input_folder, '*.nc'))
    
    if not nc_files:
        print(f"No nc files found for year {year}, skipping...")
        continue
        
    for file_path in nc_files:
        try:
            # 打开并预处理数据集
            dataset = xr.open_dataset(file_path)
            dataset_reduced = dataset.drop_vars(['lat_bounds', 'lon_bounds', 'time_bounds'])
            
            # 创建新的经纬度网格（0.1度分辨率）
            new_lats = np.arange(dataset_reduced.latitude.min(), 
                               dataset_reduced.latitude.max() + 0.1, 0.1)
            new_lons = np.arange(dataset_reduced.longitude.min(), 
                               dataset_reduced.longitude.max() + 0.1, 0.1)
            
            # 进行插值
            interpolated_ds = dataset_reduced.interp(latitude=new_lats, longitude=new_lons)
            
            # 构建新文件名（简化格式）
            filename = os.path.basename(file_path)
            date_part = filename.split('_')[3][1:9]
            new_filename = f'{date_part}.nc'
            new_file_path = os.path.join(output_folder, new_filename)
            
            # 保存插值后的数据集
            interpolated_ds.to_netcdf(new_file_path)
            
            print(f"Processed and interpolated {filename} -> {new_filename}")
            
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
            continue

print("\nAll files have been processed and interpolated!")
