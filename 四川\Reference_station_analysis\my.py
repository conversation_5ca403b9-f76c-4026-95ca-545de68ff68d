from scipy import stats
import numpy as np
from matplotlib import pyplot as plt 
import pandas as pd
 
def sk(data):
    n=len(data)
    Sk     = [0]
    UFk    = [0]
    s      =  0
    E      = [0]
    Var    = [0]
    for i in range(1,n):
        for j in range(i):
            if data[i] > data[j]:
                s = s+1
            else:
                s = s+0
        Sk.append(s)
        E.append((i+1)*(i+2)/4 )                     # Sk[i]的均值
        Var.append((i+1)*i*(2*(i+1)+5)/72 )            # Sk[i]的方差
        UFk.append((Sk[i]-E[i])/np.sqrt(Var[i]))
    UFk=np.array(UFk)
    return UFk

#a为置信度
def MK(data,a):
    ufk=sk(data)          #顺序列
    ubk1=sk(data[::-1])   #逆序列
    ubk=-ubk1[::-1]        #逆转逆序列
    
    #输出突变点的位置
    p=[]
    u=ufk-ubk
    for i in range(1,len(ufk)):
        if u[i-1]*u[i]<0:
            p.append(i)            
    if p:
        print(f"检测到{len(p)}个突变点：", p)
    else:
        print("未检测到突变点")

    
    #画图
    conf_intveral = stats.norm.interval(a, loc=0, scale=1)   #获取置信区间

    plt.figure(figsize=(10,5))
    plt.plot(range(len(data)),ufk,label = 'UFk',color = 'r')
    plt.plot(range(len(data)),ubk,label = 'UBk',color = 'b')
    plt.ylabel('UFk-UBk',fontsize=16)
    x_lim = plt.xlim()
    plt.ylim([-4,5])
    # 设置中文字体
    plt.rcParams["font.family"] = ["SimHei"]
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.plot(x_lim,[conf_intveral[0],conf_intveral[0]],'r--',label='95%显著区间')
    plt.plot(x_lim,[conf_intveral[1],conf_intveral[1]],'r--')
    plt.axhline(0,ls="--",c="k")
    plt.legend(loc='upper center',frameon=False,ncol=3,fontsize=20) # 图例
    plt.xticks(fontsize=16)
    plt.yticks(fontsize=16)
    plt.show()




filepath = r'D:\python\四川\Reference_station_analysis\data\龙泉驿56286.xlsx'
data1 = pd.read_excel(filepath)
data1.sort_values(by='年', inplace=True)
data = data1['平均气压'].values
MK(data,0.95)