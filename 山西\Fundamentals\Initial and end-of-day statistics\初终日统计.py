#%%
import pandas as pd
import numpy as np

f=pd.read_csv(r'D:\python\山西\Fundamentals\Initial and end-of-day statistics\data\data1991-2020.csv')
f['资料时间'] = pd.to_datetime(f['资料时间'])

f['平均气温'] = f['平均气温'].replace(999999,np.nan)

results = []

# 按站点分组处理
for station_id, group in f.groupby('区站号(字符)'):
    first_dates = []
    # 按年份分组处理每个站点每年的数据
    for year,year_group in group.groupby(group['资料时间'].dt.year):
        # 按日期排序
        sorted_year = year_group.sort_values('资料时间')
        # 筛选温度超过10℃的数据
        over_10 = sorted_year[sorted_year['平均气温'] > 10]
        if not over_10.empty:
            # 获取第一个符合条件的日期
            first_date = over_10.iloc[0]['资料时间']
            first_dates.append(first_date)
    if not first_dates:
        # 没有符合条件的日期
        avg_date_str = None
    else:
        # 转换为基准年份（2000年）以计算平均日期
        base_year = 2000
        base_dates = [d.replace(year=base_year) for d in first_dates]
        # 计算平均时间戳
        timestamps = [pd.Timestamp(d).timestamp() for d in base_dates]
        avg_timestamp = sum(timestamps) / len(timestamps)
        avg_date = pd.to_datetime(avg_timestamp, unit='s')
        # 调整为基准年份并格式化
        avg_date = avg_date.replace(year=base_year).strftime('%m-%d')
        avg_date_str = avg_date
        
    results.append({'station_id': station_id, 'average_first_date': avg_date_str})

results = pd.DataFrame(results)
results.to_excel(r'D:\python\山西\Fundamentals\Initial and end-of-day statistics\data\日期.xlsx', index=False)
# %%
