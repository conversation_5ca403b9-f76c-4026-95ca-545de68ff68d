import pandas as pd
import numpy as np
from tqdm import tqdm


file_path1 = r'D:\python\山西\Climate events\cold air\data\最低气温2021-2024.xlsx'
data = pd.read_excel(file_path1)
data['最低气温'] = data['最低气温'].replace(999999, np.nan)
data['资料时间'] = pd.to_datetime(data['资料时间'])
data.sort_values(by='资料时间', inplace=True)
data = data[(data['资料时间'] >= '2023-12-01') & (data['资料时间'] <= '2024-02-01')]

# 计算温度变化
def delattr_T(data):
    result = pd.DataFrame()
    for station_id in tqdm(data['区站号(字符)'].unique()):
        station_data = data[data['区站号(字符)'] == station_id].copy()

        # 计算24小时
        station_data_t24 = station_data['最低气温'].shift(1)
        station_data['∆T24'] = (station_data_t24 - station_data['最低气温'])

        # 计算48小时
        station_data_t48 = station_data['最低气温'].shift(2)
        station_data['∆T48'] = pd.concat([
            station_data_t24,
            station_data_t48], axis=1).min(axis=1) - station_data['最低气温']
        station_data['∆T48'] = station_data['∆T48']

        # 计算72小时
        station_data_t72 = station_data['最低气温'].shift(3)
        station_data['∆T72'] = pd.concat([
            station_data_t24,
            station_data_t48,
            station_data_t72], axis=1).min(axis=1) - station_data['最低气温']
        station_data['∆T72'] = station_data['∆T72']

        # 合并结果
        result = pd.concat([result, station_data], ignore_index=True)

    return result

data = delattr_T(data)

# 计算寒潮强度等级
def intensity_level(data):

    conditions = [
        (data['最低气温'] <= 0) & ((data['∆T24'] >= 12) | (data['∆T48'] >= 14) | (data['∆T72'] >= 16)),
        (data['最低气温'] <= 2) & ((data['∆T24'] >= 10) | (data['∆T48'] >= 12) | (data['∆T72'] >= 14)),
        (data['最低气温'] <= 4) & ((data['∆T24'] >= 8) | (data['∆T48'] >= 10) | (data['∆T72'] >= 12))
    ]
    
    choices = ['特强寒潮', '强寒潮', '寒潮']
    data['强度等级'] = np.select(conditions, choices, default='无寒潮')
    return data

data = intensity_level(data)

# 识别寒潮过程
def identify_cold_wave_processes(data):
    results = []
    
    for station_id in tqdm(data['区站号(字符)'].unique(), desc="识别寒潮过程"):
        station_data = data[data['区站号(字符)'] == station_id].copy()
        
        station_data = station_data.dropna(subset=['强度等级'])
        station_data = station_data[station_data['强度等级'] != '无寒潮']
        
        if station_data.empty:
            continue
        
        station_data = station_data.sort_values('资料时间')
        
        # 识别连续的寒潮过程
        station_data['过程'] = (station_data['资料时间'].diff().dt.days > 1).cumsum()
        
        for process_id, process_data in station_data.groupby('过程'):
            # 计算过程统计量
            start_time = process_data['资料时间'].min()
            end_time = process_data['资料时间'].max()
            total_days = len(process_data)
            cold_wave_days = (process_data['强度等级'] == '寒潮').sum()
            strong_cold_wave_days = (process_data['强度等级'] == '强寒潮').sum()
            severe_cold_wave_days = (process_data['强度等级'] == '特强寒潮').sum()
            
            max_temp_drop_24h = process_data['∆T24'].max()
            max_temp_drop_48h = process_data['∆T48'].max()
            max_temp_drop_72h = process_data['∆T72'].max()
            min_temp = process_data['最低气温'].min()
            
            results.append({
                '区站号': station_id,
                '过程开始时间': start_time,
                '过程结束时间': end_time,
                '寒潮总日数': total_days,
                '寒潮日数': cold_wave_days,
                '强寒潮日数': strong_cold_wave_days,
                '超强寒潮日数': severe_cold_wave_days,
                '过程24h降温幅度最大值': round(max_temp_drop_24h,1),
                '过程48h降温幅度最大值': round(max_temp_drop_48h,1),
                '过程72h降温幅度最大值': round(max_temp_drop_72h,1),
                '过程日最低气温最小值': round(min_temp,1)
            })
    
    return pd.DataFrame(results)

# 生成结果

results_df = identify_cold_wave_processes(data)

# 保存结果
results_df.sort_values(by='区站号', inplace=True)
results_df.to_csv(r'D:\python\山西\Climate events\cold air\data\result.csv', index=False)