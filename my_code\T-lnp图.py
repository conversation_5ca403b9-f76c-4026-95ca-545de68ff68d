import matplotlib.pyplot as plt
import numpy as np

# 假设你有以下温度和压力数据
# 这里只是示例数据，你需要替换为实际数据
temperatures = np.array([25, 20, 15, 10, 5, 0, -5, -10, -15, -20])  # 温度（摄氏度）
pressures = np.array([1013, 950, 890, 830, 770, 710, 650, 590, 530, 470])  # 压力（百帕）

# 将压力转换为对数压力
log_pressures = np.log(pressures)

# 创建图表
plt.figure(figsize=(10, 6))
plt.plot(temperatures, log_pressures, marker='o')

# 设置图表标题和坐标轴标签
plt.title('T-lnp Diagram')
plt.xlabel('Temperature (°C)')
plt.ylabel('Log Pressure (hPa)')

# 显示网格
plt.grid(True)

# 显示图表
plt.show()
