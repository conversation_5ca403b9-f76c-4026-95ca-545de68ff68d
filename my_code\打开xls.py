import xlrd

# 打开文件
workbook = xlrd.open_workbook('豆瓣电影top250.xls')

# 获取所有sheet的名字
sheet_names = workbook.sheet_names()
print(f"Sheet names: {sheet_names}")

# 根据sheet索引或者名称获取sheet内容
sheet = workbook.sheet_by_index(0)  # 或者使用 workbook.sheet_by_name('豆瓣电影top250')

# 打印sheet的名称、行数和列数
print(f"Sheet name: {sheet.name}")
print(f"Number of rows: {sheet.nrows}")
print(f"Number of columns: {sheet.ncols}")

# 读取单元格数据
cell_value = sheet.cell_value(1, 0)  # 读取第一行第一列的单元格数据
print(f"Cell value: {cell_value}")
