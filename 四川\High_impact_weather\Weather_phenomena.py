import numpy as np
import pandas as pd


def read_data():
    """读取并预处理数据"""
    # 从Excel文件读取数据
    data = pd.read_excel(r'D:\python\High_impact_weather\data\中国地面日值数据(SURF_CHN_MUL_DAY).xlsx')
    
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']

    data = data[data['年'] == 2024]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d')
    
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    
    # 按日期排序
    data.sort_index(inplace=True)
    
    return data

# 冰雹
def extract_hail_data(data):
    """
    提取冰雹数据,布尔值0为无冰雹,1为有冰雹
    """
    # 提取冰雹列
    hail = data['冰雹']
    # 处理缺测值999999
    hail = hail.replace(999999, 0)
    # 计算每个月的冰雹天数
    hail_days = hail.groupby(hail.index.month).apply(lambda x: (x > 0).sum())

    return hail_days

# 积雪
def extract_snow_data(data):
    """
    提取积雪数据,布尔值0为无积雪,1为有积雪
    """
    # 提取积雪列
    snow = data['积雪']
    # 处理缺测值999999
    snow = snow.replace(999999, 0)
    # 计算每个月的积雪天数
    snow_days = snow.groupby(snow.index.month).apply(lambda x: (x > 0).sum())

    return snow_days

# 雪
def extract_snowfall_data(data):
    """
    提取雪数据,布尔值0为无雪,1为有雪
    """
    # 提取雪列
    snowfall = data['雪']
    # 处理缺测值999999
    snowfall = snowfall.replace(999999, 0)
    # 计算每个月的雪天数
    snowfall_days = snowfall.groupby(snowfall.index.month).apply(lambda x: (x > 0).sum())

    return snowfall_days

# 雾、霾
def extract_fog_dust_data(data):
    """
    提取雾数据,布尔值0为无雾,1为有雾
    """
    # 提取雾、霾列
    fog = data['雾']
    fog = fog.replace(999999, 0)
    dust = data['霾']
    dust = dust.replace(999999, 0)
    # 计算每个月的雾天数
    fog_days = fog.groupby(fog.index.month).apply(lambda x: (x > 0).sum())
    dust_days = dust.groupby(dust.index.month).apply(lambda x: (x > 0).sum())
    # 提取平均能见度列
    visibility = data['最小水平能见度']/1000
    # 处理缺测值999999
    visibility = visibility.replace(999999, 0)
    # 计算1-12月的平均能见度
    visibility_mean = visibility.groupby(visibility.index.month).mean()
    # 计算最小能见度
    visibility_min = visibility.groupby(visibility.index.month).min()
    # 合并雾、霾,输出为3列
    fog_dust_days = pd.concat([fog_days, dust_days, visibility_mean,visibility_min], axis=1)
    fog_dust_days.columns = ['雾', '霾', '平均能见度', '最小能见度']


    return fog_dust_days




# 主函数
def main():
    # 读取数据
    data = read_data()
    # 冰雹
    hail_days = extract_hail_data(data)
    # 输出结果
    #print(hail_days)
    # 积雪
    snow_days = extract_snow_data(data)
    # 输出结果
    #print(snow_days)
    # 雪
    #snowfall_days = extract_snowfall_data(data)
    # 输出结果
    #print(snowfall_days)
    # 雾、霾
    fog_dust_days = extract_fog_dust_data(data)
    # 输出结果
    #print(fog_dust_days)


if __name__ == "__main__":
    main()