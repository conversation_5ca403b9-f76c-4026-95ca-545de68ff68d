import pandas as pd

file_path = r'D:\python\四川\Return_period_extremum\Bureau_data\1991~2020年德阳降水年际变化统计图.xlsx'
data = pd.read_excel(file_path)

sorted_data = data.sort_values(by='降水', ascending=True).reset_index(drop=True)


sorted_data['经验概率'] = 1 - (sorted_data.index.values + 1) / (len(sorted_data) + 1)


import matplotlib.pyplot as plt

plt.rcParams['font.family'] = ['Microsoft YaHei']  # 用来正常显示中文标签
plt.figure(figsize=(10, 6))

plt.scatter(sorted_data['经验概率'], sorted_data['降水'], 
           edgecolors='k',  # 边框颜色
           facecolors='none', # 空心填充
           s=60,            # 点大小
           linewidths=1.5,  # 边框粗细
           label='数据点')

plt.ylabel('降水')
plt.xlabel('经验概率')
plt.title('经验概率分布')
plt.grid(True)
plt.savefig(r'D:\python\四川\Return_period_extremum\1991~2020年德阳降水年际变化统计图.png')
plt.close()


