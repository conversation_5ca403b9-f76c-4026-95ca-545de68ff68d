import xarray as xr
import os
import pandas as pd
import pyodbc

def get_nc_info(nc_file):
    try:
        ds = xr.open_dataset(nc_file, engine='netcdf4')
        print(ds)
    except Exception as e:
        print(f"读取NetCDF文件失败: {str(e)}")

def get_grib2_info(grib2_file):
    try:
        ds = xr.open_dataset(grib2_file, engine='cfgrib')
        print(ds)
    except ModuleNotFoundError:
        print("错误：缺少cfgrib或ecCodes依赖库")
        print("请执行：pip install cfgrib eccodes")
    except Exception as e:
        print(f"读取GRIB2文件失败: {str(e)}")

def get_mdb_info(mdb_file):
    """读取Access数据库文件"""
    try:
        conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={mdb_file};'
        with pyodbc.connect(conn_str) as conn:
            # 获取第一个表名
            table_name = conn.cursor().tables(tableType='TABLE').fetchone().table_name
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            print(df.head())
    except pyodbc.Error as e:
        print(f"数据库连接错误: {str(e)}")
    except Exception as e:
        print(f"读取MDB文件失败: {str(e)}")

def process_file(file_name):
    if not os.path.exists(file_name):
        print("文件不存在！")
        return
        
    ext = os.path.splitext(file_name)[1].lower()
    
    if ext == '.nc':
        get_nc_info(file_name)
    elif ext in ('.grib2', '.grb2', '.grib'):
        get_grib2_info(file_name)
    elif ext == '.mdb':
        get_mdb_info(file_name)
    else:
        print('不支持的文件格式')

if __name__ == '__main__':
    file_path = input("请输入数据文件路径：")
    process_file(file_path)
    #E:\Data_wrangling\data\CLDAS\CLDAS\Z_NAFP_C_BABJ_20250306000714_P_CLDAS_RT_CHN_0P05_HOR-VIS-2025030600.GRB2
    #E:\Data_wrangling\data\CMPAS\Z_SURF_C_BABJ_20250306000425_P_CMPA_RT_CHN_0P01_HOR-PRE-2025030600.GRB2
    #E:\Data_wrangling\data\COBE sst\sst.mon.mean.nc
    #D:\python\data\mdb\2006.mdb
    #E:\Data_wrangling\data\CRA\CRA40LAND_SURFACE_2025030600_GLB_0P25_HOUR_V1_0_0.grib