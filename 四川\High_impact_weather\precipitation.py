from collections import Counter
import numpy as np
import pandas as pd

def read_data():
    """读取并预处理数据"""
    # 从Excel文件读取数据
    data = pd.read_excel(r'D:\python\High_impact_weather\data\中国地面日值数据(SURF_CHN_MUL_DAY).xlsx')
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']
    data = data[data['年'] == 2024]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d')
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    # 按日期排序
    data.sort_index(inplace=True)
    return data

# 定义信息输出函数
def print_info(a,b):
    """格式化输出信息"""
    data = read_data()
    precipitation_mask = (data['20-20时降水量'] >= a) & (data['20-20时降水量'] < b)
    # 获取所有符合条件的日期
    pre_dates = data[precipitation_mask].index
    # 提取月份信息
    pre_months = pre_dates.month
    # 统计每个月份的高温天气天数
    month_counts = Counter(pre_months)
    # 生成完整月份数据（1-12月）
    all_months = range(1, 13)
    month_data = {f"{m}月": int((month_counts.get(m, 0))) for m in all_months}
    #print(full_month_data)
    pre_days = precipitation_mask.sum()
    return month_data, int(pre_days/5)



# 计算高温天气天数(轻度高温 ≥ 35℃)
def calculate_high_precipitation_weather():
    """
    计算暴雨天气天数
    暴雨日数,降水量≥50mm
    大暴雨日数,降水量≥100mm
    特大暴雨日数,降水量≥250mm
    """
    #暴雨
    rainstorm,rainstorm_days = print_info(50,100)
    #大暴雨
    Heavy_rain,Heavy_rain_days = print_info(100,250)
    #特大暴雨
    Extreme_heavy_rain,Extreme_heavy_rain_days = print_info(250,1000)
    # 计算高温天气总天数
    total_days = rainstorm_days + Heavy_rain_days + Extreme_heavy_rain_days
    return rainstorm,Heavy_rain,Extreme_heavy_rain,total_days



if __name__ == "__main__":

    # 计算高温天气
    rainstorm,Heavy_rain,Extreme_heavy_rain, total_days = calculate_high_precipitation_weather()
    #print("暴雨总日数:", int(total_days))
    print("暴雨日数:", rainstorm)
    print("大暴雨日数:", Heavy_rain)
    print("特大暴雨日数:", Extreme_heavy_rain)
