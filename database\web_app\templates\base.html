<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="现代化气象站点数据库管理系统，提供高效的数据管理、分析和可视化功能">
    <meta name="keywords" content="气象站点,数据管理,天气数据,气象分析">
    <meta name="author" content="气象数据管理系统">

    <title>{% block title %}气象站点数据库管理系统{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌤️</text></svg>">

    <!-- CSS Dependencies -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 现代化导航栏 -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/datacenter">
                <div class="icon-wrapper me-2" style="width: 40px; height: 40px; background: linear-gradient(135deg, var(--primary-color), var(--accent-color));">
                    <i class="bi bi-cloud-sun text-white"></i>
                </div>
                <span class="fw-bold">数据中心</span>
            </a>
            <a href="/" class="btn btn-outline-primary btn-sm ms-2">
                <i class="bi bi-house-door me-1"></i>返回系统主页
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/datacenter" data-nav="home"><i class="bi bi-house-door me-1"></i>数据中心首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/datacenter/station_list" data-nav="stations"><i class="bi bi-geo-alt me-1"></i>站点列表</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" data-nav="import">
                            <i class="bi bi-cloud-upload me-1"></i>数据导入
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="combined">导入组合数据</a></li>
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="stations">导入站点数据</a></li>
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="observations">导入观测数据</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content" style="padding-top: 80px;">
        {% block content %}{% endblock %}
    </main>

    <!-- 现代化上传模态框将通过JavaScript动态创建 -->

    <!-- 现代化页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <div class="icon-wrapper me-3" style="width: 32px; height: 32px; background: linear-gradient(135deg, var(--primary-color), var(--accent-color));">
                            <i class="bi bi-cloud-sun text-white" style="font-size: 0.9rem;"></i>
                        </div>
                        <div>
                            <div class="fw-bold">气象数据管理系统</div>
                            <small class="text-muted">现代化数据管理解决方案</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">© 2025 版权所有 · 专业气象数据管理</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button class="btn btn-primary btn-fab" id="backToTop" style="display: none;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- 高德地图API -->
    {% if amap_api_key %}
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{ amap_api_key }}"></script>
    {% endif %}

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/upload-modal.js') }}"></script>

    <!-- 返回顶部功能 -->
    <script>
        // 返回顶部按钮功能
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 设置导航激活状态
        function setActiveNavigation() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link[data-nav]');

            // 移除所有激活状态
            navLinks.forEach(link => link.classList.remove('active'));

            // 根据当前路径设置激活状态
            if (currentPath === '/datacenter' || currentPath === '/datacenter/') {
                // 数据中心首页
                const homeLink = document.querySelector('[data-nav="home"]');
                if (homeLink) homeLink.classList.add('active');
            } else if (currentPath === '/datacenter/station_list' || currentPath.startsWith('/datacenter/station/')) {
                // 站点列表或站点详情页
                const stationsLink = document.querySelector('[data-nav="stations"]');
                if (stationsLink) stationsLink.classList.add('active');
            } else if (currentPath.includes('import') || currentPath.includes('upload')) {
                // 数据导入相关页面
                const importLink = document.querySelector('[data-nav="import"]');
                if (importLink) importLink.classList.add('active');
            }
        }

        // 页面加载时设置激活状态
        document.addEventListener('DOMContentLoaded', setActiveNavigation);

        // 监听页面变化（用于SPA导航）
        window.addEventListener('popstate', setActiveNavigation);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
