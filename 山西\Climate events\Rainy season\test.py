
#%%
import pandas as pd
from tqdm import tqdm

file_path = r'D:\python\山西\Climate events\Rainy season\data\降水2020-2024(6-8).xlsx'
df = pd.read_excel(file_path)
df['资料时间'] = pd.to_datetime(df['资料时间'])
stations = df['区站号(字符)'].unique()

file_path1 = r'D:\python\山西\Climate events\Rainy season\data\20-20时降水量气候态.xlsx'
df1 = pd.read_excel(file_path1)

file_path2 = r'D:\python\山西\Climate events\Rainy season\data\2022年1月1日至12月31日西太平洋副高脊线位置指数.xlsx'
df2 = pd.read_excel(file_path2)
df2['日期'] = pd.to_datetime(df2['日期'])
df2 = df2[(df2['日期'] >= '2022-06-27') & (df2['日期'] <= '2022-08-31')]
df2 = df2[['日期', '西太平洋副高脊线位置指数监测值']]
df2['西太平洋副高脊线位置指数监测值'] = df2['西太平洋副高脊线位置指数监测值'].interpolate(method='linear')
df2['连续5日大于25'] = df2['西太平洋副高脊线位置指数监测值'].rolling(5,min_periods=5).mean() >= 25
df2['连续5日大于25'] = df2['连续5日大于25'].astype(int)
df2 = df2[(df2['日期'] >= '2022-07-01') & (df2['日期'] <= '2022-08-31')]
df2.reset_index(drop=True, inplace=True)



#%%
results = []
def get_climate(station,date_window,year):
    """获取对应日期范围的气候平均值"""
    if year % 4 != 0:
        df_new = df1[df1['日期'] != '02-29'].copy()
        df_new['日期'] = pd.to_datetime(f'{year}-' + df_new['日期'].astype(str), format='%Y-%m-%d')
    else:
        df1['日期'] = pd.to_datetime(f'{year}-' + df1['日期'].astype(str), format='%Y-%m-%d')
        df_new = df1.copy()
    climate_dates = []
    for date in date_window:
        climate_date = df_new[(df_new['区站号(字符)'] == station) & (df_new['日期'].dt.month == date.month) & 
                        (df_new['日期'].dt.day == date.day)]          
        climate_dates.append(climate_date)
    climate_df = pd.concat(climate_dates)
    return climate_df


station = 53588
station_df = df[df['区站号(字符)'] == station].copy()
year = 2022
year_mask = (
    station_df['资料时间'].dt.year == year)
station_df_year = station_df[year_mask].copy()
station_df_year.sort_values(by='资料时间', inplace=True)
station_df_year.reset_index(drop=True, inplace=True)

station_df_year['向前滑动5日累计降水量'] = station_df_year['20-20时降水量'].rolling(5, min_periods=5).sum()
station_df_year['大于等于10mm'] = (station_df_year['20-20时降水量'] >= 10).astype(int)
station_df_year['5天内存在≥10mm'] = station_df_year['大于等于10mm'].rolling(5, min_periods=1).max().astype(int)
station_df_year['向前滑动5日平均降水量'] = station_df_year['20-20时降水量'].rolling(5, min_periods=5).mean()

# 雨季开始日判定
start_date = None
end_date = None
for idx,row in df2.iterrows():
    if row['连续5日大于25'] == 1:
        calculate_start_date = row['日期']
        start_candidates = station_df_year[station_df_year['资料时间'] >= calculate_start_date]
        break
        
for idx, row in start_candidates.iterrows():
    flag = '异常判断'
    if row['向前滑动5日累计降水量'] >= 35 and row['5天内存在≥10mm'] == 1:
        date = row['资料时间']
        actual_window = station_df_year[
                (station_df_year['资料时间'] >= date - pd.Timedelta(days=4)) &
                (station_df_year['资料时间'] <= date)
            ]
        first_10mm_day = actual_window[actual_window['20-20时降水量'] >= 10].head(1)
        if not first_10mm_day.empty:
            start_date = first_10mm_day['资料时间'].iloc[0]

        if start_date and start_date <= pd.to_datetime(f'{year}-08-11'):
            # 雨季结束日判定
            end_candidates = station_df_year[
                (station_df_year['资料时间'] > start_date) &
                (station_df_year['资料时间'] <= pd.to_datetime(f'{year}-08-31'))
            ]

            for end_idx in range(len(end_candidates)-9):
                window = end_candidates.iloc[end_idx+4:end_idx+10]
                # 检查窗口内所有行的降水量是否都小于35
                if (window['向前滑动5日累计降水量'] < 35).all():
                    end_date = window.iloc[-1]['资料时间']
                    break  # 找到第一个满足条件的窗口后退出循环
            

            if start_date and end_date:
                flag = '正常判断'
                rain_duration = None
                total_rain = None
                # 雨季长度、单站雨量计算
                rain_duration_data = station_df_year[
                    (station_df_year['资料时间'] >= start_date) &
                    (station_df_year['资料时间'] <= end_date)
                ]
                total_rain = round(rain_duration_data['20-20时降水量'].sum(), 2)
                rain_duration = (end_date - start_date).days + 1

                results.append({
                    '区站号': station,
                    '年份': year,
                    '开始日': start_date.strftime('%Y-%m-%d') if start_date else '空雨季',
                    '结束日': end_date.strftime('%Y-%m-%d') if isinstance(end_date, pd.Timestamp) else end_date,
                    '雨季长度': rain_duration if isinstance(rain_duration, int) else '空雨季',
                    '单站雨量': total_rain if isinstance(total_rain, float) else '空雨季',
                    '判断标志': flag
                })
                break
                

        if flag != '正常判断': 
            start_date = None
            end_date = None
            start_candidates1 = station_df_year[
                (station_df_year['资料时间'] >= pd.to_datetime(f'{year}-07-01')) &
                (station_df_year['资料时间'] <= pd.to_datetime(f'{year}-08-10'))
            ]
            max_rain = start_candidates1['向前滑动5日平均降水量'].max()
            max_rain_dates = start_candidates1[start_candidates1['向前滑动5日平均降水量'] == max_rain]
            
            start_date = None
            for max_rain_date in max_rain_dates['资料时间']:
                actual_window1 = station_df_year[
                    (station_df_year['资料时间'] >= max_rain_date - pd.Timedelta(days=4)) &
                    (station_df_year['资料时间'] <= max_rain_date)
                ]
                # 获取同期气候数据
                climate_mean = get_climate(station , actual_window1['资料时间'], year)
                
                if len(climate_mean) == len(actual_window1):
                    merged_data = pd.merge(
                        actual_window1[['资料时间', '向前滑动5日平均降水量']],
                        climate_mean[['日期', '20-20时降水量']],
                        left_on='资料时间',
                        right_on='日期'
                    )
                # 逐行对比，寻找首次超过气候值的日期
                first_exceed_date = None
                for idx, row in merged_data.iterrows():
                    if row['向前滑动5日平均降水量'] > row['20-20时降水量']:
                        first_exceed_date = row['资料时间']
                        break

                if first_exceed_date:
                    if start_date is None:
                        start_date = first_exceed_date
                    if first_exceed_date < start_date:
                        start_date = first_exceed_date
                    else:
                        start_date = start_date

            end_candidates1 = station_df_year[
                (station_df_year['资料时间'] >= pd.to_datetime(f'{year}-07-21')) &
                (station_df_year['资料时间'] <= pd.to_datetime(f'{year}-08-31'))                    
            ]
            if not end_candidates1.empty:
                max_rain = end_candidates1['向前滑动5日平均降水量'].max(skipna=True)
                max_rain_dates = end_candidates1[end_candidates1['向前滑动5日平均降水量'] == max_rain]

                for max_rain_date in max_rain_dates['资料时间']:
                    actual_window2 = station_df_year[
                        (station_df_year['资料时间'] >= max_rain_date - pd.Timedelta(days=4)) &
                        (station_df_year['资料时间'] <= max_rain_date)
                    ]
                    if not actual_window2.empty:
                        climate_mean1 = get_climate(station, actual_window2['资料时间'], year)
                        if len(climate_mean1) == len(actual_window2):
                                merged_data = pd.merge(
                                    actual_window2[['资料时间', '向前滑动5日平均降水量']],
                                    climate_mean1[['日期', '20-20时降水量']],
                                    left_on='资料时间',
                                    right_on='日期'
                                )
                        last_exceed_date = None
                        # 逆序遍历，寻找最后一次超过气候值的日期
                        for idx, row in merged_data.iloc[::-1].iterrows():
                            if row['向前滑动5日平均降水量'] > row['20-20时降水量']:
                                last_exceed_date = row['资料时间']
                                break
                        if last_exceed_date:
                                if end_date is None:
                                    end_date = last_exceed_date
                                if last_exceed_date > end_date:
                                    end_date = last_exceed_date
                                else:
                                    end_date = end_date

            rain_duration = None
            total_rain = None
            # 雨季长度、单站雨量计算
            if start_date and end_date:
                rain_duration_data = station_df_year[
                    (station_df_year['资料时间'] >= start_date) &
                    (station_df_year['资料时间'] <= end_date)
                ]
                total_rain = round(rain_duration_data['20-20时降水量'].sum(), 2)
                rain_duration = (end_date - start_date).days + 1

            
            results.append({
                '区站号': station,
                '年份': year,
                '开始日': start_date.strftime('%Y-%m-%d') if start_date else '空雨季',
                '结束日': end_date.strftime('%Y-%m-%d') if isinstance(end_date, pd.Timestamp) else end_date,
                '雨季长度': rain_duration if isinstance(rain_duration, int) else '空雨季',
                '单站雨量': total_rain if isinstance(total_rain, float) else '空雨季',
                '判断标志': flag
            })
            break
            
results.sort(key=lambda x: (x['区站号'], x['年份']))
pd.DataFrame(results).to_csv('雨季监测结果1.csv', index=False)


# %%
