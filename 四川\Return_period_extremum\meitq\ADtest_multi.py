import numpy as np
from scipy.stats import anderson_ksamp,PermutationMethod

def ADtest_multi(x1,x2,alpha):
    samples = [x1,x2]
    res = anderson_ksamp(samples,method = PermutationMethod())
    pvalue = res.pvalue
    svalue = res.statistic
    if pvalue<alpha:
        m = 1
    else:
        m = 0
    return svalue,pvalue,m



rng = np.random.default_rng(seed=1)
x1 = rng.normal(1,1,30)
x2 = rng.normal(1,0.5,45)
print(ADtest_multi(x1,x2,0.01))
