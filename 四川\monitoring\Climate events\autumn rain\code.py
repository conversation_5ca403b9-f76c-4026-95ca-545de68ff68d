#%% 加载数据
import pandas as pd
import numpy as np

year = 2023
# 读取数据
data_rain = pd.read_excel(rf'D:\python\四川\monitoring\Climate events\autumn rain\data\{year}年逐日降水数据.xlsx')
data_rain['资料时间'] = pd.to_datetime(data_rain['资料时间'])
data_rain.sort_values(by='资料时间', inplace=True)
data_rain['20-20时降水量'] = data_rain['20-20时降水量'].replace(999990, np.nan)

# 读取站点信息
station_ids = [56181,56183,56186,56187,56188,56189,56190,56193,57415,57416,57417,
               57420,57503,57507,57600,57603,56194,56195,56196,56197,56198,56199,
               56272,56273,56276,56278,56279,56280,56281,56284,56285,56287,56288,
               56289,56290,57604,57605,57608,56295,56296,56297,56298,56371,56373,
               56374,56376,56378,56380,56381,56382,56383,56384,56386,56387,56291,
               57217,57237,57303,57304,57306,57307,57308,57309,57313,57314,57315,
               57317,57318,57320,57324,57326,57328,56389,56390,56391,56393,56394,
               56395,56396,56399,56474,56475,56480,56485,56487,56490,56492,56493,
               56494,56496,57329,57401,57402,57405,57407,57408,57411,57413,57414,
               56498,56499,56592,56593,57204,57206,57208,57216]
data_rain = data_rain[data_rain['区站号(字符)'].isin(station_ids)]

#%% 判断秋雨过程
# 判断秋雨日函数
def is_autumn_rain_day(data):
    valid_stations = (data['20-20时降水量'] >= 0.1).sum()  
    ratio = valid_stations / 107 
    return ratio >= 0.5

# 初始化秋雨日标记列
data_rain['是否为秋雨日'] = False

# 标记秋雨日
for date in data_rain['资料时间'].unique():
    mask = data_rain['资料时间'] == date
    data_rain.loc[mask, '是否为秋雨日'] = is_autumn_rain_day(data_rain[mask])

# 设置分析起始日期
start_date = f'{year}-08-21'

# 获取分析期秋雨日标记
autumn_rain_days = data_rain[data_rain['资料时间'] >= pd.to_datetime(start_date)]
autumn_rain_series = autumn_rain_days.groupby('资料时间')['是否为秋雨日'].first()

# 秋雨开始日期检测
start_date_detected = None
window_size = 5

for i in range(len(autumn_rain_series) - window_size + 1):
    current_window = autumn_rain_series.iloc[i:i+window_size]
    
    # 条件1：连续5个秋雨日
    if all(current_window):
        start_date_detected = current_window.index[0]
        break
    
    # 条件2：首尾为秋雨日，5天中最多1个非秋雨日（至少4个秋雨日）
    if (current_window.iloc[0] and current_window.iloc[-1] and 
        current_window.sum() >= 4 and 
        current_window.iloc[1:-1].sum() >= 2):
        start_date_detected = current_window.index[0]
        break

# 秋雨结束日期检测
end_date_detected = None

if start_date_detected:
    print(f'华西秋雨开始日期为：{start_date_detected.strftime("%Y-%m-%d")}')
    
    # 设置结束日期上限
    end_date_limit = pd.to_datetime(f'{year}-11-30')
    end_period = autumn_rain_series[start_date_detected:end_date_limit]
    
    # 逆序滑动窗口检测
    for i in range(len(end_period) - window_size, -1, -1):
        current_window = end_period.iloc[i:i+window_size]
        
        # 条件1: 连续5个秋雨日
        condition1 = all(current_window)
        
        # 条件2: 5天中第2-4天出现1个非秋雨日
        condition2 = (current_window.iloc[0] and current_window.iloc[-1] and 
                      current_window.sum() == 4 and 
                      current_window.iloc[1:-1].sum() >= 2)
        
        if condition1 or condition2:
            # 结束日期为窗口最后一天的下一天
            end_date_detected = current_window.index[-1] + pd.DateOffset(days=1)
            break
    
    if end_date_detected:
        print(f'华西秋雨结束日期为：{end_date_detected.strftime("%Y-%m-%d")}')
    else:
        print('未检测到符合条件的华西秋雨结束日期')
    print(f'秋雨期长度为：{(end_date_detected - start_date_detected).days}天')

    

    # 筛选秋雨期数据
    rain_period = data_rain[
        (data_rain['资料时间'] >= start_date_detected) &
        (data_rain['资料时间'] <= end_date_detected)
    ]
    
    # 计算每日平均降水量
    station_rainfall = rain_period.groupby('资料时间')['20-20时降水量'].mean()
    
    # 计算区域平均降水量累积值（秋雨量指数R）
    R = station_rainfall.sum()
    print(f'华西秋雨量为：{R:.2f}毫米')

else:
    print('未检测到符合条件的华西秋雨开始日期')


# %%
