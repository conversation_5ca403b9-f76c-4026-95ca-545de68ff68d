#%%

import pandas as pd
import numpy as np

file_path = r'D:\python\四川\monitoring\Climate events\cold\data\SURF_CHN_MUL_DAY(2024).xlsx'

data = pd.read_excel(file_path)
data['最低气温'] = data['最低气温'].replace(999999,np.nan)


data['资料时间'] = pd.to_datetime(data['资料时间'])
data.sort_values(by='资料时间', inplace=True)
data = data.dropna(subset=['最低气温'])


#  降温幅度计算
def delattr_T(data):
    result = pd.DataFrame()
    for station_id in data['区站号(字符)'].unique():
        station_data = data[data['区站号(字符)'] == station_id].copy()

        # 计算24小时
        station_data_t24 = station_data['最低气温'].shift(-1)
        station_data['∆T24'] = -(station_data_t24 - station_data['最低气温'])

        # 计算48小时
        station_data_t48 = station_data['最低气温'].shift(-2)
        station_data['∆T48'] = pd.concat([
            station_data_t24,
            station_data_t48], axis=1).min(axis=1) - station_data['最低气温']
        station_data['∆T48'] = -station_data['∆T48']

        # 计算72小时
        station_data_t72 = station_data['最低气温'].shift(-3)
        station_data['∆T72'] = pd.concat([
            station_data_t24,
            station_data_t48,
            station_data_t72], axis=1).min(axis=1) - station_data['最低气温']
        station_data['∆T72'] = -station_data['∆T72']

        # 合并结果
        result = pd.concat([result, station_data], ignore_index=True)

    return result

data = delattr_T(data)

#  强度等级分级
def intensity_level(data):
    data['T_min_24h'] = data.groupby('区站号(字符)')['最低气温'].shift(-1)
    data['T_min_48h'] = data.groupby('区站号(字符)')['最低气温'].shift(-2)
    data['T_min_72h'] = data.groupby('区站号(字符)')['最低气温'].shift(-3)

    # 判断连续下降条件
    data['连续下降48h'] = (data['最低气温'] > data['T_min_24h']) & \
                          (data['T_min_24h'] > data['T_min_48h'])
    data['连续下降72h'] = (data['最低气温'] > data['T_min_24h']) & \
                          (data['T_min_24h'] > data['T_min_48h']) & \
                          (data['T_min_48h'] > data['T_min_72h'])

    # 初始化强度等级时指定dtype
    data['强度等级'] = pd.Series(index=data.index, dtype='object')

    # 寒潮条件判断
    mask_24h = (data['∆T24'] >= 8) & (data['T_min_24h'] <= 4)
    mask_48h = (data['∆T48'] >= 10) & (data['连续下降48h']) & (data['T_min_48h'] <= 4)
    mask_72h = (data['∆T72'] >= 12) & (data['连续下降72h']) & (data['T_min_72h'] <= 4)

    data.loc[mask_24h | mask_48h | mask_72h, '强度等级'] = '寒潮'
    data.loc[data['∆T48'] >= 8, '强度等级'] = '强冷空气'
    data.loc[data['∆T48'].between(6, 8, inclusive='left'), '强度等级'] = '中等强度冷空气'

    data.drop(['T_min_24h', 'T_min_48h', 'T_min_72h', '连续下降48h', '连续下降72h'], axis=1, inplace=True)

    return data

data = intensity_level(data)


#  区域冷空气过程识别
import numpy as np

def detect_regional_cold_events(data):
    # 步骤1：计算每日有效站点比例（修复分组警告）
    daily_stats = (
        data.groupby('资料时间', group_keys=False) 
        [['区站号(字符)', '强度等级']]  
        .apply(lambda g: pd.Series({
            'total_stations': g['区站号(字符)'].nunique(),
            'valid_stations': g[g['强度等级'].isin(['中等强度冷空气', '强冷空气', '寒潮'])]['区站号(字符)'].nunique()
        }))
        .reset_index()
    )
    daily_stats['ratio'] = daily_stats['valid_stations'] / daily_stats['total_stations']
    daily_stats['valid_day'] = daily_stats['ratio'] >= 0.2
    daily_stats = daily_stats.sort_values('资料时间').reset_index(drop=True)

    # 阶段1：识别所有连续有效时段 --------------------------------------------------
    raw_events = []
    current_event = None
    
    for idx in range(len(daily_stats)):
        row = daily_stats.iloc[idx]
        
        # 发现有效日且未开始记录
        if row['valid_day'] and current_event is None:
            current_event = {
                'start_date': row['资料时间'],
                'end_date': row['资料时间'],
                'valid_stations_seq': [row['valid_stations']]
            }
        
        # 延续已有事件
        elif row['valid_day'] and current_event is not None:
            current_event['end_date'] = row['资料时间']
            current_event['valid_stations_seq'].append(row['valid_stations'])
        
        # 遇到无效日且存在进行中事件
        elif not row['valid_day'] and current_event is not None:
            # 保存持续时间≥2天的事件
            if (current_event['end_date'] - current_event['start_date']).days >= 1:
                raw_events.append(current_event)
            current_event = None
    
    # 捕获最后一个事件
    if current_event is not None and (current_event['end_date'] - current_event['start_date']).days >= 1:
        raw_events.append(current_event)

    # 阶段2：分割复合事件 --------------------------------------------------------
    final_events = []
    
    for event in raw_events:
        seq = event['valid_stations_seq']
        dates = pd.date_range(event['start_date'], event['end_date'], freq='D')
        
        # 检测拐点位置（至少需要3天数据）
        split_indices = []
        for i in range(1, len(seq)-1):
            if seq[i-1] > seq[i] and seq[i+1] > seq[i]:
                split_indices.append(i)
        
        # 生成子事件时间范围
        sub_starts = [event['start_date']] + [dates[i+1] for i in split_indices]
        sub_ends = [dates[i] for i in split_indices] + [event['end_date']]
        
        # 筛选有效子事件
        for s, e in zip(sub_starts, sub_ends):
            if (e - s).days >= 1:  # 持续时间≥2天
                final_events.append({
                    'start_date': s.strftime('%Y-%m-%d'),
                    'end_date': e.strftime('%Y-%m-%d'),
                    'duration': (e - s).days + 1
                })

    # 阶段3：计算强度指数 --------------------------------------------------------
    def calculate_I(n3, n2, n1):
        a = 3*n3 + 2*n2 + n1 
        b = n3 + n2 + n1
        I = a/b
        return I
    
    def calculate_M(i, n3, n2, n1, n):
        a = n3 + n2 + n1 
        M = i * np.sqrt(a/n)
        return M

    for event in final_events:
        start_date = pd.to_datetime(event['start_date'])
        end_date = pd.to_datetime(event['end_date'])
        event_data = data[(data['资料时间'] >= start_date) & (data['资料时间'] <= end_date)]
        n3 = event_data['强度等级'].isin(['寒潮']).sum()
        n2 = event_data['强度等级'].isin(['中等强度冷空气']).sum()
        n1 = event_data['强度等级'].isin(['强冷空气']).sum()
        I = calculate_I(n3, n2, n1)
        final_events[final_events.index(event)]['强度指数'] = I
        n = data['区站号(字符)'].nunique()
        M = calculate_M(I, n3, n2, n1, n)
        final_events[final_events.index(event)]['综合强度指数'] = M

    # 阶段4：强度等级划分 --------------------------------------------------------
    for event in final_events:
        I = event['强度指数']
        if 1.0 <= 1.5 < 1.7:
            event['强度等级'] = '中等强度冷空气过程'
        elif 1.7 <= I < 1.95:
            event['强度等级'] = '强冷空气过程'
        elif 1.95 <= I < 3:
            event['强度等级'] = '寒潮过程'
        else:
            event['强度等级'] = '其他'

    return final_events

# 执行检测
cold_events = detect_regional_cold_events(data)


# 打印结果
for i, event in enumerate(cold_events, 1):
    print(f"过程{i}: {event['start_date']} 至 {event['end_date']}（持续{event['duration']}天）",'强度指数：',
          round(event['强度指数'],2),  '强度等级：', event['强度等级'], '综合强度指数：', round(event['综合强度指数'],2))


# %%
