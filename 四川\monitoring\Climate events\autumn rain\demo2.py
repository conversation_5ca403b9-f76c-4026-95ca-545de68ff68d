import pandas as pd

filepath = r'D:\python\四川\monitoring\Climatic factors\data\station\SURF_CHN_MUL_DAY(2020-2024).xlsx'
data = pd.read_excel(filepath, sheet_name='Sheet1')
# 获取年月日列
year = data['年']
month = data['月']
day = data['日']
#%%
# 将年月日组合成 datetime 格式
date_time = pd.to_datetime(year * 10000 + month * 100 + day, format='%Y%m%d')
data['资料时间'] = date_time
data = data.drop(columns=['年', '月', '日'])
data.to_csv(r'D:\python\database\example_data\四川省2020-2024数据.csv', index=False)


#%%
import pandas as pd
filepath1 = r'D:\python\四川\monitoring\Climate events\autumn rain\data\2010年逐日降水数据.xlsx'
data_rain = pd.read_excel(filepath1)

data_rain.to_csv(r'D:\python\database\example_data\2010年逐日降水数据.csv', index=False)
# %%
