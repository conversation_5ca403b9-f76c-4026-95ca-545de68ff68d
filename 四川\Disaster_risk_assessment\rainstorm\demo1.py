import pandas as pd


# 读取CSV文件
df = pd.read_excel(r'D:\python\Disaster_risk_assessment\rainstorm\data\Day.xlsx')
data = df.copy()
year = data['年']
month = data['月']
day = data['日']
data = data[data['年'] >= 2023]
data = data[data['20-20时降水量'] >= 50]

# 将年月日组合成datetime格式
date_time = pd.to_datetime(year * 10000 + month * 100 + day, format='%Y%m%d')
# 删除原始的年月日列
data.drop(['年', '月', '日'], axis=1, inplace=True)
# 添加新的日期列并设置为索引
data['date'] = date_time
data.set_index('date', inplace=True)
# 按日期排序
data.sort_index(inplace=True)


print(data)

