import pandas as pd

file_path = r'D:\python\山西\data\SURF_CHN_MUL_DAY(2020-2024).xlsx'
df = pd.read_excel(file_path)

df['资料时间'] = pd.to_datetime(df['资料时间'])
df['平均气温'] = df['平均气温'].replace({999999: None, 999990: None, 999998: None})
df['20-20时降水量'] = df['20-20时降水量'].replace({999999: None, 999990: None, 999998: None})
data = df[
    (df['资料时间'].dt.year.isin([2020, 2021, 2022, 2023, 2024])) & 
    (df['资料时间'].dt.month.isin([3, 4, 5, 6, 7, 8, 9]))
]

years = [2020]
stations = df['区站号(字符)'].unique()
ave_temp = []
total_pre = []
for station in stations:
    station_data = data[data['区站号(字符)'] == station]
    for year in years:
        ave_t = station_data[station_data['资料时间'].dt.year == year]['平均气温'].mean()
        total_p = station_data[station_data['资料时间'].dt.year == year]['20-20时降水量'].sum()
        ave_temp.append(
            {'station': station,
             'year': year, 
             '原值': ave_t})
        total_pre.append(
            {'station': station, 
             'year': year,
             '原值': total_p}
        )

ave_temp_df = pd.DataFrame(ave_temp)
total_pre_df = pd.DataFrame(total_pre)
#%%
total_pre_df = total_pre_df.sort_values(by='station')
print(total_pre_df)
# %%
