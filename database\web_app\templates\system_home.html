<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小恐龙的超算单元</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6f42c1;
            --secondary-color: #20c997;
            --accent-color: #fd7e14;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .system-header {
            text-align: center;
            padding: 60px 0 40px;
            color: white;
        }

        .system-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #fff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .system-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 40px;
        }

        .dino-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .modules-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            border: none;
            height: 100%;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .module-card:hover::before {
            left: 100%;
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .module-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: var(--bg-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .module-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .module-description {
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .module-btn {
            background: var(--bg-gradient);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
        }

        .module-btn:hover {
            transform: scale(1.05);
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .coming-soon {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .coming-soon .module-btn {
            background: #6c757d;
            cursor: not-allowed;
        }

        .coming-soon .module-btn:hover {
            transform: none;
            box-shadow: none;
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .stats-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin: 40px auto;
            max-width: 800px;
        }

        .stat-item {
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .system-title {
                font-size: 2.5rem;
            }
            
            .module-card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 系统头部 -->
    <div class="system-header">
        <div class="container">
            <div class="dino-icon">🦕</div>
            <h1 class="system-title">小恐龙的超算单元</h1>
            <p class="system-subtitle">集成化智能计算平台 · 多功能数据处理中心</p>
            
            <!-- 系统统计 -->
            <div class="stats-bar">
                <div class="row">
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">2</span>
                            <span class="stat-label">已部署模块</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">3</span>
                            <span class="stat-label">计划模块</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">在线服务</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-item">
                            <span class="stat-number">∞</span>
                            <span class="stat-label">计算能力</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能模块 -->
    <div class="modules-container">
        <div class="row g-4">
            <!-- 数据中心模块 -->
            <div class="col-lg-4 col-md-6">
                <div class="module-card" onclick="window.location.href='/datacenter'">
                    <div class="module-icon">
                        <i class="bi bi-database-fill"></i>
                    </div>
                    <h3 class="module-title">数据中心</h3>
                    <p class="module-description">
                        气象数据管理系统，支持站点管理、观测数据导入导出、数据可视化分析等功能
                    </p>
                    <a href="/datacenter" class="module-btn">
                        <i class="bi bi-arrow-right me-2"></i>进入数据中心
                    </a>
                </div>
            </div>

            <!-- 计算中心模块 -->
            <div class="col-lg-4 col-md-6">
                <div class="module-card" onclick="window.location.href='/compute'">
                    <div class="module-icon">
                        <i class="bi bi-cpu-fill"></i>
                    </div>
                    <h3 class="module-title">计算中心</h3>
                    <p class="module-description">
                        高性能气象计算平台，提供华西秋雨分析、气候变化检测等专业计算服务
                    </p>
                    <a href="/compute" class="module-btn">
                        <i class="bi bi-arrow-right me-2"></i>进入计算中心
                    </a>
                </div>
            </div>

            <!-- 监控中心模块 (即将推出) -->
            <div class="col-lg-4 col-md-6">
                <div class="module-card coming-soon">
                    <div class="module-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h3 class="module-title">监控中心</h3>
                    <p class="module-description">
                        系统监控与运维管理，实时监控系统状态、性能指标和资源使用情况
                    </p>
                    <span class="module-btn">
                        <i class="bi bi-clock me-2"></i>即将推出
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div class="footer">
        <div class="container">
            <p>&copy; 2025 小恐龙的超算单元 · 让计算更简单，让数据更智能</p>
            <p><small>Powered by Flask & Bootstrap · Made with 🦕 by Little Dino</small></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为模块卡片添加点击效果
            const moduleCards = document.querySelectorAll('.module-card:not(.coming-soon)');
            moduleCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 添加点击动画
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 统计数字动画
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                if (!isNaN(finalValue) && finalValue !== '∞') {
                    let currentValue = 0;
                    const increment = finalValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            stat.textContent = Math.floor(currentValue);
                        }
                    }, 30);
                }
            });
        });
    </script>
</body>
</html>
