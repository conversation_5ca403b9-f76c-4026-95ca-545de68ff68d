import pandas as pd
import numpy as np
from scipy.optimize import fsolve

day_path = r'D:\python\Heating_and_ventilation\data\RHU_Tem_day.xlsx'
mon_path = r'D:\python\Heating_and_ventilation\data\RHU_Tem_month.xlsx'
day_data = pd.read_excel(day_path)
mon_data = pd.read_excel(mon_path)

# Convert date columns to datetime at the start
day_data['资料时间'] = pd.to_datetime(day_data['资料时间'], errors='coerce')
mon_data['资料时间'] = pd.to_datetime(mon_data['资料时间'], errors='coerce')

# 冬季空调室外计算温度
def calculate_winter_air_conditioner_outside_temperature(method):
    """
        twk=0.30t1p+0.70tp.min
    式中:twk——冬季空调室外计算温度(℃)
         t1p——累年最冷月平均气温(℃)
         tp·min——累年最低日平均气温(℃)
    """
    if method == 0:
        # 获取平均气温最小值所在的资料时间
        min_temp_index = mon_data['平均气温'].idxmin()
        # 获取对应的资料时间
        min_temp_date = mon_data.loc[min_temp_index, '资料时间']
        # 获取月份
        min_temp_month = min_temp_date.month
        # 获取平均气温最小值所在月的平均气温
        t1p = day_data[day_data['资料时间'].dt.month == min_temp_month]['平均气温'].mean()
        tp_min = day_data['平均气温'].min()
    elif method == 1:
        # 获取平均气温最小值所在的资料时间
        min_temp_index = mon_data['平均气温'].idxmin()
        # 获取对应的资料时间
        min_temp_date = mon_data.loc[min_temp_index, '资料时间']
        # 获取月份
        min_temp_month = min_temp_date.month
        # 获取平均气温最小值所在月的平均气温
        t1p = mon_data[mon_data['资料时间'].dt.month == min_temp_month]['平均气温'].mean()
        tp_min = day_data['平均气温'].min()
    
    twk = 0.30 * t1p + 0.70 * tp_min
    return twk

# 冬季空调室外计算相对湿度
def calculate_winter_air_conditioner_outside_relative_humidity():
    # 获取平均气温最小值所在的资料时间
    min_temp_index = mon_data['平均气温'].idxmin()
    # 获取对应的月份
    min_temp_month = mon_data.loc[min_temp_index, '资料时间'].month
    # 获取平均气温最小值所在月的平均相对湿度
    rh = mon_data[mon_data['资料时间'].dt.month == min_temp_month]['平均相对湿度'].mean()
    return rh

# 夏季通风室外计算温度
def calculate_summer_ventilation_outside_temperature(method):
    """
    twf=0.71trp+0.29tmax
    式中:twf——夏季通风室外计算温度(℃)
         t1p——累年最热月平均温度(℃)
         tmax——累年极端最高温度(℃)
    """
    if method == 0:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmin()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取平均气温最大值所在月的平均气温
        t1p = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        tmax = day_data['最高气温'].max()
    elif method == 1:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmin()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取平均气温最大值所在月的平均气温
        t1p = mon_data[mon_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        tmax = day_data['最高气温'].max()

    twf = 0.71 * t1p + 0.29 * tmax
    return twf

# 夏季通风室外计算相对湿度
def calculate_summer_ventilation_outside_relative_humidity():
    # 获取平均气温最大值所在的资料时间
    max_temp_index = mon_data['平均气温'].idxmax()
    # 获取对应的月份
    max_temp_month = mon_data.loc[max_temp_index, '资料时间'].month
    # 获取平均气温最大值所在月的平均相对湿度
    rh = mon_data[mon_data['资料时间'].dt.month == max_temp_month]['平均相对湿度'].mean()
    return rh

# 夏季空气调节室外计算干球温度
def calculate_summer_air_tuning_outside_dry_ball_temperature(method):
    """
     twg= 0.71trp+0.29tmax 
    式中:twg——夏季空气调节室外计算干球温度(℃)
         trp——累年最热月平均温度(℃)
         tmax——累年极端最高温度(℃)
    """
    if method == 0:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmax()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取平均气温最大值所在月的平均气温
        trp = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        tmax = day_data['最高气温'].max()
    elif method == 1:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmax()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取平均气温最大值所在月的平均气温
        trp = mon_data[mon_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        tmax = day_data['最高气温'].max()
    
    twg = 0.71 * trp + 0.29 * tmax
    return twg

# 湿球温度计算相关函数
def mt_e(t, rh):
    ew = mt_ew(t)
    ee = rh * ew / 100.
    return ee

def mt_ew(t):
    T = t + 273.15
    T1 = 273.16
    logew = 10.79574 * (1 - T1 / T) - 5.028 * np.log10(T / T1) + 1.50475 * (1e-4) * (
            1 - np.power(10, -1 * 8.2969 * (T / T1 - 1))) \
            + 0.42873 * (1e-3) * (np.power(10, 4.76955 * (1 - T1 / T)) - 1) + 0.78614
    ew = np.power(10, logew)
    return ew

def mt_sqwd(t, rh, P, iswater=True, A=0.7947e-3):
    ee = mt_e(t, rh)
    T1 = 273.16

    def fun1(tw):
        if iswater:
            equ = ee - (np.power(10, 10.79574 * (1 - T1 / (tw + 273.15)) - 5.028 * np.log10(
                (tw + 273.15) / T1) + 1.50475 * (1e-4) * (1 - np.power(10, -1 * 8.2969 * ((tw + 273.15) / T1 - 1))) \
                                 + 0.42873 * (1e-3) * (np.power(10, 4.76955 * (
                    1 - T1 / (tw + 273.15))) - 1) + 0.78614) - A * P * (t - tw))
        else:
            equ = ee - (np.power(10, -9.09685 * (T1 / (tw + 273.15 - 1)) - 3.56654 * np.log10(
                T1 / (tw + 273.15)) + 0.87682 * (1 - T1 / (tw + 273.15 - 1)) + 0.78614) - A * P * (t - tw))
        return equ

    sqwd = fsolve(fun1, t)
    return sqwd[0]

def calculate_summer_air_tuning_outside_wet_ball_temperature(method):
    """
     tws = 0.80tsrp+0.20tsmax
    式中:tws——夏季空气调节室外计算湿球温度(℃)
         tsrp——与累年最热月平均温度和平均相对温度相对应的湿球温度
         tsmax——与累年极端最高温度和最热月平均相对湿度相对应的湿球温度
    """
    if method == 0:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmax()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取累年最热月的平均气温和相对湿度
        t_avg = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        rh_avg = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均相对湿度'].mean()
        # 获取气压
        P = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均气压'].mean()
    elif method == 1:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmax()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取累年最热月的平均气温和相对湿度
        t_avg = mon_data[mon_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        rh_avg = mon_data[mon_data['资料时间'].dt.month == max_temp_month]['平均相对湿度'].mean()
        # 获取气压
        P = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均气压'].mean()
    
    # 计算湿球温度
    tsrp = mt_sqwd(t_avg, rh_avg, P)    
    # 获取极端最高温度
    t_max = day_data['最高气温'].max()
    # 计算湿球温度
    tsmax = mt_sqwd(t_max, rh_avg, P)
    tws = 0.80 * tsrp + 0.20 * tsmax
    return tws

# 夏季空气调节室外计算日平均气温
def calculate_summer_air_tuning_outside_daily_average_temperature(method):
    """
    twp = 0.80trp+0.20tmax
    式中:twp——夏季空气调节室外计算日平均气温(℃)
         trp——累年最热月平均温度(℃)
         tmax——累年极端最高温度(℃)
    """
    if method == 0:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmax()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取平均气温最大值所在月的平均气温
        trp = day_data[day_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        tmax = day_data['最高气温'].max()
    elif method == 1:
        # 获取平均气温最大值所在的资料时间
        max_temp_index = mon_data['平均气温'].idxmax()
        # 获取对应的资料时间
        max_temp_date = mon_data.loc[max_temp_index, '资料时间']
        # 获取月份
        max_temp_month = max_temp_date.month
        # 获取平均气温最大值所在月的平均气温
        trp = mon_data[mon_data['资料时间'].dt.month == max_temp_month]['平均气温'].mean()
        tmax = day_data['最高气温'].max()
    
    twp = 0.80 * trp + 0.20 * tmax
    return twp

# 供暖室外计算温度
def calculate_heating_outside_temperature(method):
    '''
         twn=0.57t1p+0.43tp.min
    式中:twn——供暖室外计算温度(℃)
         t1p——累年最冷月平均温度(℃)
         tp·min——累年最低日平均温度(℃)
    '''
    if method == 0:
        # 获取平均气温最小值所在的资料时间
        min_temp_index = mon_data['平均气温'].idxmin()
        # 获取对应的资料时间
        min_temp_date = mon_data.loc[min_temp_index, '资料时间']
        # 获取月份
        min_temp_month = min_temp_date.month
        # 获取平均气温最小值所在月的平均气温
        t1p = day_data[day_data['资料时间'].dt.month == min_temp_month]['平均气温'].mean()
        tp_min = day_data['平均气温'].min()
    elif method == 1:
        # 获取平均气温最小值所在的资料时间
        min_temp_index = mon_data['平均气温'].idxmin()
        # 获取对应的资料时间
        min_temp_date = mon_data.loc[min_temp_index, '资料时间']
        # 获取月份
        min_temp_month = min_temp_date.month
        # 获取平均气温最小值所在月的平均气温
        t1p = mon_data[mon_data['资料时间'].dt.month == min_temp_month]['平均气温'].mean()
        tp_min = day_data['平均气温'].min()
    
    twn = 0.57 * t1p + 0.43 * tp_min
    return twn

# 夏季空调室外计算逐时温度
def calculate_summer_air_conditioner_outside_hourly_temperature(i, method=0):
    """
    tsh = twp + beta * delta_tr
    delta_tr = (twg-twp)/0.52
    式中:tsh——夏季空调室外计算逐时温度(℃)
         twp——夏季空气调节室外计算日平均气温(℃)
         beta——室外温度逐小时变化系数
         delta_tr——夏季室外计算平均日较差
         twg——夏季空气调节室外计算干球温度(℃)
    """
    # 获取夏季空气调节室外计算日平均气温
    twp = calculate_summer_air_tuning_outside_daily_average_temperature(method)
    # 获取夏季空气调节室外计算干球温度
    twg = calculate_summer_air_tuning_outside_dry_ball_temperature(method)
    # 计算夏季室外计算平均日较差
    delta_tr = (twg-twp)/0.52
    # 室外温度逐小时变化系数
    beta = [-0.35,-0.38,-0.42,-0.45,-0.47,-0.41,-0.28,-0.12, 0.03, 0.16, 0.29, 0.40, 0.48, 0.52,
             0.51, 0.43, 0.39, 0.28, 0.14, 0.00, -0.10, -0.17, -0.23, -0.26]
    # 计算夏季空调室外计算逐时温度
    tsh = twp + beta[i] * delta_tr
    return tsh

if __name__ == '__main__':
    method = 1
    print("冬季空调室外计算温度：{:.2f}".format(calculate_winter_air_conditioner_outside_temperature(method)))
    print("冬季空调室外计算相对湿度:{:.2f}".format(calculate_winter_air_conditioner_outside_relative_humidity()))
    print("夏季通风室外计算温度:{:.2f}".format(calculate_summer_ventilation_outside_temperature(method)))
    print("夏季通风室外计算相对湿度:{:.2f}".format(calculate_summer_ventilation_outside_relative_humidity()))
    print("夏季空气调节室外计算干球温度:{:.2f}".format(calculate_summer_air_tuning_outside_dry_ball_temperature(method)))
    tws = float(calculate_summer_air_tuning_outside_wet_ball_temperature(method))
    print("夏季空气调节室外计算湿球温度:{:.2f}".format(tws))
    print("夏季空气调节室外计算日平均气温:{:.2f}".format(calculate_summer_air_tuning_outside_daily_average_temperature(method)))
    print("供暖室外计算温度:{:.2f}".format(calculate_heating_outside_temperature(method)))
    for i in range(24):
        print("夏季空调室外计算逐时温度：{:.2f}".format(calculate_summer_air_conditioner_outside_hourly_temperature(i)))
