# -*- coding: utf-8 -*-
"""
Web应用配置文件
"""

import os

class Config:
    """基础配置"""
    
    # 高德地图API配置
    # 请到 https://console.amap.com/ 申请API Key
    AMAP_API_KEY = os.environ.get('AMAP_API_KEY', '73459635cc813da05f3b558ce10f5d25')
    
    # 文件上传配置
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 16MB
    
    # 数据库配置
    DATABASE_PATH = '../weather_stations.db'
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY', '1b29f1564d9489ebafedf262045203345ce4dd23ad94399ba27dd0f42e1e977e')
    DEBUG = True

# 使用说明：
# 1. 访问 https://console.amap.com/ 注册账号
# 2. 创建应用获取API Key
# 3. 将API Key替换上面的 '您的高德地图API_KEY'
# 4. 或者设置环境变量: export AMAP_API_KEY=your_actual_key
