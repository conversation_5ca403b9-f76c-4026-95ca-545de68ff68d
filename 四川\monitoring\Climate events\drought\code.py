#In[0]: 数据加载
import pandas as pd

file_path = r'D:\python\四川\monitoring\Climate events\drought\data\T_DPW_CP_RINDEX_202505201004.xlsx'
data = pd.read_excel(file_path)
data = data.fillna(0)
station_ids = data['V01000'].unique()  
data['V04000'] = pd.to_datetime(data['V04000'], format='%Y%m%d')
data = data[data['V04000'] > '2023-12-31']


# %%
# 判断无旱、轻旱、中旱、重旱、特旱(返回整数分类)
def classify_drought_intensity(mci_value):
    if -0.5 < mci_value:
        return 0
    elif -1.0 < mci_value <= -0.5:
        return 1
    elif -1.5 < mci_value <= -1.0:
        return 2
    elif -2.0 < mci_value <= -1.5:
        return 3
    else:
        return 4
    
# 应用函数到 MCI 列
data['MCI_Class'] = data['MCI'].apply(classify_drought_intensity).astype(int)


# --------------------MCI指数--------------------
#%%四川省单站逐时段MCI指数统计表
df = pd.DataFrame(columns=['站号', '时段', 'MCI指数', '干旱等级'])
# 干旱等级对应字典
drought_levels = {0: '无旱', 1: '轻旱', 2: '中旱', 3: '重旱', 4: '特旱'}

df['站号'] = data['V01000']
df['时段'] = data['V04000']
df['MCI指数'] = data['MCI']
df['干旱等级'] = data['MCI_Class'].map(drought_levels)
df.to_excel(r'D:\python\四川\monitoring\Climate events\drought\data\四川省单站逐时段MCI指数统计表.xlsx', index=False)

#%%四川省逐日单站干旱日数统计表
all_station_data = []

for station_id in station_ids:
    station_data = data[data['V01000'] == station_id]
    
    # 为每个站点创建临时DataFrame，添加index参数
    temp_df = pd.DataFrame({
        '站号': [station_id],  # 改为列表形式
        '时段累计无旱日数': [station_data[station_data['MCI_Class'] == 0].shape[0]],
        '时段累计轻旱日数': [station_data[station_data['MCI_Class'] == 1].shape[0]],
        '时段累计中旱日数': [station_data[station_data['MCI_Class'] == 2].shape[0]],
        '时段累计重旱日数': [station_data[station_data['MCI_Class'] == 3].shape[0]],
        '时段累计特旱日数': [station_data[station_data['MCI_Class'] == 4].shape[0]],
        '时段累计中旱及以上日数': [station_data[station_data['MCI_Class'] >= 2].shape[0]],
        '时段累计重旱及以上日数': [station_data[station_data['MCI_Class'] >= 3].shape[0]]
    }, index=[0])  # 添加index参数
    
    all_station_data.append(temp_df)

# 合并所有站点数据
df1 = pd.concat(all_station_data, ignore_index=True)

df1.to_excel(r'D:\python\四川\monitoring\Climate events\drought\data\四川省逐日单站干旱日数统计表.xlsx', index=False)

#%% 四川省逐日干旱站数统计表

all_station_data = []

for time in data['V04000'].unique():
    time_data = data[data['V04000'] == time]

    # 为每个时间创建临时DataFrame，添加index参数
    temp_df = pd.DataFrame({
       '区域':['四川省'],
        '时段': [time],  
        '无旱站数': [time_data[time_data['MCI_Class'] == 0]['V01000'].nunique()],
        '轻旱站数': [time_data[time_data['MCI_Class'] == 1]['V01000'].nunique()],
        '中旱站数': [time_data[time_data['MCI_Class'] == 2]['V01000'].nunique()],
        '重旱站数': [time_data[time_data['MCI_Class'] == 3]['V01000'].nunique()],
        '特旱站数': [time_data[time_data['MCI_Class'] == 4]['V01000'].nunique()],
        '中旱及以上站数': [time_data[time_data['MCI_Class'] >= 2]['V01000'].nunique()],
        '重旱及以上站数': [time_data[time_data['MCI_Class'] >= 3]['V01000'].nunique()]
    })
    all_station_data.append(temp_df)

# 合并所有站点数据
df2 = pd.concat(all_station_data, ignore_index=True)

df2.to_excel(r'D:\python\四川\monitoring\Climate events\drought\data\四川省逐日干旱站数统计表.xlsx', index=False)



# --------------------单站过程--------------------
'''
当某站连续15天及以上出现轻旱及以上等级干旱,且
至少有一天干旱等级达到中旱及以上，则确定为发生一次干旱过程。干旱过程时
段内第一次出现轻旱的日期,为干旱过程开始日。干旱过程发生后,当连续5天
干旱等级为无旱等级，则干旱过程结束，干旱过程结束前最后一天干旱
等级为轻旱或以上的日期为干旱过程结束日。某站干旱过程开始日到结束日（含
结束日）的总天数为某站干旱过程日数。
'''
# %% 

# 定义函数来查找初始段
def find_initial_segments(station_data):
    segments = []
    current_segment = []
    current_has_severe = False
    consecutive_non_drought = 0
    valid_length = 0  # 有效干旱长度计数器
    
    for idx, row in station_data.iterrows():
        mci_class = row['MCI_Class']
        date = row['V04000']
        
        if mci_class >= 1:
            current_segment.append(date)
            if mci_class >= 2:
                current_has_severe = True
            valid_length += 1  # 只有干旱日才累计有效长度
            
            # 当有效长度达到15天后，允许最多4天无旱间断
            if valid_length >= 15:
                consecutive_non_drought = 0  # 重置无旱计数器
                
        else:
            # 仅当有效长度≥15天后才允许无旱间断
            if valid_length >= 15:
                consecutive_non_drought += 1
                
                # 当累计无旱超过4天时结束当前段
                if consecutive_non_drought > 4:
                    if current_has_severe:
                        segments.append({
                            'start': current_segment[0],
                            'end': current_segment[-1]
                        })
                    # 重置计数器但保留最后4天无旱日
                    current_segment = current_segment[-4:] if len(current_segment) >=4 else []
                    valid_length = 0
                    current_has_severe = False
                    consecutive_non_drought = 0
            else:
                # 未达到15天时遇到无旱直接重置
                current_segment = []
                valid_length = 0
                current_has_severe = False

    # 处理最后一个有效段
    if valid_length >= 15 and current_has_severe:
        segments.append({
            'start': current_segment[0],
            'end': current_segment[-1]
        })
    
    return segments

# 计算累计干旱强度
def calculate_cumulative_drought_intensity(mci_values):
    n = len(mci_values)
    a = 0.5
    if n == 0:
        return 0
    return (n ** (a - 1)) * abs(mci_values.sum())

# 计算过程强度
def calculate_drought_process_intensity(mci_series, date_series):
    max_zs = 0
    max_start = None
    max_end = None
    m = len(mci_series)
    
    for i in range(m):
        for j in range(i + 1, m + 1):
            subset = mci_series.iloc[i:j]
            current_zs = calculate_cumulative_drought_intensity(subset)
            
            # 更新最大值及对应时段
            if current_zs > max_zs or (current_zs == max_zs and (max_end - max_start) < (j - i)):
                max_zs = current_zs
                max_start = date_series.iloc[i]
                max_end = date_series.iloc[j-1]
    
    return max_zs, max_start, max_end

# 处理每个站点，统计干旱过程
process_list = []

for station_id in station_ids:
    # 获取站点数据并按时间排序
    station_data = data[data['V01000'] == station_id].sort_values('V04000')
    if len(station_data) == 0:
        continue
    
    # 查找所有符合条件的初始段
    segments = find_initial_segments(station_data)
    
    for seg in segments:
        start_date = seg['start']
        # 获取从start_date开始的数据
        mask = (station_data['V04000'] >= start_date)
        process_data = station_data.loc[mask]
        
        end_date = None
        consecutive_non_drought = 0
        last_drought_day = seg['end']  # 初始段的最后一天
        
        # 遍历从start_date开始的数据，寻找连续5天无旱
        for idx, row in process_data.iterrows():
            current_date = row['V04000']
            mci_class = row['MCI_Class']
            
            if mci_class >= 1:
                consecutive_non_drought = 0
                last_drought_day = current_date
            else:
                consecutive_non_drought += 1
                if consecutive_non_drought == 5:
                    end_date = last_drought_day
                    break  # 找到结束日期，退出循环
        
        # 如果找到结束日期
        if end_date is not None:
            # 计算持续时间
            duration = (end_date - start_date).days + 1
            # 筛选该时间段内的数据
            mask_duration = (process_data['V04000'] >= start_date) & (process_data['V04000'] <= end_date)
            process_days = process_data.loc[mask_duration]
            
            # 统计各等级天数
            class_counts = process_days['MCI_Class'].value_counts().reindex([0,1,2,3,4], fill_value=0)
            no_drought = class_counts.get(0, 0)
            light_drought = class_counts.get(1, 0)
            moderate_drought = class_counts.get(2, 0)
            severe_drought = class_counts.get(3, 0)
            extreme_drought = class_counts.get(4, 0)
            
            # 计算累计干旱强度和过程强度
            mci_values = process_days.loc[process_days['MCI_Class'] >= 1, 'MCI']
            date_series = process_days.loc[process_days['MCI_Class'] >= 1, 'V04000']
            cumulative_intensity = calculate_cumulative_drought_intensity(mci_values)
            process_intensity, max_zs_start, max_zs_end = calculate_drought_process_intensity(mci_values, date_series)
            
            # 添加过程信息
            process_list.append({
                '站号': station_id,
                '过程开始日期': start_date.strftime('%Y%m%d'),
                '过程结束日期': end_date.strftime('%Y%m%d'),
                '过程持续时间': duration,
                '累计干旱强度': cumulative_intensity,
                '干旱过程强度': process_intensity,
                '最大累计强度开始时间': max_zs_start.strftime('%Y%m%d') if max_zs_start else None,
                '最大累计强度结束时间': max_zs_end.strftime('%Y%m%d') if max_zs_end else None,
                '无旱日数': no_drought,
                '轻旱日数': light_drought,
                '中旱日数': moderate_drought,
                '重旱日数': severe_drought,
                '特旱日数': extreme_drought,
            })

# 转换为DataFrame并保存
if process_list:
    df_process = pd.DataFrame(process_list)
    df_process.to_excel(r'D:\python\四川\monitoring\Climate events\drought\data\四川省单站干旱过程统计表.xlsx', index=False)
else:
    print("没有找到符合条件的干旱过程。")




#%% --------------------区域过程--------------------
import numpy as np
from tqdm import tqdm

station_info = pd.read_excel(r'D:\python\四川\monitoring\Climate events\drought\data\station_info.xlsx')
stations = station_info[['站号', '纬度', '经度']].values

# 经纬度计算距离
def distance(lat1, lon1, lat2, lon2):
    import math
    EARTH_RADIUS = 6371.0
    def to_radians(degree):
        return degree * math.pi / 180.0
    def calculate_distance(lat1, lon1, lat2, lon2):
        d_lat = to_radians(lat2 - lat1)
        d_lon = to_radians(lon2 - lon1)
        a = math.sin(d_lat / 2) ** 2 + math.cos(to_radians(lat1)) * math.cos(to_radians(lat2)) * math.sin(d_lon / 2) ** 2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        return EARTH_RADIUS * c
    return calculate_distance(lat1, lon1, lat2, lon2)


# 计算所有站点之间的距离矩阵
print("正在计算站点间距离...")
distance_matrix = np.zeros((len(stations), len(stations)))
for i, (id1, lat1, lon1) in tqdm(enumerate(stations)):
    for j, (id2, lat2, lon2) in enumerate(stations):
        if i < j:
            dist = distance(lat1, lon1, lat2, lon2)
            distance_matrix[i][j] = dist
            distance_matrix[j][i] = dist

# 构建相邻站点字典（200km）
adjacent_stations = {}
threshold = 200 
for i, (id1, _, _) in enumerate(stations):
    adjacent = stations[distance_matrix[i] <= threshold, 0].tolist()
    adjacent.remove(id1)
    adjacent_stations[id1] = adjacent

print("相邻站点构建完成。")



# 2. 构建监测区域（所有站点组成一个大区域）
all_stations = station_info['站号'].unique().tolist()
print(f"监测区域包含{len(all_stations)}个站点。")

# 3. 预处理干旱数据
print('预处理干旱数据...')
data_regional = data[['V01000', 'V04000', 'MCI', 'MCI_Class']].copy()
data_regional['中旱及以上'] = data_regional['MCI_Class'] >= 2
print("数据预处理完成。")

# 4. 计算每日区域干旱情况（修复版）
def find_drought_clusters(drought_stations, adjacent_stations):
    """使用深度优先搜索找到所有连通的干旱站点群组"""
    visited = set()
    clusters = []

    def dfs(station, current_cluster):
        """深度优先搜索，找到连通的干旱站点"""
        if station in visited or station not in drought_stations:
            return
        visited.add(station)
        current_cluster.add(station)

        # 遍历所有相邻站点
        for neighbor in adjacent_stations.get(station, []):
            if neighbor in drought_stations and neighbor not in visited:
                dfs(neighbor, current_cluster)

    # 对每个未访问的干旱站点进行DFS
    for station in drought_stations:
        if station not in visited:
            cluster = set()
            dfs(station, cluster)
            if len(cluster) >= 2:  # 至少2个相邻站点才算群组
                clusters.append(list(cluster))
            # 注意：孤立的单个干旱站点不会被加入clusters

    return clusters

print("开始计算每日区域干旱情况...")
daily_stats = []
for date in tqdm(data_regional['V04000'].unique()):
    daily_data = data_regional[data_regional['V04000'] == date]

    # 获取当日有中旱以上的站点
    drought_stations = daily_data[daily_data['中旱及以上']]['V01000'].unique().tolist()

    # 找到所有连通的干旱站点群组
    drought_clusters = find_drought_clusters(drought_stations, adjacent_stations)

    # 选择最大的群组作为代表
    if drought_clusters:
        best_group = max(drought_clusters, key=len)
        cluster_count = len(drought_clusters)
    else:
        best_group = []
        cluster_count = 0

    # 判断是否达到5%阈值
    is_regional_day = len(best_group) / len(all_stations) >= 0.05

    daily_stats.append({
        '日期': date,
        '干旱站点数': len(drought_stations),
        '有效群组站点数': len(best_group),
        '群组总数': cluster_count,
        '是否区域干旱日': is_regional_day,
        '干旱站点列表': best_group,
        '所有群组': drought_clusters  # 保存所有群组信息，便于调试
    })

daily_df = pd.DataFrame(daily_stats)
print("每日区域干旱情况计算完成。")
print(f"总共处理了 {len(daily_df)} 天的数据")
print(f"其中区域干旱日有 {daily_df['是否区域干旱日'].sum()} 天")



# 5. 区域性干旱过程识别
# 先计算每日必要指标
daily_df['站点比例'] = daily_df['有效群组站点数'] / len(all_stations)
daily_df['前一天站点列表'] = daily_df['干旱站点列表'].shift(1)

# 计算每日站点重合率
def calculate_overlap(row):
    if not row['前一天站点列表'] or len(row['前一天站点列表']) == 0:
        return 0.0
    # 添加当前站点列表空值检查
    if not row['干旱站点列表'] or len(row['干旱站点列表']) == 0:
        return 0.0
    prev = set(row['前一天站点列表'])
    current = set(row['干旱站点列表'])
    return len(prev & current) / len(current)

daily_df['站点重合率'] = daily_df.apply(calculate_overlap, axis=1)

# 过程识别参数
END_CONDITION_DAYS = 5        
SITE_PERCENT_THRESH = 0.05   
OVERLAP_THRESH = 0.5
START_CONSECUTIVE_DAYS = 15    

processes = []
current_process = None
end_condition_counter = 0
start_condition_counter = 0

for i in tqdm(range(len(daily_df))):
    row = daily_df.iloc[i]
    
    # 过程开始检测
    if current_process is None:
        if row['是否区域干旱日'] and row['站点重合率'] >= OVERLAP_THRESH:
            start_condition_counter += 1
            if start_condition_counter >= START_CONSECUTIVE_DAYS:
                start_date_idx = max(0, i - (START_CONSECUTIVE_DAYS-1))
                start_date = daily_df.iloc[start_date_idx]['日期']
            # 开始新过程
                current_process = {
                    'start_date': start_date,
                    'end_date': start_date,
                    'consecutive_days': 1,
                    'active_days': 1,
                    'current_stations': set(row['干旱站点列表'])
                }
        else:
            start_condition_counter = 0
        continue
    
    # 过程持续监测
    else:
        current_process['end_date'] = row['日期']
        current_process['consecutive_days'] += 1
        
        # 检测结束条件
        condition1 = row['站点比例'] < SITE_PERCENT_THRESH
        condition2 = row['站点重合率'] < OVERLAP_THRESH
        
        # 满足任意结束条件时计数
        if condition1 or condition2:
            end_condition_counter += 1
        else:
            end_condition_counter = 0  # 重置计数器
            
        # 达到结束条件持续天数
        if end_condition_counter >= END_CONDITION_DAYS:
            # 确定过程结束日为满足条件的前一天
            end_date_index = max(0, i - END_CONDITION_DAYS)
            end_date = daily_df.iloc[end_date_index]['日期']
            
            # 记录有效过程（持续时间需包含有效时段）
            if current_process['consecutive_days'] >= 15:
                processes.append({
                    'start_date': current_process['start_date'].strftime('%Y%m%d'),
                    'end_date': end_date.strftime('%Y%m%d'),
                    'duration': (end_date - current_process['start_date']).days + 1,
                    'trigger_condition': '站点比例不足' if condition1 else '重合率不足'
                })
            
            # 重置状态
            current_process = None
            end_condition_counter = 0

# 处理最后一个未结束的过程
if current_process and current_process['consecutive_days'] >= 15:
    processes.append({
        'start_date': current_process['start_date'].strftime('%Y%m%d'),
        'end_date': current_process['end_date'].strftime('%Y%m%d'),
        'duration': (current_process['end_date'] - current_process['start_date']).days + 1,
        'trigger_condition': '自然结束'
    })

#  6. 过程强度计算
def calculate_process_intensity(start_date, end_date, stations):
    mask = (data_regional['V04000'] >= start_date) & \
           (data_regional['V04000'] <= end_date)
    
    # 影响范围（逐日达到中旱及以上站点数的平均值）
    A = daily_df[(daily_df['日期'] >= start_date) & 
                (daily_df['日期'] <= end_date)]['干旱站点数'].mean()
    
    # 干旱强度（MCI绝对值平均）
    process_data = data_regional[mask & data_regional['V01000'].isin(stations)]
    I = process_data['MCI'].abs().mean()
    
    # 持续时间
    T = (end_date - start_date).days + 1
    
    return I * np.sqrt(A/T) * np.sqrt(T)  # 强度Z

# 为每个过程补充完整信息
for process in processes:
    start = pd.to_datetime(process['start_date'])
    end = pd.to_datetime(process['end_date'])
    
    # 获取过程期间所有干旱站点
    mask = (daily_df['日期'] >= start) & (daily_df['日期'] <= end)
    all_stations = set()
    daily_df[mask]['干旱站点列表'].apply(lambda x: all_stations.update(x))
    
    process['影响站数'] = len(all_stations)
    process['强度Z'] = calculate_process_intensity(start, end, all_stations)


# 7. 结果输出
output_columns = [
    'start_date', 'end_date', 'duration', '影响站数','强度Z'
]
df_region_process = pd.DataFrame(processes)[output_columns]

df_region_process.columns = [
    '开始日期', '结束日期', '持续天数', '影响站点数',
    '过程强度Z'
]

# 保存结果
df_region_process.to_csv(
    r'D:\python\四川\monitoring\Climate events\drought\data\四川省区域性干旱过程统计表.csv',
    index=False
)

print("区域性干旱过程分析完成！生成过程记录{}条".format(len(df_region_process)))
print(df_region_process)

# %%
