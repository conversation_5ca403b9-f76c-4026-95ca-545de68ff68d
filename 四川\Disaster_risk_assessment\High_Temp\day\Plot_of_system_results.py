from matplotlib.colors import ListedColormap, BoundaryNorm
import numpy as np
import rasterio
import matplotlib.pyplot as plt



with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\tif\assess_level.tif') as src:
    data = src.read(1)  
    meta = src.meta     
    print(meta)
    # 创建一个与原始数据相同大小的新数组，初始值为np.nan
    data[data == -999] = np.nan


    # 定义风险等级对应的颜色
    colors = ['#FCDAD5', '#F5A89A', '#EE7C6B', '#DF0029', '#B2001F']
    cmap = ListedColormap(colors)

    # 定义边界
    boundaries = [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]
    norm = BoundaryNorm(boundaries, cmap.N)

    plt.imshow(data, cmap=cmap, norm=norm)
    plt.colorbar(ticks=[1, 2, 3, 4, 5],
                 label='Risk Level')
    plt.title('Risk Level')
    plt.savefig(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\photo\Risk Level system.png', dpi=400)
    plt.show()



with rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\tif\hazard_level.tif') as src:
    data = src.read(1)  
    meta = src.meta     
    #print(meta)
    data[data == -999] = np.nan


    # 定义风险等级对应的颜色
    colors = ['#FFFF00', '#FFB226', '#FF7333', '#CC2600', '#A31447']
    cmap = ListedColormap(colors)

    # 定义边界
    boundaries = [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]
    norm = BoundaryNorm(boundaries, cmap.N)

    plt.imshow(data, cmap=cmap, norm=norm)
    plt.colorbar(ticks=[1, 2, 3, 4, 5],
                 label='Hazard Level')
    plt.title('Hazard Level')
    plt.savefig(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\photo\Hazard Level system.png', dpi=400)
    plt.show()