# 气象站点数据库管理系统 - Web版使用说明

## 🚀 快速开始

### 启动Web应用
```bash
cd web_app
python app.py
```

然后在浏览器中访问：`http://localhost:5000`

## 📋 主要功能

### 1. 站点管理
- **浏览站点列表**: 在首页点击"浏览站点"查看所有站点
- **查看站点详情**: 点击站点名称查看详细信息和地图位置
- **导入站点数据**: 在首页点击"导入站点信息"上传CSV或Excel文件

### 2. 观测数据管理
- **查看观测数据**: 在站点详情页面查看该站点的观测记录
- **导入观测数据**: 在首页点击"导入观测数据"上传CSV或Excel文件
- **数据筛选**: 支持按省份筛选站点

### 3. 数据导入格式

#### 站点数据CSV格式示例：
```csv
区站号,站名,纬度,经度,测站高度,省名,地市
54511,北京,39.9042,116.4074,55.0,北京市,北京市
```

#### 观测数据CSV格式示例：
```csv
区站号,资料时间,气温,湿度,降水量,风速
54511,2024-01-01 12:00:00,15.5,65.0,0.0,2.5
```

## 🌐 Web界面功能

### 首页功能卡片
- **站点管理**: 浏览和管理气象站点
- **数据导入**: 批量导入站点和观测数据
- **观测数据**: 查看和分析气象观测记录

### 站点列表页面
- 显示所有站点的基本信息
- 支持按省份筛选
- 显示每个站点的观测数据数量
- 点击站点名称查看详情

### 站点详情页面
- **高德地图显示**: 精确显示站点地理位置
- **站点信息卡片**: 详细的站点基本信息
- **观测数据表格**: 该站点的历史观测记录
- **数据导出**: 支持CSV格式导出

## 💾 数据备份

**重要**: 定期备份您的数据！
- 直接复制 `weather_stations.db` 文件
- 或者在Python中调用备份功能：
```python
from weather_database_fixed import WeatherDatabase
db = WeatherDatabase()
db.backup_database()
db.close()
```

## 🗺️ 高德地图配置

为了显示站点地理位置，需要配置高德地图API：

1. **申请API Key**: 访问 https://console.amap.com/ 注册并申请
2. **配置密钥**: 编辑 `web_app/config.py` 文件，替换API Key
3. **详细步骤**: 参考 `高德地图API设置指南.md`

**注意**: 不配置API Key也可以正常使用其他功能，只是无法显示地图。

## 🔧 常见问题

### Q: 导入数据失败怎么办？
A: 检查文件格式是否正确，确保包含必要的列名（如"区站号"、"站名"等）

### Q: 经纬度输入错误怎么办？
A: 纬度范围：-90到90，经度范围：-180到180

### Q: 如何查看所有站点？
A: 在首页点击"浏览站点"，或直接访问 `/station_list` 页面

### Q: 地图不显示怎么办？
A: 检查是否正确配置了高德地图API Key，参考设置指南

### Q: 数据库文件在哪里？
A: 默认在程序目录下的 `weather_stations.db` 文件

## 📁 文件结构

```
database/
├── weather_database_fixed.py    # 数据库核心模块
├── weather_stations.db         # SQLite数据库文件
├── web_app/                    # Web应用
│   ├── app.py                  # Flask应用
│   ├── templates/              # HTML模板
│   ├── static/                 # 静态文件
│   └── uploads/                # 上传文件目录
└── example_data/               # 示例数据
```

## 🛡️ 数据安全提醒

1. **定期备份**: 建议每周备份一次数据库
2. **文件检查**: 导入前检查数据文件格式
3. **测试导入**: 大批量数据导入前先用小文件测试

## 📞 技术支持

如遇到问题，请检查：
1. 控制台输出的错误信息
2. 数据文件格式是否正确
3. 文件编码是否为UTF-8

---
*最后更新: 2024年*
