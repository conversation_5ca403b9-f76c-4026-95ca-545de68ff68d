import xarray as xr
import os
import pandas as pd
import pyodbc
import h5py

def get_nc_info(nc_file):
    try:
        ds = xr.open_dataset(nc_file, engine='netcdf4')
        print('查看变量单位选1，查看全部信息选2')
        choice = input('请输入您的选择：')
        if choice == '1':
            for var_name in ds.variables:
                print(f"变量 {var_name} 的单位是: {ds[var_name].attrs.get('units', '无单位信息')}")
        elif choice == '2':
            print(ds)
    except ModuleNotFoundError:
        print("错误：缺少netCDF4或xarray依赖库")
        print("请执行：pip install netCDF4 xarray")
    except Exception as e:
        print(f"读取NetCDF文件失败: {str(e)}")

def get_grib2_info(grib2_file):
    try:
        ds = xr.open_dataset(grib2_file, engine='cfgrib', backend_kwargs={'filter_by_keys': {'dataType': 'pf'}})
        print('查看变量单位选1，查看全部信息选2')
        choice = input('请输入您的选择：')
        if choice == '1':
            for var_name in ds.variables:
                print(f"变量 {var_name} 的单位是: {ds[var_name].attrs.get('units', '无单位信息')}")
        elif choice == '2':
            print(ds)
    except ModuleNotFoundError:
        print("错误：缺少cfgrib或ecCodes依赖库")
        print("请执行：pip install cfgrib eccodes")
    except Exception as e:
        print(f"读取GRIB2文件失败: {str(e)}")

def get_mdb_info(mdb_file):
    """读取Access数据库文件"""
    try:
        conn_str = f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={mdb_file};'
        with pyodbc.connect(conn_str) as conn:
            # 获取第一个表名
            table_name = conn.cursor().tables(tableType='TABLE').fetchone().table_name
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            print(df.head())
    except pyodbc.Error as e:
        print(f"数据库连接错误: {str(e)}")
    except Exception as e:
        print(f"读取MDB文件失败: {str(e)}")

import h5py

def get_hdf_info(hdf_file, mode='r'):
    try:
        with h5py.File(hdf_file, mode) as f:
            print("文件结构：")
            f.visititems(lambda name, obj: print(f"{name}: {type(obj)}"))
    except Exception as e:
        print(f"读取HDF文件失败: {str(e)}")
    

def process_file(file_name):
    if not os.path.exists(file_name):
        print("文件不存在！")
        return
        
    ext = os.path.splitext(file_name)[1].lower()
    
    if ext == '.nc':
        get_nc_info(file_name)
    elif ext in ('.grib2', '.grb2', '.grib'):
        get_grib2_info(file_name)
    elif ext == '.mdb':
        get_mdb_info(file_name)
    elif ext == '.hdf':
        get_hdf_info(file_name)
    else:
        print('不支持的文件格式')

if __name__ == '__main__':
    file_path = input("请输入数据文件路径：")
    process_file(file_path)
    #E:\Data_wrangling\data\CLDAS\CLDAS\Z_NAFP_C_BABJ_20250306000714_P_CLDAS_RT_CHN_0P05_HOR-VIS-2025030600.GRB2
    #E:\Data_wrangling\data\CMPAS\Z_SURF_C_BABJ_20250306000425_P_CMPA_RT_CHN_0P01_HOR-PRE-2025030600.GRB2
    #E:\Data_wrangling\data\COBE sst\sst.mon.mean.nc
    #E:\Data_wrangling\data\shanxi\TDLY\MCD12Q1.********.h00v08.061.2022146024956.hdf
    #E:\Data_wrangling\data\shanxi\ZBFGD\MYD13A1.********.h00v08.061.2025073235432.hdf
    #E:\Data_wrangling\data\Forcast\ECMWF\20250319000000-0h-enfo-ef.grib2
