import cdsapi
import os
from subprocess import call

def idmDownloader(task_url, folder_path, file_name):
    """
    IDM下载器
    :param task_url: 下载任务地址
    :param folder_path: 存放文件夹
    :param file_name: 文件名
    :return:
    """
    # IDM安装目录
    idm_engine = "C:\\Program Files (x86)\\Internet Download Manager\\IDMan.exe"
    # 将任务添加至队列
    call([idm_engine, '/d', task_url, '/p', folder_path, '/f', file_name, '/a'])
    # 开始任务队列
    call([idm_engine, '/s'])

c = cdsapi.Client()
yearStart = 1997
yearEnd = 2020
monStrt = 1
monEnd = 12
base_path = r'D:\python\data\precipitation1'
variable = 'all'

years = range(yearStart, yearEnd+1)
yr = [str(i) for i in years]
months = [str(j).zfill(2) for j in range(monStrt, monEnd+1)]

for iyr in range(len(yr)):
    year_path = os.path.join(base_path, str(yr[iyr]))
    os.makedirs(year_path, exist_ok=True)
    
    filename = f'satellite_precipitation_{yr[iyr]}.nc'
    output_file_path = os.path.join(year_path, filename)
    
    if os.path.exists(output_file_path):
        print(f'File {output_file_path} already exists. Skipping download.')
        continue
        
    print('=======  year:  ' + yr[iyr] + '  =======')
    r = c.retrieve(
        'satellite-precipitation',
        {
            'variable': 'all',
            'time_aggregation': 'daily_mean',
            'year': [yr[iyr]],
            'month': months,
            'day': [
                '01', '02', '03',
                '04', '05', '06',
                '07', '08', '09',
                '10', '11', '12',
                '13', '14', '15',
                '16', '17', '18',
                '19', '20', '21',
                '22', '23', '24',
                '25', '26', '27',
                '28', '29', '30',
                '31'
            ],
            'area': [60, 70, 10, 140]
        })
    url = r.location
    idmDownloader(url, year_path, filename)