
# In[0]:

import numpy as np
import pandas as pd

# 读取站点信息文件
file_path1 = r'D:\python\monitoring\data\station\station_info.xlsx'
station_info = pd.read_excel(file_path1)
station_ids = station_info['station_id'].astype(str).values

# 读取 excel 文件
file_path = r'D:\python\monitoring\data\station\SURF_CHN_MUL_DAY(2020-2024).xlsx'
data = pd.read_excel(file_path)

# 获取年月日列
year = data['年']
month = data['月']
day = data['日']

# 将年月日组合成 datetime 格式
date_time = pd.to_datetime(year * 10000 + month * 100 + day, format='%Y%m%d')
# 删除原始的年月日列
data.drop(['年', '月', '日'], axis=1, inplace=True)
# 添加新的日期列并设置为索引
data['date'] = date_time
data.set_index('date', inplace=True)
# 按日期排序
data.sort_index(inplace=True)


# 读取天气现象文件
file_path2 = r'D:\python\monitoring\Statistics_days_stations\data\天气现象_雨(2020-2025年3月).xlsx'
weather_data = pd.read_excel(file_path2)


# In[1] :常规要素
def calculation_with_time_scale(station_ids, data, var_name, a, Statistical_period, method='fixed'):
    # 初始化日数数据列表
    calculation_days = []
    # 初始化站数据列表
    calculation_stations = []

    for station_id in station_ids:
        # 使用原始数据的副本进行筛选
        station_data = data[data['区站号(字符)'].astype(str) == station_id].copy()
        station_data.replace([999990,999999,999998], np.nan, inplace=True)

        # 获取站点经纬度信息
        station_row = station_info[station_info['station_id'].astype(str) == station_id]
        lon = station_row['lon'].values[0]
        lat = station_row['lat'].values[0]

        if method == 'fixed':
            # 提取指定统计时段的数据（固定阈值）
            station_data = station_data.loc[Statistical_period]
            total_rain = station_data[station_data[var_name] >= 0.1]
            light_rain = station_data[(station_data[var_name] >= 0.1) & (station_data[var_name] < 10)]
            Moderate_rain = station_data[(station_data[var_name] >= 10) & (station_data[var_name] < 25)]
            heavy_rain = station_data[(station_data[var_name] >= 25) & (station_data[var_name] < 50)]
            rainstorm = station_data[(station_data[var_name] >= 50) & (station_data[var_name] < 100)]
            heavy_rainstorm = station_data[(station_data[var_name] >= 100) & (station_data[var_name] < 250)]
            violent_rainstorm = station_data[station_data[var_name] >= 250]

            # 统计时段累计不同要素量
            total_rain_days = len(total_rain)
            light_rain_days = len(light_rain)
            Moderate_rain_days = len(Moderate_rain)
            heavy_rain_days = len(heavy_rain)
            rainstorm_days = len(rainstorm)
            heavy_rainstorm_days = len(heavy_rainstorm)
            violent_rainstorm_days = len(violent_rainstorm)

            # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
            calculation_days.append({
                'station_id': station_id,
                'lon': lon,
                'lat': lat,
                '总日数': total_rain_days,
                '小雨日数': light_rain_days,
                '中雨日数': Moderate_rain_days,
                '大雨日数': heavy_rain_days,
                '暴雨日数': rainstorm_days,
                '大暴雨日数': heavy_rainstorm_days,
                '特大暴雨日数': violent_rainstorm_days 
            })

            total_rain_stations = 0
            light_rain_stations = 0
            Moderate_rain_stations = 0
            heavy_rain_stations = 0
            rainstorm_stations = 0
            heavy_rainstorm_stations = 0
            violent_rainstorm_stations = 0

            if station_data.max()[var_name] >= 250:
                violent_rainstorm_stations += 1
            elif station_data.max()[var_name] >= 100:
                heavy_rainstorm_stations += 1
            elif station_data.max()[var_name] >= 50:
                rainstorm_stations += 1
            elif station_data.max()[var_name] >= 25:
                heavy_rain_stations += 1
            elif station_data.max()[var_name] >= 10:
                Moderate_rain_stations += 1
            elif station_data.max()[var_name] >= 0.1:
                light_rain_stations += 1

            total_rain_stations = light_rain_stations + Moderate_rain_stations + heavy_rain_stations + rainstorm_stations + heavy_rainstorm_stations + violent_rainstorm_stations

            # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
            calculation_stations.append({
                'station_id': station_id,
                'lon': lon,
                'lat': lat,
                '总站数': total_rain_stations,
                '小雨站数': light_rain_stations,
                '中雨站数': Moderate_rain_stations,
                '大雨站数': heavy_rain_stations,
                '暴雨站数': rainstorm_stations,
                '大暴雨站数': heavy_rainstorm_stations,
                '特大暴雨站数': violent_rainstorm_stations 
            })            


            
        elif method == 'Customize':
            # 提取指定统计时段的数据（自定义阈值）
            station_data = station_data.loc[Statistical_period]
            Statistical_data = station_data[station_data[var_name] >= a]


            # 统计时段累计不同要素量
            days = len(Statistical_data)

            # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
            calculation_days.append({
                'station_id': station_id,
                'lon': lon,
                'lat': lat,
                '日数': days
            })

            total_stations = 0

            if station_data.max()[var_name] >= 10:
                total_stations += 1



            # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
            calculation_stations.append({
               'station_id': station_id,
                'lon': lon,
                'lat': lat,
                '站数': total_stations,
            })


    cumulative_df_days = pd.DataFrame(calculation_days)
    cumulative_df_stations = pd.DataFrame(calculation_stations)

    return cumulative_df_days, cumulative_df_stations

# In[2] :天气现象

def calculation_weather(station_ids, data, var_name, Statistical_period):
    # 初始化日数数据列表
    calculation_days = []
    # 初始化站数据列表
    calculation_stations = []

    for station_id in station_ids:
        # 使用原始数据的副本进行筛选
        station_data = data[data['区站号(字符)'].astype(str) == station_id].copy()

        # 获取站点经纬度信息
        station_row = station_info[station_info['station_id'].astype(str) == station_id]
        lon = station_row['lon'].values[0]
        lat = station_row['lat'].values[0]

        rain_days = len(station_data[station_data[var_name] == 1])

        # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
        calculation_days.append({
            'station_id': station_id,
            'lon': lon,
            'lat': lat,
            '雨日数': rain_days
        })

    for date in Statistical_period:
        # 使用原始数据的副本进行筛选
        date_data = data[data['资料时间'] == date].copy()

        # 统计时段累计不同要素量
        rain_stations = len(date_data[date_data[var_name] == 1])

        # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
        calculation_stations.append({
            'date': date,
            '雨站数': rain_stations
        })

    cumulative_df_days = pd.DataFrame(calculation_days)
    cumulative_df_stations = pd.DataFrame(calculation_stations)

    return cumulative_df_days, cumulative_df_stations


# In[3] :常规要素

for i in range(2021, 2025):
    Statistical_period = pd.date_range(start=f'{i}-02-01', end=f'{i}-05-31', freq='D')

    cumulative_df_days, cumulative_df_stations = calculation_with_time_scale(station_ids, data, '20-20时降水量', 10,  Statistical_period, method='Customize')

    '''
    print(cumulative_df_days)
    print(cumulative_df_stations)
    '''

    if i == 2021:
        cumulative_df_days.to_excel(r'D:\python\monitoring\Statistics_days_stations\data\precipitation_result_days(Customize).xlsx', index=False, sheet_name=f'{i}')
        cumulative_df_stations.to_excel(r'D:\python\monitoring\Statistics_days_stations\data\precipitation_result_stations(Customize).xlsx', index=False, sheet_name=f'{i}')
    else:
        with pd.ExcelWriter(r'D:\python\monitoring\Statistics_days_stations\data\precipitation_result_days(Customize).xlsx', mode='a', engine='openpyxl') as writer:
            cumulative_df_days.to_excel(writer, index=False, sheet_name=f'{i}')
        with pd.ExcelWriter(r'D:\python\monitoring\Statistics_days_stations\data\precipitation_result_stations(Customize).xlsx', mode='a', engine='openpyxl') as writer:
            cumulative_df_stations.to_excel(writer, index=False, sheet_name=f'{i}')


# In[4] :天气现象

Statistical_period = weather_data['资料时间'].astype(str).unique()
cumulative_df_days, cumulative_df_stations = calculation_weather(station_ids, weather_data, '雨',  Statistical_period)

'''
print(cumulative_df_days)
print(cumulative_df_stations)
'''

cumulative_df_days.to_excel(r'D:\python\monitoring\Statistics_days_stations\data\weather_result_days.xlsx', index=False)
cumulative_df_stations.to_excel(r'D:\python\monitoring\Statistics_days_stations\data\weather_result_stations.xlsx', index=False)

# %%