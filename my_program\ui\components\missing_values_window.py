import tkinter as tk
from tkinter import ttk, messagebox
import numpy as np

class MissingValuesWindow:
    def __init__(self, parent, data_processor, result_text):
        self.window = tk.Toplevel(parent)
        self.window.title("处理缺失值")
        self.window.geometry("600x500")
        self.data_processor = data_processor
        self.result_text = result_text
        
        self.setup_ui()

    def setup_ui(self):
        # 显示列选择框
        ttk.Label(self.window, text="选择需要处理的列：").pack(pady=5)
        columns_frame = ttk.Frame(self.window)
        columns_frame.pack(pady=5)

        # 创建列选择列表框
        self.columns_listbox = tk.Listbox(columns_frame, selectmode=tk.MULTIPLE, width=40, height=8)
        self.columns_listbox.pack(side=tk.LEFT, padx=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(columns_frame, orient=tk.VERTICAL, command=self.columns_listbox.yview)
        scrollbar.pack(side=tk.LEFT, fill=tk.Y)
        self.columns_listbox.configure(yscrollcommand=scrollbar.set)

        # 添加列名
        for col in self.data_processor.df.columns:
            self.columns_listbox.insert(tk.END, col)

        # 异常值输入框
        value_frame = ttk.Frame(self.window)
        value_frame.pack(pady=5, fill=tk.X, padx=5)
        
        ttk.Label(value_frame, text="输入需要检测的缺测值（用逗号分隔）：").pack(side=tk.LEFT)
        self.values_entry = ttk.Entry(value_frame, width=40)
        self.values_entry.pack(side=tk.LEFT, padx=5)
        self.values_entry.insert(0, "999999")

        # 预览区域
        preview_frame = ttk.LabelFrame(self.window, text="缺测值检测结果", padding=5)
        preview_frame.pack(pady=5, fill=tk.BOTH, expand=True)
        
        self.preview_text = tk.Text(preview_frame, width=50, height=10)
        self.preview_text.pack(fill=tk.BOTH, expand=True)

        # 按钮区域
        button_frame = ttk.Frame(self.window)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="检测缺测值", command=self.check_missing_values).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="替换为NaN", command=lambda: self.replace_values(np.nan)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="替换为0", command=lambda: self.replace_values(0)).pack(side=tk.LEFT, padx=5)

    def get_selected_columns(self):
        selected_indices = self.columns_listbox.curselection()
        return [self.data_processor.df.columns[i] for i in selected_indices]

    def check_missing_values(self):
        try:
            selected_columns = self.get_selected_columns()
            if not selected_columns:
                messagebox.showwarning("警告", "请至少选择一列进行检测！")
                return

            values_to_check = [float(x.strip()) for x in self.values_entry.get().split(",")]
            if not values_to_check:
                messagebox.showwarning("警告", "请输入需要检测的缺测值！")
                return

            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, "缺测值检测结果：\n\n")

            for col in selected_columns:
                self.preview_text.insert(tk.END, f"列 '{col}' 的检测结果：\n")
                for val in values_to_check:
                    count = (self.data_processor.df[col] == val).sum()
                    if count > 0:
                        percent = (count / len(self.data_processor.df[col])) * 100
                        self.preview_text.insert(tk.END, f"缺测值 {val} 出现 {count} 次，占比 {percent:.2f}%\n")
                self.preview_text.insert(tk.END, "\n")

        except Exception as e:
            messagebox.showerror("错误", f"检测失败：{str(e)}")

    def replace_values(self, replace_value):
        try:
            selected_columns = self.get_selected_columns()
            values_to_replace = [float(x.strip()) for x in self.values_entry.get().split(",")]
            
            success, result = self.data_processor.handle_missing_values(
                selected_columns, values_to_replace, replace_value
            )

            if success:
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, "缺测值替换完成！\n\n")
                self.result_text.insert(tk.END, "处理后的数据预览（前5行）：\n")
                self.result_text.insert(tk.END, str(result))
                self.window.destroy()
                messagebox.showinfo("成功", "缺测值替换完成！")
            else:
                messagebox.showerror("错误", f"替换失败：{result}")

        except Exception as e:
            messagebox.showerror("错误", f"替换失败：{str(e)}")