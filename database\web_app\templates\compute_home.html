<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算中心 - 小恐龙的超算单元</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #17a2b8;
            --accent-color: #ffc107;
            --bg-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .compute-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            margin-bottom: 40px;
        }

        .page-title {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .breadcrumb-custom {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 8px 20px;
            margin-bottom: 20px;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: white;
        }

        .compute-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            border: none;
            height: 100%;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .compute-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(116, 185, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .compute-card:hover::before {
            left: 100%;
        }

        .compute-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .compute-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            background: var(--bg-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .compute-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .compute-description {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .compute-features {
            list-style: none;
            padding: 0;
            margin-bottom: 25px;
        }

        .compute-features li {
            padding: 5px 0;
            color: #495057;
            position: relative;
            padding-left: 20px;
        }

        .compute-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--primary-color);
            font-weight: bold;
        }

        .compute-btn {
            background: var(--bg-gradient);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
        }

        .compute-btn:hover {
            transform: scale(1.05);
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .status-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .coming-soon {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .coming-soon .compute-btn {
            background: #6c757d;
            cursor: not-allowed;
        }

        .coming-soon .compute-btn:hover {
            transform: none;
            box-shadow: none;
        }

        .coming-soon .status-badge {
            background: #6c757d;
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }
            
            .compute-card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="compute-header">
        <div class="container">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-custom">
                    <li class="breadcrumb-item"><a href="/">🦕 小恐龙的超算单元</a></li>
                    <li class="breadcrumb-item active">💻 计算中心</li>
                </ol>
            </nav>
            
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="bi bi-cpu-fill me-3"></i>计算中心
                    </h1>
                    <p class="page-subtitle">高性能气象计算平台 · 专业分析工具集</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="/" class="btn btn-outline-light">
                        <i class="bi bi-house-door me-2"></i>返回系统主页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 计算功能模块 -->
    <div class="container">
        <div class="row g-4">
            <!-- 华西秋雨分析 -->
            <div class="col-lg-6 col-md-6">
                <div class="compute-card" onclick="window.location.href='/compute/huaxi-autumn-rain'">
                    <div class="status-badge">已部署</div>
                    <div class="compute-icon">
                        <i class="bi bi-cloud-rain-heavy-fill"></i>
                    </div>
                    <h3 class="compute-title">华西秋雨分析</h3>
                    <p class="compute-description">
                        专业的华西秋雨气象现象分析工具，基于多站点降水数据进行综合分析和预测
                    </p>
                    <ul class="compute-features">
                        <li>多站点降水数据分析</li>
                        <li>秋雨强度等级判定</li>
                        <li>时空分布可视化</li>
                        <li>历史对比分析</li>
                        <li>趋势预测模型</li>
                    </ul>
                    <a href="/compute/huaxi-autumn-rain" class="compute-btn">
                        <i class="bi bi-play-fill me-2"></i>开始分析
                    </a>
                </div>
            </div>

            <!-- 气候变化分析 (即将推出) -->
            <div class="col-lg-6 col-md-6">
                <div class="compute-card coming-soon">
                    <div class="status-badge">开发中</div>
                    <div class="compute-icon">
                        <i class="bi bi-thermometer-sun"></i>
                    </div>
                    <h3 class="compute-title">气候变化分析</h3>
                    <p class="compute-description">
                        长期气候变化趋势分析，温度、降水等要素的变化检测和归因分析
                    </p>
                    <ul class="compute-features">
                        <li>长期趋势检测</li>
                        <li>突变点识别</li>
                        <li>周期性分析</li>
                        <li>归因分析</li>
                        <li>未来预估</li>
                    </ul>
                    <span class="compute-btn">
                        <i class="bi bi-clock me-2"></i>即将推出
                    </span>
                </div>
            </div>

            <!-- 极端天气预警 (即将推出) -->
            <div class="col-lg-6 col-md-6">
                <div class="compute-card coming-soon">
                    <div class="status-badge">规划中</div>
                    <div class="compute-icon">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                    </div>
                    <h3 class="compute-title">极端天气预警</h3>
                    <p class="compute-description">
                        基于机器学习的极端天气事件预警系统，提供高精度的预警服务
                    </p>
                    <ul class="compute-features">
                        <li>实时监测预警</li>
                        <li>多模型集成</li>
                        <li>风险等级评估</li>
                        <li>影响范围预测</li>
                        <li>应急响应建议</li>
                    </ul>
                    <span class="compute-btn">
                        <i class="bi bi-clock me-2"></i>即将推出
                    </span>
                </div>
            </div>

            <!-- 数值模式后处理 (即将推出) -->
            <div class="col-lg-6 col-md-6">
                <div class="compute-card coming-soon">
                    <div class="status-badge">规划中</div>
                    <div class="compute-icon">
                        <i class="bi bi-diagram-3-fill"></i>
                    </div>
                    <h3 class="compute-title">数值模式后处理</h3>
                    <p class="compute-description">
                        数值天气预报模式输出的统计后处理，提高预报精度和可用性
                    </p>
                    <ul class="compute-features">
                        <li>模式输出统计</li>
                        <li>偏差订正</li>
                        <li>降尺度处理</li>
                        <li>集合预报</li>
                        <li>产品制作</li>
                    </ul>
                    <span class="compute-btn">
                        <i class="bi bi-clock me-2"></i>即将推出
                    </span>
                </div>
            </div>
        </div>

        <!-- 页脚信息 -->
        <div class="text-center mt-5 mb-4">
            <p class="text-white-50">
                <i class="bi bi-info-circle me-2"></i>
                计算中心提供专业的气象数据分析和计算服务，支持多种气象现象的深度分析
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为可用的计算卡片添加点击效果
            const computeCards = document.querySelectorAll('.compute-card:not(.coming-soon)');
            computeCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98) translateY(-8px)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
