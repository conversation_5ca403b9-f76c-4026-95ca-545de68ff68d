# In[0]:

import numpy as np
import pandas as pd
import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import cartopy.mpl.ticker as cticker
import geopandas as gpd
from matplotlib.path import Path
from cartopy.mpl.patch import geos_to_path

PRE_file_path = r'D:\python\monitoring\data\reanalysis\CRA40_DAILY-PRE-NUL-20241231000000-OVL-0.25.nc'
T_file_path = r'D:\python\monitoring\data\reanalysis\CRA40_DAILY-T-NUL-20241231000000-OVL-0.25.nc'
PRE_data = xr.open_dataset(PRE_file_path).sel(lon=slice(95, 109), lat=slice(25.3, 34.88)).sel(time=slice('2024-04-01', '2024-06-30'))
T_data = xr.open_dataset(T_file_path).sel(lon=slice(95, 109), lat=slice(25.3, 34.88)).sel(time=slice('2024-03-01', '2024-06-30'))

preci = PRE_data['pre'].sum(dim='time')
temp = T_data['t2m'].mean(dim='time')

# 打开站点信息文件
station_info = pd.read_excel(r'D:\python\monitoring\data\station\station_info.xlsx')
station_ids = station_info['station_id'].astype(str)
# 遍历站点 ID 列表，获取每个站点的经纬度信息
station_lon_lat = []
for station_id in station_ids:
    station_row = station_info[station_info['station_id'].astype(str) == station_id]
    lon = station_row['lon'].values[0]
    lat = station_row['lat'].values[0]
    station_lon_lat.append((station_id, lon, lat))

# 提取站点的经纬度
station_lons = [lon for _, lon, _ in station_lon_lat]
station_lats = [lat for _, _, lat in station_lon_lat]

# 利用双线性插值方法，将格点数据插值到站点上
preci_interp = preci.interp(lon=('points', station_lons), lat=('points', station_lats), method='linear')
temp_interp = temp.interp(lon=('points', station_lons), lat=('points', station_lats), method='linear')

# 创建一个 DataFrame 来存储插值结果
interp_result = pd.DataFrame({
    'station_id': station_ids,
    'precipitation': preci_interp.values,
    'temperature': temp_interp.values
})

print(interp_result)


# 将列表转换为 DataFrame
station_df = pd.DataFrame(interp_result)
print(station_df)
station_df.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\day_reanalysis_data2.xlsx',index=False)


# In[1]:降水

# 绘图
fig = plt.figure(figsize=(15, 10))
proj = ccrs.PlateCarree()
ax = fig.add_axes([0.05, 0.3, 0.5, 0.5], projection=proj)
img_extent = [95, 109, 25.3, 34.88]

def contour_map(fig,img_extent):
    img_extent = [95, 109, 25.3, 34.88]
    fig.set_extent(img_extent, crs=ccrs.PlateCarree())
    fig.add_feature(cfeature.COASTLINE.with_scale('50m'),alpha=0.5)
    fig.add_feature(cfeature.LAKES,alpha=0.5)
    fig.set_xticks(np.arange(95, 109, 2.5),crs=ccrs.PlateCarree())
    fig.set_yticks(np.arange(25.3, 34.88, 1),crs=ccrs.PlateCarree())
    lon_formatter = cticker.LongitudeFormatter()
    lat_formatter = cticker.LatitudeFormatter()
    fig.xaxis.set_major_formatter(lon_formatter)
    fig.yaxis.set_major_formatter(lat_formatter)

shp = gpd.read_file(r'D:\python\monitoring\data\shp\省界_region.shp',encoding='gbk')
a = shp['geometry']
contour_map(ax,[95, 109, 25.3, 34.88])
contour = ax.contourf(preci.lon, preci.lat, preci, cmap='Blues')
# 生成裁剪路径
path_clip = Path.make_compound_path(*geos_to_path(a.to_list()))
# 将裁剪路径应用到图层
contour.set_clip_path(path_clip, transform=ax.transData)
# 绘制多边形边缘线
ax.add_geometries(a, crs=ccrs.PlateCarree(), facecolor='none', edgecolor='black')
cbar = plt.colorbar(contour, ax=ax, orientation='horizontal', pad=0.05, aspect=50)
cbar.set_label('mm')
ax.set_title('Precipitation')
plt.show()


# In[2]:平均气温
# 绘图


fig = plt.figure(figsize=(15, 10))
proj = ccrs.PlateCarree()
ax = fig.add_axes([0.05, 0.3, 0.5, 0.5], projection=proj)
img_extent = [95, 109, 25.3, 34.88]

def contour_map(fig,img_extent):
    img_extent = [95, 109, 25.3, 34.88]
    fig.set_extent(img_extent, crs=ccrs.PlateCarree())
    fig.add_feature(cfeature.COASTLINE.with_scale('50m'),alpha=0.5)
    fig.add_feature(cfeature.LAKES,alpha=0.5)
    fig.set_xticks(np.arange(95, 109, 2.5),crs=ccrs.PlateCarree())
    fig.set_yticks(np.arange(25.3, 34.88, 1),crs=ccrs.PlateCarree())
    lon_formatter = cticker.LongitudeFormatter()
    lat_formatter = cticker.LatitudeFormatter()
    fig.xaxis.set_major_formatter(lon_formatter)
    fig.yaxis.set_major_formatter(lat_formatter)

shp = gpd.read_file(r'D:\python\monitoring\data\shp\省界_region.shp',encoding='gbk')
a = shp['geometry']
contour_map(ax,[95, 109, 25.3, 34.88])
contour = ax.contourf(temp.lon, temp.lat, temp, cmap='coolwarm')
# 生成裁剪路径
path_clip = Path.make_compound_path(*geos_to_path(a.to_list()))
# 将裁剪路径应用到图层
contour.set_clip_path(path_clip, transform=ax.transData)
# 绘制多边形边缘线
ax.add_geometries(a, crs=ccrs.PlateCarree(), facecolor='none', edgecolor='black')
cbar = plt.colorbar(contour, ax=ax, orientation='horizontal', pad=0.05, aspect=50)
cbar.set_label('°C')
ax.set_title('Average Temperature')
plt.show()
