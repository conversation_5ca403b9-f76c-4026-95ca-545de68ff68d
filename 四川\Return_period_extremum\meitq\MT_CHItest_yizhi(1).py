

def MT_CHItest(x1,x2):
    import numpy as np
    from scipy.stats import chisquare
    obs = np.array([x1,x2])
    data1,_ = np.histogram(x1,bins=20,range=(np.min(obs),np.max(obs)))
    data2,_ = np.histogram(x2,bins=20,range=(np.min(obs),np.max(obs)))
    data1 = np.array(data1)+0.1
    data2 = np.array(data2)+0.1

    res = chisquare(f_obs=data1, f_exp=data2)
    print(data1,data2)
    print(res)
    svalue = res.statistic
    if res.pvalue <= 0.01:
        m = 0.01
    if res.pvalue <= 0.05 and res.pvalue > 0.01:
        m = 0.05
    if res.pvalue <= 0.1 and res.pvalue > 0.05:
        m = 0.1
    if res.pvalue > 0.1:
        m = 1
    return svalue,m




# test
import numpy as np
rng = np.random.default_rng(seed=1)
x1 = rng.gumbel(1,1,30)*10+200
x2 = rng.uniform(2,30,30)+200

print(MT_CHItest(x1,x2))

'''
def ks_test(x1, x2):
    res = kstest(x1, x2)
    val = res.statistic
    m = 0
    if res.pvalue <= 0.01:
        m = 0.01
    if 0.05 >= res.pvalue > 0.01:
        m = 0.05
    if 0.1 >= res.pvalue > 0.05:
        m = 0.1
    if res.pvalue > 0.1:
        m = 1
    return val, m


def ad_test(x1, x2):
    samples = [x1, x2]
    val, _, pvalue = anderson_ksamp(samples)
    m = 0
    if pvalue <= 0.01:
        m = 0.01
    if 0.05 >= pvalue > 0.01:
        m = 0.05
    if 0.1 >= pvalue > 0.05:
        m = 0.1
    if pvalue > 0.1:
        m = 1
    return val, m


def chi_test(x1, x2):
    """
        关联卡方检验
        参数：
            data1		检验序列1，一维，data1(n)，n为序列1的长度，要求序列数值>0
            data2 		检验序列2，一维，data1(n)，n为序列2的长度，要求序列数值>0
            返回值
            ----------
            val     	检验值
            m			判识指标，取值0.01, 0.05, 0.1, 1
    """
    obs = np.array([x1, x2])
    data1, _ = np.histogram(x1, bins=20, range=(np.min(obs), np.max(obs)))
    data2, _ = np.histogram(x2, bins=20, range=(np.min(obs), np.max(obs)))
    data1 = np.array(data1) + 0.1
    data2 = np.array(data2) + 0.1

    res = chisquare(f_obs=data1, f_exp=data2)
    val = res.statistic

    m = 0
    if res.pvalue <= 0.01:
        m = 0.01
    if 0.05 >= res.pvalue > 0.01:
        m = 0.05
    if 0.1 >= res.pvalue > 0.05:
        m = 0.1
    if res.pvalue > 0.1:
        m = 1
    return val, m


def mse_test(x1, x2):
    mse = mean_squared_error(x1, x2)
    return mse, 0


def rmse_test(x1, x2):
    """
    计算均方根误差（RMSE）

    参数:
    x1 (numpy array): 实际值
    x2 (numpy array): 预测值

    返回:
        float: 均方根误差
    """
    # 计算差值的平方
    squared_errors = (x1 - x2) ** 2

    # 计算平方的平均值
    mean_squared_error = np.mean(squared_errors)

    # 取平方根
    rmse = np.sqrt(mean_squared_error)

    return rmse, 0
    '''