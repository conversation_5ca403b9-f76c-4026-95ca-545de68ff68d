import numpy as np
from datetime import datetime

def read_nino34_data(filepath):
    years = []
    monthly_data = []
    try:
        with open(filepath, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                    
                data = line.split()
                if len(data) < 2:  # 确保行中至少有年份和一个月份的数据
                    print(f"警告：第 {line_num} 行数据格式不正确，已跳过")
                    continue
                
                try:
                    year = int(data[0])
                    values = []
                    for val in data[1:]:
                        try:
                            values.append(float(val))
                        except ValueError:
                            print(f"警告：第 {line_num} 行包含无效的数值：{val}")
                            values.append(-99.99)  # 使用缺失值标记
                    
                    years.append(year)
                    monthly_data.append(values)
                except ValueError:
                    print(f"警告：第 {line_num} 行的年份格式不正确：{data[0]}")
                    continue
                    
    except FileNotFoundError:
        print(f"错误：找不到数据文件 {filepath}")
        return np.array([]), np.array([])
    
    if not years:
        print("错误：数据文件为空或格式不正确")
        return np.array([]), np.array([])
        
    return np.array(years), np.array(monthly_data)

def calculate_3month_moving_average(data):
    result = []
    for i in range(len(data)):
        if i == 0:
            avg = (data[i] + data[i+1]) / 2
        elif i == len(data)-1:
            avg = (data[i-1] + data[i]) / 2
        else:
            avg = (data[i-1] + data[i] + data[i+1]) / 3
        result.append(round(avg, 1))
    return np.array(result)

def get_event_strength(peak_value):
    abs_peak = abs(peak_value)
    if abs_peak >= 2.5:
        return "超强事件"
    elif abs_peak >= 2.0:
        return "强事件"
    elif abs_peak >= 1.3:
        return "中等事件"
    elif abs_peak >= 0.5:
        return "弱事件"
    return "非事件"

def calculate_climatology(years, monthly_data, base_period=(1991, 2020)):
    """计算气候态（月平均值）"""
    monthly_means = np.zeros(12)
    counts = np.zeros(12)
    
    for year_idx, year in enumerate(years):
        if year >= base_period[0] and year <= base_period[1]:
            year_data = monthly_data[year_idx]
            for month_idx, value in enumerate(year_data):
                if value != -99.99:  # 排除缺失值
                    monthly_means[month_idx] += value
                    counts[month_idx] += 1
    
    # 计算每月平均值
    for i in range(12):
        if counts[i] > 0:
            monthly_means[i] /= counts[i]
            
    return monthly_means

def calculate_anomalies(monthly_data, climatology):
    """计算距平值"""
    anomalies = []
    for year_data in monthly_data:
        year_anomalies = []
        for month_idx, value in enumerate(year_data):
            if value == -99.99:
                year_anomalies.append(-99.99)
            else:
                anomaly = value - climatology[month_idx]
                year_anomalies.append(round(anomaly, 2))
        anomalies.append(year_anomalies)
    return np.array(anomalies)

def find_events(years, monthly_anomalies):
    """基于距平值识别事件"""
    events = []
    for year_idx, year in enumerate(years):
        if year_idx >= len(monthly_anomalies)-1:  # 跳过最后一年的不完整数据
            break
            
        year_data = monthly_anomalies[year_idx]
        if -99.99 in year_data:  # 跳过包含缺失值的年份
            continue
            
        # 计算3个月滑动平均
        ma3 = calculate_3month_moving_average(year_data)
        
        # 检查是否满足事件条件（持续5个月以上，绝对值>=0.5）
        consecutive_months = 0
        max_value = 0
        max_month = 0
        
        for month_idx, value in enumerate(ma3):
            if abs(value) >= 0.5:
                consecutive_months += 1
                if abs(value) > abs(max_value):
                    max_value = value
                    max_month = month_idx + 1
            else:
                consecutive_months = 0
                
            if consecutive_months >= 5:
                event_type = "厄尔尼诺" if max_value > 0 else "拉尼娜"
                events.append({
                    'year': year,
                    'type': event_type,
                    'peak_value': max_value,
                    'peak_month': max_month,
                    'strength': get_event_strength(max_value)
                })
                break
                
    return events

def main():
    years, monthly_data = read_nino34_data('d:/python/data/nino/nina34_data.txt')
    
    if len(years) == 0:
        print("程序终止：无有效数据")
        return
        
    # 计算气候态和距平值
    climatology = calculate_climatology(years, monthly_data)  # 修改这里的函数调用
    monthly_anomalies = calculate_anomalies(monthly_data, climatology)
    
    # 使用距平值寻找事件
    events = find_events(years, monthly_anomalies)
    
    if not events:
        print("未发现任何厄尔尼诺/拉尼娜事件")
        return
        
    print("NINO3.4事件分析结果（基于1991-2020年气候态）：")
    print("年份\t类型\t峰值(℃)\t峰值月份\t强度等级")
    print("-" * 50)
    
    for event in events:
        print(f"{event['year']}\t{event['type']}\t{event['peak_value']:.1f}\t{event['peak_month']}\t{event['strength']}")

if __name__ == "__main__":
    main()
