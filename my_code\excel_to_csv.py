import pandas as pd

# 读取Excel文件
df = pd.read_excel('D:/python/data/station/station.xlsx')

# 将数据保存为CSV文件
df.to_csv('D:/python/data/station/station.csv', index=False, encoding='utf-8')

# 读取 CSV 文件
file_path = 'd:/python/data/station/station.csv'
df = pd.read_csv(file_path)

# 保留 lat 和 lon 列数据的一位小数
df['Lat'] = df['Lat'].round(1)
df['Lon'] = df['Lon'].round(1)

# 将数据保存为CSV文件
df.to_csv('D:/python/data/station/station_round.csv', index=False, encoding='utf-8')

# 删除 lat 和 lon 列数据重复的行
df = df.drop_duplicates(subset=['Lat','Lon'])

# 保存修改后的数据到新的 CSV 文件
df.to_csv('d:/python/data/station/station_unique.csv', index=False)