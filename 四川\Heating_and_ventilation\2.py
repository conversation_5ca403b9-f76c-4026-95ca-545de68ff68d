# In[0]:加载数据
import pandas as pd
import numpy as np

hour_path = r'E:\python_env\Heating and air\data\SURF_CHN_MUL_HOR.xlsx'
day_path = r'E:\python_env\Heating and air\data\SURF_CHN_MUL_DAY.xlsx'
month_path = r'E:\python_env\Heating and air\data\SURF_CHN_MUL_MON.xlsx'

hour_data = pd.read_excel(hour_path)
day_data = pd.read_excel(day_path)
month_data = pd.read_excel(month_path)



# In[1]:计算函数

# 供暖计算室外温度
def Heating_calculates_the_outside_temperature():
    daily_temperature = day_data['平均气温'].sort_values()
    temperature = daily_temperature.values[25]
    return temperature

# 冬季通风室外计算温度
def Calculate_the_temperature_outside_the_ventilation_room_in_winter():
    temperature = month_data['平均气温'].min()
    return temperature

# 冬季空调室外计算温度
def In_winter_the_air_conditioner_calculates_the_temperature_outside():
    daily_temperature = day_data['平均气温'].sort_values()
    temperature = daily_temperature.values[5]
    return temperature

# 冬季空调室外计算相对湿度
def Relative_humidity_is_calculated_outside_the_air_conditioner_in_winter():
    relative_humidity = month_data['平均相对湿度'].min()
    return relative_humidity

# 夏季空调室外计算干球温度
def In_summer_the_air_conditioner_calculates_the_dry_bulb_temperature_outside():
    T_dp = hour_data['露点温度']
    RH = hour_data['相对湿度']
    T_db = (T_dp/(1-np.log(RH)/17.27))
    temperature = T_db.sort_values().values[150]
    return temperature

# 夏季空调室外计算湿球温度

# 夏季通风室外计算温度
def The_temperature_is_calculated_outside_the_ventilation_room_in_summer():
    hour_data['资料时间'] = pd.to_datetime(hour_data['资料时间'])
    grouped = hour_data.groupby([hour_data['资料时间'].dt.year, hour_data['资料时间'].dt.month])
    monthly_avg_temperature = grouped['温度'].mean()
    max_avg_temperature_month = monthly_avg_temperature.idxmax()
    temperature = hour_data[(hour_data['资料时间'].dt.year == max_avg_temperature_month[0]) & (hour_data['资料时间'].dt.month == max_avg_temperature_month[1]) & (hour_data['资料时间'].dt.hour == 14)]['温度'].mean()
    return temperature


# 夏季通风室外计算相对湿度
def The_relative_humidity_is_calculated_outside_the_ventilation_room_in_summer():
    hour_data['资料时间'] = pd.to_datetime(hour_data['资料时间'])
    grouped = hour_data.groupby([hour_data['资料时间'].dt.year, hour_data['资料时间'].dt.month])
    monthly_avg_temperature = grouped['温度'].mean()
    max_avg_temperature_month = monthly_avg_temperature.idxmax()
    relative = hour_data[(hour_data['资料时间'].dt.year == max_avg_temperature_month[0]) & (hour_data['资料时间'].dt.month == max_avg_temperature_month[1]) & (hour_data['资料时间'].dt.hour == 14)]['相对湿度'].mean()
    return relative

# 夏季空调室外计算日平均温度
def The_average_temperature_is_calculated_outside_the_air_conditioner_in_summer():
    temperature = day_data['平均气温'].sort_values(ascending=False).values[15]
    return temperature




# In[2]:测试函数
print('供暖计算室外温度：',Heating_calculates_the_outside_temperature())
print('冬季通风室外计算温度：',Calculate_the_temperature_outside_the_ventilation_room_in_winter())
print('冬季空调室外计算温度：',In_winter_the_air_conditioner_calculates_the_temperature_outside())
print('冬季空调室外计算相对湿度：',Relative_humidity_is_calculated_outside_the_air_conditioner_in_winter())
print('夏季空调室外计算干球温度：',In_summer_the_air_conditioner_calculates_the_dry_bulb_temperature_outside())
print('夏季通风室外计算温度：',The_temperature_is_calculated_outside_the_ventilation_room_in_summer())
print('夏季通风室外计算相对湿度：',The_relative_humidity_is_calculated_outside_the_ventilation_room_in_summer())
print('夏季空调室外计算日平均温度：',The_average_temperature_is_calculated_outside_the_air_conditioner_in_summer())


