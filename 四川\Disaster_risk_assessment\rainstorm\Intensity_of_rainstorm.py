import numpy as np
import pandas as pd
from scipy.spatial import cKDTree
import rasterio


def read_data():
    """读取并预处理数据"""
    # 从Excel文件读取数据
    data = pd.read_excel(r'D:\python\Disaster_risk_assessment\rainstorm\data\Day.xlsx')
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']
    data = data[data['年'] >= 2023]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year * 10000 + month * 100 + day, format='%Y%m%d')
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    # 按日期排序
    data.sort_index(inplace=True)
    # 提取所需列
    data = data[['区站号', '20-20时降水量']]

    # 获取站点经纬度
    file_path = r'D:\python\Disaster_risk_assessment\rainstorm\data\降水日数.xlsx'
    df = pd.read_excel(file_path)
    lon = df['经度'].values
    lat = df['纬度'].values
    station_id = df['站号'].values
    station_name = df['站名'].values

    station_info = pd.DataFrame({
        '区站号': station_id,
        '经度': lon,
        '纬度': lat,
        '站名': station_name
    })

    return data, station_info


# 计算暴雨年尺度危险性指标
def calculate_rainstorm_days():
    """
    选择以下6个指标:
    a.降水日数:单年降水超过0.1mm的累计天数,多年求平均；
    b.年降水量:年内逐日降水量累计值,多年求平均；
    c.暴雨日数:年内单日降水量超过50mm的累计日数,多年求平均；
    d.最长连续降水日数:年内连续降水日数最大值,多年取最大；
    e.最大连续降水量:连续降水过程中逐日降水量累计值,多年取最大；
    f.最大日降水量(20-20):年内20-20日降水量最大值,多年取最大。
    """
    # 读取数据
    data, station_info = read_data()
    result = {}
    # 按区站号分组
    for station_id, group in data.groupby('区站号'):
        # 计算降雨日数
        rainy_data = group[group['20-20时降水量'] >= 0.1]
        rainy_days = rainy_data.groupby(rainy_data.index.year).size()
        avg_rainy_days = rainy_days.mean()

        # 计算年降水量
        annual_precipitation = group.groupby(group.index.year)['20-20时降水量'].sum()
        avg_annual_precipitation = annual_precipitation.mean()

        # 计算暴雨日数
        rainstorm_data = group[group['20-20时降水量'] >= 50]
        rainstorm_days = rainstorm_data.groupby(rainstorm_data.index.year).size()
        avg_rainstorm_days = rainstorm_days + 0.1
        avg_rainstorm_days = avg_rainstorm_days.mean()


        # 计算最长连续降水日数
        def longest_consecutive_rain(df):
            consecutive = (df['20-20时降水量'] > 0).astype(int)
            consecutive = consecutive.groupby((consecutive != consecutive.shift()).cumsum()).cumsum()
            return consecutive.max()

        longest_consecutive_rain_days = group.groupby(group.index.year).apply(longest_consecutive_rain)
        max_longest_consecutive_rain_days = longest_consecutive_rain_days.max()

        # 计算最大连续降水量
        def max_consecutive_precipitation(df):
            consecutive_rain = (df['20-20时降水量'] > 0).astype(int)
            consecutive_groups = consecutive_rain.groupby((consecutive_rain != consecutive_rain.shift()).cumsum())
            max_precip = 0
            for _, group in consecutive_groups:
                if group.iloc[0] == 1:
                    start_index = group.index[0]
                    end_index = group.index[-1]
                    precip_sum = df.loc[start_index:end_index, '20-20时降水量'].sum()
                    if precip_sum > max_precip:
                        max_precip = precip_sum
            return max_precip

        max_consecutive_precip = group.groupby(group.index.year).apply(max_consecutive_precipitation)
        max_max_consecutive_precip = max_consecutive_precip.max()

        # 计算最大日降水量
        max_daily_precipitation = group.groupby(group.index.year)['20-20时降水量'].max()
        max_max_daily_precipitation = max_daily_precipitation.max()
        
        result[station_id] = {
            '降水日数': round(avg_rainy_days),
            '年降水量': round(avg_annual_precipitation, 1),
            '暴雨日数': round(avg_rainstorm_days),
            '最长连续降水日数': round(max_longest_consecutive_rain_days),
            '最大连续降水量': round(max_max_consecutive_precip, 1),
            '最大日降水量(20 - 20)': round(max_max_daily_precipitation, 1)
        }
    return result, station_info


# 归一化处理
def normalize(data):
    all_values = {
        '降水日数': [],
        '年降水量': [],
        '暴雨日数': [],
        '最长连续降水日数': [],
        '最大连续降水量': [],
        '最大日降水量(20 - 20)': []
    }
    # 收集所有区站的指标值
    for station_id, metrics in data.items():
        for key, value in metrics.items():
            all_values[key].append(value)
    # 计算每个指标的最大值和最小值
    min_max = {
        key: (min(values), max(values))
        for key, values in all_values.items()
    }
    normalized_data = {}
    # 对每个区站的指标进行归一化处理
    for station_id, metrics in data.items():
        normalized_metrics = {}
        for key, value in metrics.items():
            min_val, max_val = min_max[key]
            if max_val == min_val:
                normalized_metrics[key] = 0.5
            else:
                normalized_metrics[key] = 0.5 + 0.5 * (value - min_val) / (max_val - min_val)
        normalized_data[station_id] = normalized_metrics
    return normalized_data


# 计算暴雨过程强度指数
def calculate_rainstorm_intensity(normalized_data):
    rainstorm_intensity = {}
    num_metrics = 6
    for station_id, metrics in normalized_data.items():
        intensity = sum(metrics.values()) / num_metrics
        rainstorm_intensity[station_id] = intensity
    return rainstorm_intensity


# 调用函数计算指标
result, station_info = calculate_rainstorm_days()
# 归一化处理
normalized_result = normalize(result)
# 计算暴雨过程强度指数
rainstorm_intensity = calculate_rainstorm_intensity(normalized_result)

# 将结果保存到DataFrame
df_data = []
for station_id in result.keys():
    row = {
        '区站号': station_id,
        **result[station_id],
        **{f'归一化_{key}': value for key, value in normalized_result[station_id].items()},
        '暴雨过程强度指数': rainstorm_intensity[station_id]
    }
    df_data.append(row)

df = pd.DataFrame(df_data)

# 合并经纬度和站名信息
df = pd.merge(df, station_info, on='区站号', how='left')

# 保存到Excel文件
output_file = r'D:\python\Disaster_risk_assessment\rainstorm\data\rainstorm_metrics.xlsx'
df.to_excel(output_file, index=False)

print(f"结果已保存到 {output_file}")



# -----------------计算致灾因子危险性--------------------

# 定义IDW插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values)-1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)

# 自然断点法分类
def classify_risk_level(risk_score):
    import jenkspy
    breaks = jenkspy.jenks_breaks(risk_score, n_classes=5)
    risk_level = []
    for score in risk_score:
        if score <= breaks[1]:
            risk_level.append(1)
        elif breaks[1] < score <= breaks[2]:
            risk_level.append(2)
        elif breaks[2] < score <= breaks[3]:
            risk_level.append(3)
        elif breaks[3] < score <= breaks[4]:
            risk_level.append(4)
        else:
            risk_level.append(5)
    return risk_level

# 读取TIFF文件获取地理信息
tiff_path = r'D:\python\Disaster_risk_assessment\rainstorm\data\Pregnancy_environmental_factors\暴雨方案配置1_裁剪.tif'
with rasterio.open(tiff_path) as src:
    data = src.read(1)
    data[data == -999] = 0
    meta = src.meta
    bounds = src.bounds
    height = src.height
    width = src.width
    transform = src.transform  # 获取transform
    
    # 生成网格坐标
    x = np.linspace(bounds.left, bounds.right, width)
    y = np.linspace(bounds.top, bounds.bottom, height)
    grid_x, grid_y = np.meshgrid(x, y)  

# 读取暴雨过程强度指数
rainstorm_intensity = pd.read_excel(r'D:\python\Disaster_risk_assessment\rainstorm\data\rainstorm_metrics.xlsx')
station_id = rainstorm_intensity['区站号'].values
intensity = rainstorm_intensity['暴雨过程强度指数'].values
lon = rainstorm_intensity['经度'].values
lat = rainstorm_intensity['纬度'].values

# 进行IDW插值
interpolated_data = safe_idw_interpolation(np.column_stack((lon, lat)), intensity, grid_x, grid_y)

hazard_level = (1 + data) * interpolated_data 

# 进行自然断点法分类
risk_level_classes = np.array(classify_risk_level(hazard_level.flatten())).reshape(height, width)


# 保存为TIFF文件
output_file = r'D:\python\Disaster_risk_assessment\rainstorm\data\tif\hazard_level_self.tif'
with rasterio.open(
    output_file, 'w',
    driver='GTiff',
    height=height, 
    width=width,
    count=1,
    dtype=rasterio.float32,
    crs=meta['crs'],
    transform=meta['transform']
) as dst:
    dst.write(risk_level_classes.astype(rasterio.float32), 1)
    dst.update_tags(
        title='Hazard Level',
        units='unitless',
        nodata=0,  # 设置nodata值为0
        description='Hazard Level'  # 添加描述信息
    )


# ------------计算暴雨风险等级--------------
# 读取人口数据
population_data = rasterio.open(r'D:\python\Disaster_risk_assessment\High_Temp\day\data\czt\人口_裁剪.tif')
population = population_data.read(1)

risk_level_index = hazard_level ** 0.7 + population ** 0.3

# 转换为风险等级
risk_level_index_classes = classify_risk_level(risk_level_index.flatten())
# 将等级转换为整数
risk_level_index_classes = [int(level) for level in risk_level_index_classes]
# 将等级转换为二维数组
risk_level_index_classes = np.array(risk_level_index_classes).reshape(height, width)


# 将结果保存为TIFF文件
output_file = r'D:\python\Disaster_risk_assessment\rainstorm\data\tif\assess_level_self.tif'
with rasterio.open(
    output_file, 'w',
    driver='GTiff',
    height=height,
    width=width,
    count=1,
    dtype=rasterio.float32,
    crs=meta['crs'],
    transform=meta['transform']
) as dst:
    dst.write(risk_level_index_classes.astype(rasterio.float32), 1)
    dst.update_tags(
        title='Assess Level',
        units='unitless',
        nodata=0,  # 设置nodata值为0
        description='Assess Level',  # 添加描述信息        
        min=risk_level_index_classes.min(),  # 设置最小值
        max=risk_level_index_classes.max()  # 设置最大值 
    )
