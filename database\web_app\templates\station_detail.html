{% extends "base.html" %}

{% block title %}{{ station.name }} - 站点详情{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                        {{ station.name }}
                    </h2>
                    <p class="text-muted mb-0">
                        <i class="bi bi-hash me-1"></i>站点ID: {{ station.station_id }}
                        {% if station.province %}
                        <span class="mx-2">•</span>
                        <i class="bi bi-geo me-1"></i>{{ station.province }}
                        {% if station.city %}{{ station.city }}{% endif %}
                        {% endif %}
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="history.back()">
                        <i class="bi bi-arrow-left me-1"></i>返回
                    </button>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="observations">
                        <i class="bi bi-plus-circle me-1"></i>导入观测数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- 左侧：站点信息和地图 -->
        <div class="col-lg-5">
            <!-- 站点基本信息 -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>站点信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="text-muted small">纬度</label>
                                <div class="fw-bold">{{ "%.4f"|format(station.latitude) }}°</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="text-muted small">经度</label>
                                <div class="fw-bold">{{ "%.4f"|format(station.longitude) }}°</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="text-muted small">海拔高度</label>
                                <div class="fw-bold">
                                    {% if station.elevation %}
                                        {{ station.elevation }} 米
                                    {% else %}
                                        --
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="text-muted small">观测记录</label>
                                <div class="fw-bold">
                                    <span class="badge bg-primary fs-6">{{ observation_stats.basic.total_records }}</span> 条
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地图 -->
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-map me-2"></i>地理位置
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="station-map" style="height: 300px; border-radius: 0 0 0.375rem 0.375rem;"></div>
                </div>
            </div>
        </div>

        <!-- 右侧：观测数据统计 -->
        <div class="col-lg-7">
            <!-- 数据概览 -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart me-2"></i>数据概览
                    </h5>
                </div>
                <div class="card-body">
                    {% if observation_stats.basic.total_records > 0 %}
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <div class="stat-number text-primary">{{ observation_stats.basic.total_records }}</div>
                                <div class="stat-label">总记录数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <div class="stat-number text-success">{{ observation_stats.basic.years_count }}</div>
                                <div class="stat-label">覆盖年份</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <div class="stat-number text-warning">{{ observation_stats.basic.months_count }}</div>
                                <div class="stat-label">覆盖月份</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card text-center">
                                <div class="stat-number text-info">
                                    {% if observation_stats.basic.earliest_date and observation_stats.basic.latest_date %}
                                        {{ ((observation_stats.basic.latest_date|replace('-', '')|int - observation_stats.basic.earliest_date|replace('-', '')|int) / 10000)|round|int }}
                                    {% else %}
                                        0
                                    {% endif %}
                                </div>
                                <div class="stat-label">数据跨度(年)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="text-muted small">最早记录</label>
                                <div class="fw-bold">{{ observation_stats.basic.earliest_date or '--' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="text-muted small">最新记录</label>
                                <div class="fw-bold">{{ observation_stats.basic.latest_date or '--' }}</div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem; color: #6c757d; opacity: 0.5;"></i>
                        <h6 class="mt-3 text-muted">暂无观测数据</h6>
                        <p class="text-muted">该站点还没有观测记录</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 数据完整性 -->
            {% if observation_stats.basic.total_records > 0 %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart me-2"></i>数据完整性
                    </h5>
                    <button class="btn btn-sm btn-light" id="showDetailedDataBtn" data-bs-toggle="modal" data-bs-target="#detailedDataModal">
                        <i class="bi bi-table me-1"></i>查看详细数据
                    </button>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% for field, percentage in observation_stats.completeness.items() %}
                        <div class="col-md-6">
                            <div class="completeness-item">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="small">
                                        {% if field == 'temperature' %}
                                            <i class="bi bi-thermometer-half text-danger me-1"></i>温度
                                        {% elif field == 'precipitation' %}
                                            <i class="bi bi-cloud-rain text-primary me-1"></i>降水
                                        {% elif field == 'humidity' %}
                                            <i class="bi bi-droplet text-info me-1"></i>湿度
                                        {% elif field == 'wind_speed' %}
                                            <i class="bi bi-wind text-success me-1"></i>风速
                                        {% elif field == 'pressure' %}
                                            <i class="bi bi-speedometer text-warning me-1"></i>气压
                                        {% endif %}
                                    </span>
                                    <span class="small fw-bold">{{ percentage }}%</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    {% set progress_class = 'bg-success' if percentage >= 80 else ('bg-warning' if percentage >= 60 else 'bg-danger') %}
                                    <div class="progress-bar {{ progress_class }}" data-width="{{ percentage }}" style="width: 0;"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>


            {% endif %}
        </div>
    </div>

<!-- 详细数据弹窗 -->
<div class="modal fade" id="detailedDataModal" tabindex="-1" aria-labelledby="detailedDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="detailedDataModalLabel">
                    <i class="bi bi-table me-2"></i>{{ station.name }} - 详细观测数据
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <!-- 搜索和工具栏 -->
                <div class="p-3 border-bottom bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" id="detailedDataSearch" placeholder="搜索日期、温度、降水量...">
                                <button class="btn btn-outline-primary" type="button" id="searchDetailedBtn">
                                    搜索
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-outline-success btn-sm" id="exportDetailedCsv">
                                    <i class="bi bi-download me-1"></i>导出CSV
                                </button>
                                <button class="btn btn-outline-info btn-sm" id="refreshDetailedData">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container" id="tableContainer">
                    <!-- 滚动指示器 -->
                    <div class="scroll-indicators">
                        <div class="scroll-indicator scroll-indicator-top" id="scrollIndicatorTop">
                            <i class="bi bi-chevron-up"></i>
                            <span>向上滚动查看更多</span>
                        </div>
                        <div class="scroll-indicator scroll-indicator-bottom" id="scrollIndicatorBottom">
                            <i class="bi bi-chevron-down"></i>
                            <span>向下滚动查看更多</span>
                        </div>
                    </div>

                    <!-- 表格滚动容器 -->
                    <div class="table-responsive table-scroll-container" id="tableScrollContainer">
                        <table class="table table-hover table-sm mb-0" id="detailedDataTable">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th style="width: 120px;">观测日期</th>
                                    <th class="text-center" style="width: 80px;">温度(℃)</th>
                                    <th class="text-center" style="width: 90px;">降水量(mm)</th>
                                    <th class="text-center" style="width: 80px;">湿度(%)</th>
                                    <th class="text-center" style="width: 80px;">风速(m/s)</th>
                                    <th class="text-center" style="width: 80px;">气压(hPa)</th>
                                    <th class="text-center" style="width: 80px;">最高温(℃)</th>
                                    <th class="text-center" style="width: 80px;">最低温(℃)</th>
                                    <th class="text-center" style="width: 80px;">风向</th>
                                </tr>
                            </thead>
                            <tbody id="detailedDataTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 滚动到顶部按钮 -->
                    <div class="scroll-to-top" id="scrollToTop">
                        <i class="bi bi-arrow-up"></i>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="detailedDataLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">正在加载数据...</div>
                </div>

                <!-- 空状态 -->
                <div id="detailedDataEmpty" class="text-center py-5" style="display: none;">
                    <i class="bi bi-inbox" style="font-size: 3rem; color: #6c757d; opacity: 0.5;"></i>
                    <h6 class="mt-3 text-muted">没有找到观测数据</h6>
                    <p class="text-muted">请尝试调整搜索条件</p>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="text-muted small">
                        <span id="detailedDataInfo">显示第 1-50 条，共 0 条记录</span>
                    </div>
                    <div class="d-flex gap-2">
                        <!-- 分页控件 -->
                        <nav aria-label="详细数据分页">
                            <ul class="pagination pagination-sm mb-0" id="detailedDataPagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- 隐藏的数据，供JavaScript使用 -->
<script id="station-data" type="application/json">
{
    "id": "{{ station.station_id }}",
    "name": "{{ station.name }}",
    "latitude": {{ station.latitude }},
    "longitude": {{ station.longitude }},
    "hasAmapApiKey": {% if amap_api_key %}"{{ amap_api_key }}"{% else %}null{% endif %}
}
</script>

<style>
.info-item {
    padding: 0.5rem 0;
}

.info-item label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.stat-card {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    background: #f8f9fa;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.completeness-item {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    background: #f8f9fa;
}

.amap-logo, .amap-copyright {
    opacity: 0.7;
}

/* 表格滚动容器样式 */
.table-container {
    position: relative;
}

.table-scroll-container {
    max-height: 500px;
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.table-scroll-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 滚动指示器 */
.scroll-indicators {
    position: absolute;
    right: 20px;
    z-index: 10;
    pointer-events: none;
}

.scroll-indicator {
    display: none;
    align-items: center;
    gap: 8px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeInOut 2s ease-in-out;
}

.scroll-indicator-top {
    top: 10px;
}

.scroll-indicator-bottom {
    bottom: 10px;
}

.scroll-indicator.show {
    display: flex;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* 滚动到顶部按钮 */
.scroll-to-top {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    z-index: 10;
}

.scroll-to-top:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.scroll-to-top.show {
    display: flex;
}

/* 表格行交互效果 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
    cursor: pointer;
}

.table-hover tbody tr.selected {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

.table-hover tbody tr.table-row {
    transition: all 0.2s ease;
}

/* 表格滚动容器焦点样式 */
.table-scroll-container:focus {
    outline: 2px solid rgba(0, 123, 255, 0.3);
    outline-offset: -2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 1.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .table-scroll-container {
        max-height: 400px;
    }

    .scroll-indicator {
        font-size: 0.75rem;
        padding: 6px 10px;
    }

    .scroll-indicator span {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置进度条宽度
    const progressBars = document.querySelectorAll('.progress-bar[data-width]');
    progressBars.forEach(bar => {
        const width = bar.getAttribute('data-width');
        bar.style.width = width + '%';
    });

    // 从隐藏的JSON元素中解析数据
    const stationData = JSON.parse(document.getElementById('station-data').textContent);

    // 检查是否配置了高德地图API Key和AMap对象是否可用
    const hasAmapApiKey = stationData.hasAmapApiKey;
    const mapContainer = document.getElementById('station-map');
    
    if (hasAmapApiKey && typeof AMap !== 'undefined') {
        // 初始化地图
        const map = new AMap.Map('station-map', {
            zoom: 12,
            center: [stationData.longitude, stationData.latitude],
            mapStyle: 'amap://styles/normal'
        });
        
        // 添加站点标记
        const marker = new AMap.Marker({
            position: [stationData.longitude, stationData.latitude],
            title: stationData.name,
            icon: new AMap.Icon({
                size: new AMap.Size(32, 32),
                image: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="#dc3545">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                `)
            })
        });

        // 将标记添加到地图上
        map.add(marker);

        // 添加信息窗口
        const infoWindow = new AMap.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h6 style="margin: 0 0 5px 0; color: #333;">${stationData.name}</h6>
                    <p style="margin: 0; color: #666; font-size: 12px;">
                        经度: ${stationData.longitude.toFixed(4)}°<br>
                        纬度: ${stationData.latitude.toFixed(4)}°
                    </p>
                </div>
            `
        });

        marker.on('click', function() {
            infoWindow.open(map, marker.getPosition());
        });
    } else {
        // 如果没有配置API Key或AMap不可用，显示提示信息
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                    <div class="text-center">
                        <i class="bi bi-geo-alt" style="font-size: 3rem; color: #6c757d;"></i>
                        <p class="mt-2 mb-0 text-muted">地图功能需要配置高德地图API Key</p>
                        <small class="text-muted">经度: ${stationData.longitude.toFixed(4)}°, 纬度: ${stationData.latitude.toFixed(4)}°</small>
                    </div>
                </div>
            `;
        }
    }
    
    // 详细数据弹窗管理
    let currentPage = 1;
    let currentSearch = '';
    const pageSize = 50;

    // 详细数据弹窗显示时加载数据
    const detailedDataModal = document.getElementById('detailedDataModal');
    if (detailedDataModal) {
        detailedDataModal.addEventListener('shown.bs.modal', function() {
            loadDetailedData(1, '');
        });
    }

    // 搜索功能
    const searchBtn = document.getElementById('searchDetailedBtn');
    const searchInput = document.getElementById('detailedDataSearch');

    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', function() {
            currentSearch = searchInput.value.trim();
            loadDetailedData(1, currentSearch);
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                currentSearch = this.value.trim();
                loadDetailedData(1, currentSearch);
            }
        });
    }

    // 刷新数据
    const refreshBtn = document.getElementById('refreshDetailedData');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadDetailedData(currentPage, currentSearch);
        });
    }

    // 导出CSV
    const exportBtn = document.getElementById('exportDetailedCsv');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportDetailedDataToCsv();
        });
    }

    // 分页事件委托
    const pagination = document.getElementById('detailedDataPagination');
    if (pagination) {
        pagination.addEventListener('click', function(e) {
            e.preventDefault();

            const target = e.target.closest('a.page-link');
            if (!target) return;

            const pageItem = target.closest('.page-item');
            if (pageItem && pageItem.classList.contains('disabled')) return;

            const page = parseInt(target.getAttribute('data-page'));
            if (page && page > 0) {
                loadDetailedData(page, currentSearch);
            }
        });
    }

    // 滚动交互功能
    const tableScrollContainer = document.getElementById('tableScrollContainer');
    const scrollIndicatorTop = document.getElementById('scrollIndicatorTop');
    const scrollIndicatorBottom = document.getElementById('scrollIndicatorBottom');
    const scrollToTop = document.getElementById('scrollToTop');

    if (tableScrollContainer) {
        // 滚动事件处理
        tableScrollContainer.addEventListener('scroll', function() {
            const scrollTop = this.scrollTop;
            const scrollHeight = this.scrollHeight;
            const clientHeight = this.clientHeight;
            const scrollBottom = scrollHeight - scrollTop - clientHeight;

            // 显示/隐藏滚动到顶部按钮
            if (scrollTop > 100) {
                scrollToTop.classList.add('show');
            } else {
                scrollToTop.classList.remove('show');
            }

            // 显示滚动指示器
            if (scrollTop > 50 && scrollBottom > 50) {
                // 在中间位置，不显示指示器
                scrollIndicatorTop.classList.remove('show');
                scrollIndicatorBottom.classList.remove('show');
            } else if (scrollTop <= 50 && scrollBottom > 50) {
                // 接近顶部，显示向下滚动指示器
                scrollIndicatorTop.classList.remove('show');
                scrollIndicatorBottom.classList.add('show');
                setTimeout(() => scrollIndicatorBottom.classList.remove('show'), 2000);
            } else if (scrollBottom <= 50 && scrollTop > 50) {
                // 接近底部，显示向上滚动指示器
                scrollIndicatorBottom.classList.remove('show');
                scrollIndicatorTop.classList.add('show');
                setTimeout(() => scrollIndicatorTop.classList.remove('show'), 2000);
            }
        });

        // 滚动到顶部按钮点击事件
        if (scrollToTop) {
            scrollToTop.addEventListener('click', function() {
                tableScrollContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 键盘导航支持
        tableScrollContainer.addEventListener('keydown', function(e) {
            const scrollStep = 50;
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    this.scrollBy({ top: -scrollStep, behavior: 'smooth' });
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.scrollBy({ top: scrollStep, behavior: 'smooth' });
                    break;
                case 'PageUp':
                    e.preventDefault();
                    this.scrollBy({ top: -this.clientHeight * 0.8, behavior: 'smooth' });
                    break;
                case 'PageDown':
                    e.preventDefault();
                    this.scrollBy({ top: this.clientHeight * 0.8, behavior: 'smooth' });
                    break;
                case 'Home':
                    e.preventDefault();
                    this.scrollTo({ top: 0, behavior: 'smooth' });
                    break;
                case 'End':
                    e.preventDefault();
                    this.scrollTo({ top: this.scrollHeight, behavior: 'smooth' });
                    break;
            }
        });

        // 使表格容器可以获得焦点以支持键盘导航
        tableScrollContainer.setAttribute('tabindex', '0');
    }

    // 加载详细数据
    async function loadDetailedData(page = 1, search = '') {
        try {
            showDetailedDataLoading(true);

            const url = `/api/observations/${stationData.id}/detailed?page=${page}&limit=${pageSize}&search=${encodeURIComponent(search)}`;
            const response = await fetch(url);
            const data = await response.json();

            if (response.ok) {
                renderDetailedDataTable(data.observations);
                renderDetailedDataPagination(data);
                updateDetailedDataInfo(data);
                currentPage = page;
            } else {
                throw new Error(`服务器错误: ${response.status}`);
            }
        } catch (error) {
            console.error('加载详细数据失败:', error);
            showDetailedDataError();
        } finally {
            showDetailedDataLoading(false);
        }
    }

    // 显示加载状态
    function showDetailedDataLoading(show) {
        const loading = document.getElementById('detailedDataLoading');
        const table = document.getElementById('detailedDataTable');
        const empty = document.getElementById('detailedDataEmpty');

        if (loading) loading.style.display = show ? 'block' : 'none';
        if (table) table.style.display = show ? 'none' : 'table';
        if (empty) empty.style.display = 'none';
    }

    // 渲染数据表格
    function renderDetailedDataTable(observations) {
        const tbody = document.getElementById('detailedDataTableBody');
        const empty = document.getElementById('detailedDataEmpty');
        const tableScrollContainer = document.getElementById('tableScrollContainer');

        if (!tbody) return;

        if (!observations || observations.length === 0) {
            tbody.innerHTML = '';
            if (empty) empty.style.display = 'block';
            return;
        }

        if (empty) empty.style.display = 'none';

        // 格式化数值的辅助函数
        function formatValue(value) {
            if (value === null || value === undefined) return '<span class="text-muted">--</span>';
            if (typeof value === 'number') return value.toFixed(1);
            return value;
        }

        tbody.innerHTML = observations.map((obs, index) => `
            <tr data-row-index="${index}" class="table-row">
                <td>
                    <span class="badge bg-light text-dark">${obs.timestamp || '--'}</span>
                </td>
                <td class="text-center">
                    ${obs.temperature !== null && obs.temperature !== undefined ?
                        `<span class="fw-bold text-danger">${formatValue(obs.temperature)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.precipitation !== null && obs.precipitation !== undefined ?
                        `<span class="fw-bold text-primary">${formatValue(obs.precipitation)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.humidity !== null && obs.humidity !== undefined ?
                        `<span class="fw-bold text-info">${formatValue(obs.humidity)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.wind_speed !== null && obs.wind_speed !== undefined ?
                        `<span class="fw-bold text-success">${formatValue(obs.wind_speed)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.pressure !== null && obs.pressure !== undefined ?
                        `<span class="fw-bold text-warning">${formatValue(obs.pressure)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.max_temperature !== null && obs.max_temperature !== undefined ?
                        `<span class="fw-bold text-danger">${formatValue(obs.max_temperature)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.min_temperature !== null && obs.min_temperature !== undefined ?
                        `<span class="fw-bold text-primary">${formatValue(obs.min_temperature)}</span>` :
                        '<span class="text-muted">--</span>'}
                </td>
                <td class="text-center">
                    ${obs.wind_direction ? obs.wind_direction : '<span class="text-muted">--</span>'}
                </td>
            </tr>
        `).join('');

        // 重置滚动位置到顶部
        if (tableScrollContainer) {
            tableScrollContainer.scrollTop = 0;
        }

        // 添加行点击事件（可选：用于行选择或详情查看）
        tbody.querySelectorAll('tr.table-row').forEach(row => {
            row.addEventListener('click', function() {
                // 移除其他行的选中状态
                tbody.querySelectorAll('tr.selected').forEach(r => r.classList.remove('selected'));
                // 添加当前行的选中状态
                this.classList.add('selected');
            });
        });

        // 显示数据加载完成的提示
        showScrollHint();
    }

    // 显示滚动提示
    function showScrollHint() {
        const scrollIndicatorBottom = document.getElementById('scrollIndicatorBottom');
        if (scrollIndicatorBottom) {
            scrollIndicatorBottom.classList.add('show');
            setTimeout(() => {
                scrollIndicatorBottom.classList.remove('show');
            }, 3000);
        }
    }

    // 渲染分页
    function renderDetailedDataPagination(data) {
        const pagination = document.getElementById('detailedDataPagination');
        if (!pagination) return;

        const { page, total_pages } = data;
        let paginationHTML = '';

        // 上一页
        paginationHTML += `
            <li class="page-item ${page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${page - 1}" data-action="prev">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(total_pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}" data-action="page">${i}</a>
                </li>
            `;
        }

        // 下一页
        paginationHTML += `
            <li class="page-item ${page >= total_pages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${page + 1}" data-action="next">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    // 更新信息显示
    function updateDetailedDataInfo(data) {
        const info = document.getElementById('detailedDataInfo');
        if (!info) return;

        const { page, limit, total_count } = data;
        const start = (page - 1) * limit + 1;
        const end = Math.min(page * limit, total_count);

        info.textContent = `显示第 ${start}-${end} 条，共 ${total_count} 条记录`;
    }

    // 显示错误状态
    function showDetailedDataError() {
        const tbody = document.getElementById('detailedDataTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">加载数据失败，请稍后重试</div>
                    </td>
                </tr>
            `;
        }
    }

    // 导出CSV
    async function exportDetailedDataToCsv() {
        try {
            // 获取所有数据（不分页）
            const response = await fetch(`/api/observations/${stationData.id}/detailed?limit=10000&search=${encodeURIComponent(currentSearch)}`);
            const data = await response.json();

            if (!response.ok || !data.observations) {
                throw new Error('获取数据失败');
            }

            // 准备CSV内容
            const headers = ['观测日期', '温度(℃)', '降水量(mm)', '湿度(%)', '风速(m/s)', '气压(hPa)', '最高温(℃)', '最低温(℃)', '风向'];
            const csvContent = [headers.join(',')];

            data.observations.forEach(obs => {
                const row = [
                    obs.timestamp,
                    obs.temperature !== null ? obs.temperature.toFixed(1) : '',
                    obs.precipitation !== null ? obs.precipitation.toFixed(1) : '',
                    obs.humidity !== null ? obs.humidity.toFixed(1) : '',
                    obs.wind_speed !== null ? obs.wind_speed.toFixed(1) : '',
                    obs.pressure !== null ? obs.pressure.toFixed(1) : '',
                    obs.max_temperature !== null ? obs.max_temperature.toFixed(1) : '',
                    obs.min_temperature !== null ? obs.min_temperature.toFixed(1) : '',
                    obs.wind_direction || ''
                ];
                csvContent.push(row.join(','));
            });

            // 创建下载链接
            const blob = new Blob([csvContent.join('\\n')], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', `${stationData.name}_详细观测数据.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('导出CSV失败:', error);
            alert('导出失败，请稍后重试');
        }
    }
});
</script>
{% endblock %}
