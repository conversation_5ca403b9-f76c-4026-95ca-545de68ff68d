# In[0] :

from matplotlib import pyplot as plt
import numpy as np
import pandas as pd
from scipy.spatial import cKDTree
import geopandas as gpd
import cartopy.feature as cfeature
import cartopy.mpl.ticker as cticker
import cartopy.crs as ccrs
from matplotlib.path import Path
from cartopy.mpl.patch import geos_to_path

plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文显示字体为黑体（SimHei）
plt.rcParams['axes.unicode_minus'] = False     # 正确显示负号


# 读取站点信息文件
file_path1 = r'D:\python\monitoring\data\station\station_info.xlsx'
station_info = pd.read_excel(file_path1)
station_ids = station_info['station_id'].astype(str).values

# 读取 excel 文件
file_path = r'D:\python\monitoring\data\station\SURF_CHN_MUL_DAY(2020-2024).xlsx'
data = pd.read_excel(file_path)

# 获取年月日列
year = data['年']
month = data['月']
day = data['日']

# 将年月日组合成 datetime 格式
date_time = pd.to_datetime(year * 10000 + month * 100 + day, format='%Y%m%d')
# 删除原始的年月日列
data.drop(['年', '月', '日'], axis=1, inplace=True)
# 添加新的日期列并设置为索引
data['date'] = date_time
data.set_index('date', inplace=True)
# 按日期排序
data.sort_index(inplace=True)

# IDW 插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values) - 1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)


# 不同时间尺度的不同要素计算函数
def precipitation_calculation(station_data, var_name, date_range, time_scale, method):
    if time_scale == 'day':
        var_data = station_data[var_name][date_range]
        if method == 'sum':
            cumulative_var = var_data.sum()
        elif method == 'mean':
            cumulative_var = var_data.mean()
    elif time_scale == 'pentad':
        pentad_data = []
        for i in range(0, len(date_range), 5):
            pentad_dates = date_range[i:i + 5]
            pentad_var = station_data[var_name][pentad_dates]
            if method == 'sum':
                pentad_cumulative = pentad_var.sum()
            elif method == 'mean':
                pentad_cumulative = pentad_var.mean()
            pentad_data.append(pentad_cumulative)
        cumulative_var = np.mean(pentad_data) if method == 'mean' else np.sum(pentad_data)
    elif time_scale == 'decade':
        decade1_dates = date_range[:10]
        decade2_dates = date_range[10:20]
        decade3_dates = date_range[20:]

        decade1_var = station_data[var_name][decade1_dates]
        decade2_var = station_data[var_name][decade2_dates]
        decade3_var = station_data[var_name][decade3_dates]

        if method == 'sum':
            decade1_cumulative = decade1_var.sum()
            decade2_cumulative = decade2_var.sum()
            decade3_cumulative = decade3_var.sum()
        elif method == 'mean':
            decade1_cumulative = decade1_var.mean()
            decade2_cumulative = decade2_var.mean()
            decade3_cumulative = decade3_var.mean()

        cumulative_var = decade1_cumulative + decade2_cumulative + decade3_cumulative if method == 'sum' else np.mean(
            [decade1_cumulative, decade2_cumulative, decade3_cumulative])
    elif time_scale == 'month':
        var_data = station_data[var_name][date_range]
        if method == 'sum':
            cumulative_var = var_data.groupby(pd.Grouper(freq='ME')).sum().sum()
        elif method == 'mean':
            cumulative_var = var_data.groupby(pd.Grouper(freq='ME')).mean().mean()
    elif time_scale == 'season':
        var_data = station_data[var_name][date_range]
        if method == 'sum':
            cumulative_var = var_data.groupby(pd.Grouper(freq='Q')).sum().sum()
        elif method == 'mean':
            cumulative_var = var_data.groupby(pd.Grouper(freq='Q')).mean().mean()
    elif time_scale == 'year':
        var_data = station_data[var_name][date_range]
        if method == 'sum':
            cumulative_var = var_data.groupby(pd.Grouper(freq='Y')).sum().sum()
        elif method == 'mean':
            cumulative_var = var_data.groupby(pd.Grouper(freq='Y')).mean().mean()
    return cumulative_var

# 不同时间尺度的不同要素计算与绘图函数
def precipitation_with_time_scale(station_ids, data, var_name, date_range, cmap, method='sum', time_scale='day',
                                  file_path=None):
    # 初始化数据列表
    precipitation = []

    for station_id in station_ids:
        # 使用原始数据的副本进行筛选
        station_data = data[data['区站号(字符)'].astype(str) == station_id].copy()
        station_data.replace([999990,999999], np.nan, inplace=True)

        # 计算统计时段变量
        cumulative_var = precipitation_calculation(station_data, var_name, date_range, time_scale, method)

        # 获取站点经纬度信息
        station_row = station_info[station_info['station_id'].astype(str) == station_id]
        lon = station_row['lon'].values[0]
        lat = station_row['lat'].values[0]

        # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
        precipitation.append({
            'station_id': station_id,
            'lon': lon,
            'lat': lat,
            'cumulative_var': cumulative_var
        })

    # 将列表转换为 DataFrame
    cumulative_df = pd.DataFrame(precipitation)

    # -------------插值--------------------
    lon = cumulative_df['lon'].values
    lat = cumulative_df['lat'].values
    precipitation_values = cumulative_df['cumulative_var'].values
    x = np.linspace(96, 109, 100)
    y = np.linspace(25.5, 35, 100)
    xx, yy = np.meshgrid(x, y)
    zz = safe_idw_interpolation(np.column_stack((lon, lat)), precipitation_values, xx, yy, power=2, k=10)

    leftlon,rightlon,lowerlat,upperlat = (96, 109, 25.5, 35)
    def contour_map(fig,img_extent):
        img_extent = [leftlon,rightlon,lowerlat,upperlat]
        fig.set_extent(img_extent, crs=ccrs.PlateCarree())
        fig.add_feature(cfeature.COASTLINE.with_scale('50m'),alpha=0.5)
        fig.add_feature(cfeature.LAKES,alpha=0.5)
        fig.set_xticks(np.arange(96, 109, 2.5),crs=ccrs.PlateCarree())
        fig.set_yticks(np.arange(25.5, 35, 1),crs=ccrs.PlateCarree())
        lon_formatter = cticker.LongitudeFormatter()
        lat_formatter = cticker.LatitudeFormatter()
        fig.xaxis.set_major_formatter(lon_formatter)
        fig.yaxis.set_major_formatter(lat_formatter)
    
    shp = gpd.read_file(r'D:\python\monitoring\data\shp\省界_region.shp',encoding='gbk')
    a = shp['geometry']

    # ------------------绘图---------------------
    fig = plt.figure(figsize=(15, 10))
    proj = ccrs.PlateCarree()
    ax = fig.add_axes([0.05, 0.3, 0.5, 0.5], projection=proj)
    contour_map(ax,[leftlon,rightlon,lowerlat,upperlat])
    contour = ax.contourf(xx, yy, zz, levels=15, cmap=cmap)
    ax.scatter(lon, lat, c=precipitation_values, cmap=cmap, s=40, edgecolor='black', linewidth=0.5)

    # 生成裁剪路径
    path_clip = Path.make_compound_path(*geos_to_path(a.to_list()))
    # 将裁剪路径应用到图层
    contour.set_clip_path(path_clip, transform=ax.transData)
    # 绘制多边形边缘线
    ax.add_geometries(a, crs=ccrs.PlateCarree(), facecolor='none', edgecolor='black')
    ax.set_title(f'{var_name} {date_range[0]}~{date_range[-1]}')
    plt.colorbar(contour, label=f'{var_name}')
    # 设置坐标轴标签
    ax.set_xlabel('经度')
    ax.set_ylabel('纬度')
    # 设置坐标轴范围
    ax.set_xlim(leftlon, rightlon)
    ax.set_ylim(lowerlat, upperlat)


    if file_path:
        plt.savefig(rf'{file_path}', dpi=300, bbox_inches='tight')
    plt.show()

    return cumulative_df


# In[1] :降水

date_range = pd.date_range(start='2024-05-01', end='2024-08-31', freq='D')
cumulative_df = precipitation_with_time_scale(station_ids, data, '20-20时降水量', date_range, cmap='Blues',
                method='sum', time_scale='month',# 可选: day, pentad, decade, month, season, year
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\mon\mon_precipitation_distribution.png')
print(cumulative_df)

cumulative_df.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\mon_precipitation_result.xlsx', index=False)

# In[2] :平均气温

date_range = pd.date_range(start='2024-03-01', end='2024-7-31', freq='D')
cumulative_df = precipitation_with_time_scale(station_ids, data, '平均气温', date_range, cmap='coolwarm',
                method='mean', time_scale='month',# 可选: day, pentad, decade, month, season, year
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\mon\mon_ave_T_distribution.png')
print(cumulative_df)

cumulative_df.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\mon_ave_T_result.xlsx', index=False)

# In[3] :平均2分钟风速

date_range = pd.date_range(start='2024-03-01', end='2024-06-30', freq='D')
cumulative_df = precipitation_with_time_scale(station_ids, data, '平均2分钟风速', date_range, cmap='viridis',
                method='mean', time_scale='month',# 可选: day, pentad, decade, month, season, year
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\mon\mon_ave_2min_wind_distribution.png')
print(cumulative_df)

cumulative_df.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\mon_ave_2min_wind_result.xlsx', index=False)

# In[4] :平均相对湿度

date_range = pd.date_range(start='2022-04-01', end='2022-07-31', freq='D')
cumulative_df = precipitation_with_time_scale(station_ids, data, '平均相对湿度', date_range, cmap='YlGnBu',
                method='mean', time_scale='month',# 可选: day, pentad, decade, month, season, year
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\mon\mon_ave_RH_distribution.png')
print(cumulative_df)

cumulative_df.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\mon_ave_RH_result.xlsx', index=False)


# In[5] :平均地面温度
date_range = pd.date_range(start='2024-01-01', end='2024-03-31', freq='D')
cumulative_df = precipitation_with_time_scale(station_ids, data, '平均地面温度', date_range, cmap='YlOrRd',
                method='mean', time_scale='month',# 可选: day, pentad, decade, month, season, year
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\mon\mon_ave_GT_distribution.png')
print(cumulative_df)

cumulative_df.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\mon_ave_GT_result.xlsx', index=False)

# %%
