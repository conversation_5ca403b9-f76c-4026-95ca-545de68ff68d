import tkinter as tk
from tkinter import ttk, messagebox

class FilterWindow:
    def __init__(self, parent, data_processor, result_text):
        self.window = tk.Toplevel(parent)
        self.window.title("数据筛选")
        self.window.geometry("400x300")
        self.data_processor = data_processor
        self.result_text = result_text
        
        self.setup_ui()

    def setup_ui(self):
        # 添加筛选选项
        ttk.Label(self.window, text="选择列：").pack(pady=5)
        self.column_var = tk.StringVar()
        column_combo = ttk.Combobox(self.window, textvariable=self.column_var)
        column_combo['values'] = list(self.data_processor.df.columns)
        column_combo.pack(pady=5)
        
        ttk.Label(self.window, text="筛选条件：").pack(pady=5)
        self.condition_var = tk.StringVar()
        condition_entry = ttk.Entry(self.window, textvariable=self.condition_var)
        condition_entry.pack(pady=5)
        
        ttk.Button(self.window, text="应用筛选", command=self.apply_filter).pack(pady=10)

    def apply_filter(self):
        try:
            column = self.column_var.get()
            condition = self.condition_var.get()
            
            filtered_df = self.data_processor.filter_data(column, condition)
            if filtered_df is not None:
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, str(filtered_df))
                self.window.destroy()
            else:
                messagebox.showerror("错误", "筛选失败，请检查筛选条件")

        except Exception as e:
            messagebox.showerror("错误", f"筛选失败：{str(e)}")