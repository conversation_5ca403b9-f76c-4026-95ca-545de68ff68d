
#%%
import pandas as pd
from tqdm import tqdm

file_path = r'D:\python\山西\Climate events\Rainy season\data\降水1991-2020.xlsx'
df = pd.read_excel(file_path)

df['资料时间'] = pd.to_datetime(df['资料时间'])
df['20-20时降水量'] = df['20-20时降水量'].replace({999999: None, 999990: None, 999998: None})
df['year'] = df['资料时间'].dt.year
df['month'] = df['资料时间'].dt.month
df['day'] = df['资料时间'].dt.day

results = []
stations = df['区站号(字符)'].unique()


for station in tqdm(stations):
    station_df = df[df['区站号(字符)'] == station].copy()
    daily_climatology = station_df.groupby(['month', 'day'])['20-20时降水量'].mean().reset_index()
    daily_climatology['日期'] = daily_climatology.apply(lambda x: f"{x['month']:02d}-{x['day']:02d}", axis=1)
    daily_climatology['区站号(字符)'] = station
    results.append(daily_climatology[['日期', '区站号(字符)', '20-20时降水量']])


result_df = pd.concat(results, ignore_index=True)
result_df['20-20时降水量'] = result_df['20-20时降水量']
output_path = r'D:\python\山西\Climate events\Rainy season\data\20-20时降水量气候态.xlsx'
result_df.to_excel(output_path, index=False)

print('降水气候态计算完成')
# %%
