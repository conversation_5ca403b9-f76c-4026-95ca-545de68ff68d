#%%
import pandas as pd
from datetime import datetime
from tqdm import tqdm
import numpy as np

file_path = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\平均气温1991-2020.xlsx'
df = pd.read_excel(file_path)
stations = df['区站号(字符)'].unique()
df['资料时间'] = pd.to_datetime(df['资料时间'])
df['平均气温'] = df['平均气温'].replace({999999: np.nan, 999990: np.nan, 999998: np.nan})

results = []
for station in tqdm(stations):
    station_df = df[df['区站号(字符)'] == station].copy()
    groups = station_df.groupby(station_df['资料时间'].dt.month)
    for month, group in groups:
        groups1 = group.groupby(station_df['资料时间'].dt.day)
        for day, group2 in groups1:
            station_average_data = group2['平均气温'].mean()
            date = datetime(2000, month, day).strftime('%m-%d')
            results.append({
                '日期': date,
                '区站号(字符)': station,
                '平均气温': round(station_average_data,1),      
            })

result_df = pd.DataFrame(results)
result_df.to_excel(r'D:\python\山西\data\平均气温气候态.xlsx', index=False)
print('气温气候态计算完成')


#%%


file_path = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\降水1991-2020.xlsx'
df = pd.read_excel(file_path)

stations = df['区站号(字符)'].unique() 
results = []
for station in tqdm(stations):
    station_df = df[df['区站号(字符)'] == station].copy()
    station_df['资料时间'] = pd.to_datetime(station_df['资料时间'])
    groups = station_df.groupby(station_df['资料时间'].dt.month)
    for month, group in groups:
        groups1 = group.groupby(station_df['资料时间'].dt.day)
        for day, group2 in groups1:
            group2['20-20时降水量'] = group2['20-20时降水量'].replace({999999: np.nan, 999990: np.nan, 999998: np.nan})
            station_average_data = group2['20-20时降水量'].mean()
            date = datetime(2000, month, day).strftime('%m-%d')
            results.append({
                '日期': date,
                '区站号(字符)': station,
                '20-20时降水量': round(station_average_data,1), 
            })
result_df = pd.DataFrame(results)
result_df.to_excel(r'D:\python\山西\data\20-20时降水量气候态.xlsx', index=False)
print('降水气候态计算完成')


# %%

# %%
