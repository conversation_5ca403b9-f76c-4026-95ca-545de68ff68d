import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd

class StatisticsWindow:
    def __init__(self, parent, data_processor, result_text):
        self.window = tk.Toplevel(parent)
        self.window.title("常规统计")
        self.window.geometry("200x150")
        self.window.transient(parent)
        self.data_processor = data_processor
        self.result_text = result_text

        self.setup_ui()

    def setup_ui(self):
        # 添加按钮
        ttk.Button(self.window, text="连续变化", command=self.show_continuous_variation).pack(
            pady=10, padx=20, fill=tk.X
        )
        ttk.Button(self.window, text="历年同期", command=self.show_same_period_analysis).pack(
            pady=10, padx=20, fill=tk.X
        )

    def show_continuous_variation(self):
        analysis_window = ContinuousVariationWindow(
            self.window, self.data_processor, self.result_text
        )

    def show_same_period_analysis(self):
        messagebox.showinfo("提示", "历年同期分析功能正在开发中...")


class ContinuousVariationWindow:
    def __init__(self, parent, data_processor, result_text):
        self.window = tk.Toplevel(parent)
        self.window.title("连续变化分析")
        self.window.geometry("600x500")
        self.data_processor = data_processor
        self.result_text = result_text

        self.setup_ui()

    def setup_ui(self):
        # 时间范围选择
        self.setup_time_frame()
        # 要素选择
        self.setup_element_frame()
        # 统计方法选择
        self.setup_method_frame()
        # 处理按钮
        ttk.Button(self.window, text="开始处理", command=self.process_data).pack(pady=10)

    def setup_time_frame(self):
        time_frame = ttk.LabelFrame(self.window, text="时间范围选择", padding=8)
        time_frame.pack(fill=tk.X, padx=5, pady=5)

        # 开始时间
        start_frame = ttk.Frame(time_frame)
        start_frame.pack(fill=tk.X, pady=5)
        ttk.Label(start_frame, text="开始时间：").pack(side=tk.LEFT)
        self.start_year = ttk.Entry(start_frame, width=6)
        self.start_year.pack(side=tk.LEFT, padx=2)
        ttk.Label(start_frame, text="年").pack(side=tk.LEFT)
        self.start_month = ttk.Entry(start_frame, width=4)
        self.start_month.pack(side=tk.LEFT, padx=2)
        ttk.Label(start_frame, text="月").pack(side=tk.LEFT)
        self.start_day = ttk.Entry(start_frame, width=4)
        self.start_day.pack(side=tk.LEFT, padx=2)
        ttk.Label(start_frame, text="日").pack(side=tk.LEFT)

        # 结束时间
        end_frame = ttk.Frame(time_frame)
        end_frame.pack(fill=tk.X, pady=5)
        ttk.Label(end_frame, text="结束时间：").pack(side=tk.LEFT)
        self.end_year = ttk.Entry(end_frame, width=6)
        self.end_year.pack(side=tk.LEFT, padx=2)
        ttk.Label(end_frame, text="年").pack(side=tk.LEFT)
        self.end_month = ttk.Entry(end_frame, width=4)
        self.end_month.pack(side=tk.LEFT, padx=2)
        ttk.Label(end_frame, text="月").pack(side=tk.LEFT)
        self.end_day = ttk.Entry(end_frame, width=4)
        self.end_day.pack(side=tk.LEFT, padx=2)
        ttk.Label(end_frame, text="日").pack(side=tk.LEFT)

    def setup_element_frame(self):
        element_frame = ttk.LabelFrame(self.window, text="要素选择", padding=8)
        element_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建要素列表框和滚动条
        self.elements_listbox = tk.Listbox(element_frame, selectmode=tk.MULTIPLE)
        scrollbar = ttk.Scrollbar(element_frame, orient=tk.VERTICAL, command=self.elements_listbox.yview)
        self.elements_listbox.configure(yscrollcommand=scrollbar.set)
        self.elements_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加数据列到列表框
        for col in self.data_processor.df.columns:
            self.elements_listbox.insert(tk.END, col)

    def setup_method_frame(self):
        method_frame = ttk.LabelFrame(self.window, text="统计方法", padding=8)
        method_frame.pack(fill=tk.X, padx=5, pady=5)
        self.method_var = tk.StringVar(value="sum")
        ttk.Radiobutton(method_frame, text="求和", variable=self.method_var, value="sum").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(method_frame, text="平均", variable=self.method_var, value="mean").pack(side=tk.LEFT, padx=10)

    def process_data(self):
        try:
            # 获取选择的要素
            selected_indices = self.elements_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("警告", "请至少选择一个要素！")
                return
            selected_elements = [self.elements_listbox.get(i) for i in selected_indices]

            # 获取时间范围
            start = f"{self.start_year.get()}-{self.start_month.get()}-{self.start_day.get()}"
            end = f"{self.end_year.get()}-{self.end_month.get()}-{self.end_day.get()}"
            start_date = pd.to_datetime(start)
            end_date = pd.to_datetime(end)

            # 处理数据
            success, result = self.data_processor.calculate_continuous_variation(
                selected_elements, self.method_var.get(), start_date, end_date
            )

            if success:
                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, f"统计时段：{start} 至 {end}\n\n")
                self.result_text.insert(tk.END, str(result))
                self.window.destroy()
            else:
                messagebox.showerror("错误", f"处理失败：{result}")

        except Exception as e:
            messagebox.showerror("错误", f"处理失败：{str(e)}")