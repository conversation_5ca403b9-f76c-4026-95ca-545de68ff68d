import pandas as pd
import numpy as np

class DataProcessor:
    def __init__(self):
        self.df = None

    def load_data(self, file_path):
        """加载Excel文件数据"""
        try:
            self.df = pd.read_excel(file_path)
            return True, self.df.columns
        except Exception as e:
            return False, str(e)

    def get_preview(self):
        """获取数据预览"""
        if self.df is None:
            return None
        preview_df = self.df.head()
        preview_df.columns = [str(col)[:15] + '...' if len(str(col)) > 15 else str(col) for col in preview_df.columns]
        return preview_df

    def get_basic_statistics(self):
        """获取基础统计信息"""
        if self.df is None:
            return None
        return self.df.describe()

    def filter_data(self, column, condition):
        """根据条件筛选数据"""
        try:
            return self.df[self.df[column].astype(str).str.contains(condition)]
        except Exception as e:
            return None

    def handle_missing_values(self, columns, values, replace_value):
        """处理缺失值"""
        try:
            replace_dict = {col: {val: replace_value for val in values} for col in columns}
            self.df = self.df.replace(replace_dict)
            return True, self.df[columns].head()
        except Exception as e:
            return False, str(e)

    def calculate_continuous_variation(self, elements, method, start_date, end_date):
        """计算连续变化"""
        try:
            result_df = pd.DataFrame()
            for element in elements:
                if method == "sum":
                    result = self.df[element].sum()
                else:
                    result = self.df[element].mean()
                result_df[element] = [result]
            return True, result_df
        except Exception as e:
            return False, str(e)