import math

def get_angle(lon1, lat1, lon2, lat2):
    deg2rad = math.pi / 180
    dlat = (lat2 - lat1) * deg2rad
    dlon = (lon2 - lon1) * deg2rad
    y = math.sin(dlon) * math.cos(lat2 * deg2rad)
    x = math.cos(lat1 * deg2rad) * math.sin(lat2 * deg2rad) - math.sin(lat1 * deg2rad) * math.cos(lat2 * deg2rad) * math.cos(dlon)
    angle = math.atan2(y, x) * 180 / math.pi
    return angle

# 示例用法
lat1 = 30.6097  # 纬度1
lon1 = 106.1224  # 经度1
lat2 =  31.53 # 纬度2
lon2 = 106.47  # 经度2

bearing = get_angle(lon1, lat1, lon2, lat2)
print(f"方位角（初始方位角）是: {bearing}°")
