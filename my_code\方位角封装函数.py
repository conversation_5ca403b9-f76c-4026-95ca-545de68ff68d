import math

def get_angle(lon1, lat1, lon2, lat2):
    deg2rad = math.pi / 180
    dlat = (lat2 - lat1) * deg2rad
    dlon = (lon2 - lon1) * deg2rad
    y = math.sin(dlon) * math.cos(lat2 * deg2rad)
    x = math.cos(lat1 * deg2rad) * math.sin(lat2 * deg2rad) - math.sin(lat1 * deg2rad) * math.cos(lat2 * deg2rad) * math.cos(dlon)
    angle = math.atan2(y, x) * 180 / math.pi
    return angle

def main():
    # 从键盘输入经纬度
    lat1 = float(input("请输入第一个点的纬度: "))
    lon1 = float(input("请输入第一个点的经度: "))
    lat2 = float(input("请输入第二个点的纬度: "))
    lon2 = float(input("请输入第二个点的经度: "))

    # 计算方位角
    bearing = get_angle(lon1, lat1, lon2, lat2)
    print(f"方位角（初始方位角）是: {bearing}°")

if __name__ == "__main__":
    main()