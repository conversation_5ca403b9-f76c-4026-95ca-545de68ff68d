import struct
import numpy as np
import xarray as xr
import datetime

# 数据文件路径
file_path = r'D:\python\data\Z_SATE_C_BAWX_20250325200917_P_FY2G_SSI_VIS_OTG_20250325_TOAD.AWX'

# 1. 数据读取
with open(file_path, 'rb') as f:
    # 2. 元数据解析
    f.seek(52)
    (benchmark,) = struct.unpack('h', f.read(2))
    (scale_factor,) = struct.unpack('h', f.read(2))
    f.seek(78)
    (max_lat,) = struct.unpack('h', f.read(2))
    (min_lon,) = struct.unpack('h', f.read(2))
    (min_lat,) = struct.unpack('h', f.read(2))
    (max_lon,) = struct.unpack('h', f.read(2))
    max_lat = max_lat / 100
    min_lon = min_lon / 100
    min_lat = min_lat / 100
    max_lon = max_lon / 100
    f.seek(92)
    (lon_size,) = struct.unpack('h', f.read(2))
    (lat_size,) = struct.unpack('h', f.read(2))

    # 3. 数据解析
    f.seek(484)
    data = []
    for i in range(lon_size * lat_size):
        (aa,) = struct.unpack('h', f.read(2))
        data.append(aa)

# 4. 数据处理
grid = np.reshape(data, [1, lon_size, lat_size])
grid = (grid - benchmark) / scale_factor

lon = np.zeros(lon_size)
lat = np.zeros(lat_size)
for j in range(lon_size):
    lat[j] = max_lat - j
    lon[j] = min_lon + j

# 假设数据时间为文件名中的日期
data_time_str = '2025-03-25 00:00:00'

# 假设数据测量的时间间隔是 24 小时
time_interval_seconds = 24*3600
# 将数据从 W/m² 转换为 MJ/m²
grid = grid * time_interval_seconds / 1e6  # 1e6 是从焦耳转换为兆焦耳的系数

data_time = np.datetime64(datetime.datetime.strptime(data_time_str, '%Y-%m-%d %H:%M:%S'))
times = [data_time]

# 创建 xarray.Dataset 对象
ds = xr.Dataset(data_vars={"dsrf": (['time', 'lat', 'lon'], grid)},
                coords={'time': times, 'lat': lat, 'lon': lon})

# 打印解析结果
print("基准值:", benchmark)
print("比例因子:", scale_factor)
print("最大纬度:", max_lat)
print("最小经度:", min_lon)
print("最小纬度:", min_lat)
print("最大经度:", max_lon)
print("经度网格大小:", lon_size)
print("纬度网格大小:", lat_size)
print("数据网格形状:", grid.shape)

print("数据集内容:")
print(ds)
print(ds["dsrf"].values.max())


