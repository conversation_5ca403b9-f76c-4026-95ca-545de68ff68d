#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的气象数据库批量导入模块
提供高性能的批量数据导入功能，显著提升大数据量的导入速度
"""

import sqlite3
import csv
import os
import traceback
import pandas as pd

class WeatherDatabaseBatchOptimized:
    """优化的气象数据库批量导入类"""
    
    def __init__(self, db_path='weather_stations.db'):
        """初始化数据库连接"""
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
    
    def _parse_float(self, value):
        """安全地将字符串转换为浮点数"""
        if value is None or value == '' or str(value).strip() == '':
            return None
        try:
            # 处理可能的占位符值
            if str(value) in ['999990.0', '999999', '999990', '--', 'N/A']:
                return None
            return float(str(value).strip())
        except (ValueError, TypeError):
            return None

    def _normalize_timestamp(self, timestamp):
        """标准化时间戳格式，统一为 YYYY-MM-DD 格式

        支持的输入格式:
        - 2023-11-30 00:00:00 -> 2023-11-30
        - 2023-11-30 -> 2023-11-30
        - 2023/11/30 -> 2023-11-30
        - 其他格式尝试自动解析
        """
        if not timestamp:
            return None

        timestamp_str = str(timestamp).strip()

        # 如果已经是 YYYY-MM-DD 格式，直接返回
        if len(timestamp_str) == 10 and timestamp_str.count('-') == 2:
            try:
                # 验证日期格式是否正确
                from datetime import datetime
                datetime.strptime(timestamp_str, '%Y-%m-%d')
                return timestamp_str
            except ValueError:
                pass

        # 处理包含时间的格式 (如: 2023-11-30 00:00:00)
        if ' ' in timestamp_str:
            date_part = timestamp_str.split(' ')[0]
            # 递归调用处理日期部分
            return self._normalize_timestamp(date_part)

        # 处理斜杠分隔的日期格式 (如: 2023/11/30)
        if '/' in timestamp_str:
            try:
                from datetime import datetime
                # 尝试解析 YYYY/MM/DD 格式
                dt = datetime.strptime(timestamp_str, '%Y/%m/%d')
                return dt.strftime('%Y-%m-%d')
            except ValueError:
                try:
                    # 尝试解析 MM/DD/YYYY 格式
                    dt = datetime.strptime(timestamp_str, '%m/%d/%Y')
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    pass

        # 尝试其他常见格式
        try:
            from datetime import datetime
            import pandas as pd

            # 使用pandas的智能日期解析
            dt = pd.to_datetime(timestamp_str)
            return dt.strftime('%Y-%m-%d')
        except:
            # 如果所有解析都失败，返回原始字符串
            print(f"警告: 无法解析时间格式 '{timestamp_str}'，保持原样")
            return timestamp_str
    
    def _validate_coordinates(self, latitude, longitude):
        """验证经纬度的有效性"""
        if latitude is None or longitude is None:
            raise ValueError("经纬度不能为空")
        if not (-90 <= latitude <= 90):
            raise ValueError(f"纬度必须在-90到90之间，当前值: {latitude}")
        if not (-180 <= longitude <= 180):
            raise ValueError(f"经度必须在-180到180之间，当前值: {longitude}")
    
    def add_stations_batch(self, stations_data, batch_size=1000):
        """批量添加站点数据，提高大数据量导入性能
        
        Args:
            stations_data: 站点数据列表，每个元素是包含站点信息的字典
            batch_size: 批量处理大小，默认1000条
        
        Returns:
            tuple: (成功数量, 失败数量)
        """
        success_count = 0
        error_count = 0
        
        try:
            # 开始事务
            self.cursor.execute('BEGIN TRANSACTION')
            
            for i in range(0, len(stations_data), batch_size):
                batch = stations_data[i:i + batch_size]
                batch_values = []
                
                for station in batch:
                    try:
                        # 验证必需参数
                        station_id = station.get('station_id')
                        name = station.get('name')
                        latitude = station.get('latitude')
                        longitude = station.get('longitude')
                        
                        if not station_id or not name:
                            print(f"跳过无效站点数据: {station}")
                            error_count += 1
                            continue
                            
                        # 验证经纬度
                        self._validate_coordinates(latitude, longitude)
                        
                        batch_values.append((
                            station_id, name, latitude, longitude,
                            station.get('elevation'), station.get('region'),
                            station.get('country'), station.get('install_date'),
                            station.get('notes')
                        ))
                        
                    except Exception as e:
                        print(f"验证站点数据时出错: {e}, 数据: {station}")
                        error_count += 1
                        continue
                
                # 批量插入
                if batch_values:
                    try:
                        self.cursor.executemany('''
                        INSERT OR REPLACE INTO stations
                        (station_id, name, latitude, longitude, elevation, region, country, install_date, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', batch_values)
                        success_count += len(batch_values)
                        print(f"批量插入 {len(batch_values)} 条站点记录")
                    except sqlite3.Error as e:
                        print(f"批量插入站点数据时出错: {e}")
                        error_count += len(batch_values)
            
            # 提交事务
            self.conn.commit()
            print(f"批量导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
            
        except Exception as e:
            # 回滚事务
            self.conn.rollback()
            print(f"批量导入站点数据时发生严重错误: {e}")
            error_count += len(stations_data) - success_count
            
        return success_count, error_count

    def add_observations_batch(self, observations_data, batch_size=1000):
        """批量添加观测数据，提高大数据量导入性能
        
        Args:
            observations_data: 观测数据列表，每个元素是包含观测信息的字典
            batch_size: 批量处理大小，默认1000条
        
        Returns:
            tuple: (成功数量, 失败数量)
        """
        success_count = 0
        error_count = 0
        
        try:
            # 开始事务
            self.cursor.execute('BEGIN TRANSACTION')
            
            # 预先获取所有存在的站点ID，避免重复查询
            self.cursor.execute('SELECT station_id FROM stations')
            existing_stations = set(row[0] for row in self.cursor.fetchall())
            
            for i in range(0, len(observations_data), batch_size):
                batch = observations_data[i:i + batch_size]
                batch_values = []
                
                for obs in batch:
                    try:
                        # 验证必需参数
                        station_id = obs.get('station_id')
                        timestamp = obs.get('timestamp')
                        
                        if not station_id or not timestamp:
                            error_count += 1
                            continue

                        # 标准化时间格式
                        timestamp = self._normalize_timestamp(timestamp)
                        if not timestamp:
                            print(f"警告: 无法解析时间格式，跳过记录: {obs.get('timestamp')}")
                            error_count += 1
                            continue

                        # 检查站点是否存在
                        if station_id not in existing_stations:
                            error_count += 1
                            continue
                        
                        batch_values.append((
                            station_id, timestamp,
                            obs.get('pressure'), obs.get('max_station_pressure'), obs.get('min_station_pressure'),
                            obs.get('temperature'), obs.get('max_temperature'), obs.get('min_temperature'),
                            obs.get('avg_water_vapor_pressure'), obs.get('humidity'), obs.get('min_relative_humidity'),
                            obs.get('precipitation'), obs.get('precipitation_08_08'),
                            obs.get('wind_speed'), obs.get('avg_10min_wind_speed'), obs.get('max_wind_speed'),
                            obs.get('gust_wind_speed'), obs.get('wind_direction'),
                            obs.get('avg_ground_temperature'), obs.get('max_ground_temperature'),
                            obs.get('min_ground_temperature'), obs.get('sunshine_duration')
                        ))
                        
                    except Exception as e:
                        print(f"验证观测数据时出错: {e}")
                        error_count += 1
                        continue
                
                # 批量插入
                if batch_values:
                    try:
                        self.cursor.executemany('''
                        INSERT OR REPLACE INTO observations
                        (station_id, timestamp, pressure, max_station_pressure, min_station_pressure,
                         temperature, max_temperature, min_temperature, avg_water_vapor_pressure,
                         humidity, min_relative_humidity, precipitation, precipitation_08_08,
                         wind_speed, avg_10min_wind_speed, max_wind_speed, gust_wind_speed,
                         wind_direction, avg_ground_temperature, max_ground_temperature,
                         min_ground_temperature, sunshine_duration)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', batch_values)
                        success_count += len(batch_values)
                        print(f"批量插入 {len(batch_values)} 条观测记录")
                    except sqlite3.Error as e:
                        print(f"批量插入观测数据时出错: {e}")
                        error_count += len(batch_values)
            
            # 提交事务
            self.conn.commit()
            print(f"批量导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
            
        except Exception as e:
            # 回滚事务
            self.conn.rollback()
            print(f"批量导入观测数据时发生严重错误: {e}")
            error_count += len(observations_data) - success_count
            
        return success_count, error_count

    def import_stations_from_csv_batch(self, csv_file, progress_callback=None, batch_size=1000):
        """从CSV文件批量导入站点数据，优化大数据量导入性能
        
        Args:
            csv_file: CSV文件路径
            progress_callback: 进度回调函数，接收两个参数(current, total)
            batch_size: 批量处理大小，默认1000条
        """
        stations_data = []
        
        try:
            # 尝试多种编码方式读取文件
            encodings = ['utf-8-sig', 'gbk', 'gb2312', 'utf-8']
            success = False
            
            for encoding in encodings:
                try:
                    print(f"尝试使用 {encoding} 编码读取CSV文件...")
                    # 首先计算总行数，用于进度显示
                    total_rows = 0
                    with open(csv_file, 'r', encoding=encoding) as f:
                        total_rows = sum(1 for _ in f) - 1  # 减去标题行
                    
                    # 如果文件为空或只有标题，直接返回
                    if total_rows <= 0:
                        if progress_callback:
                            progress_callback(0, 0)
                        return False
                        
                    # 然后读取并处理数据
                    with open(csv_file, 'r', encoding=encoding) as f:
                        reader = csv.DictReader(f)
                        print(f"CSV文件列名: {reader.fieldnames}")
                        
                        for i, row in enumerate(reader):
                            # 更新进度（解析阶段）
                            if progress_callback:
                                progress_callback(i, total_rows * 2)  # 解析占50%，插入占50%
                                
                            # 检查区站号字段 (支持多种可能的列名)
                            station_id = None
                            for key in row.keys():
                                if '区站号' in key and row[key]:
                                    station_id = row[key]
                                    break
                                    
                            if not station_id:
                                continue
                            
                            # 查找站名
                            name = None
                            for key in row.keys():
                                if '站名' in key and row[key]:
                                    name = row[key]
                                    break
                            if not name:
                                name = station_id  # 站名默认使用站点ID
                                
                            # 查找纬度
                            latitude = None
                            for key in row.keys():
                                if '纬度' in key:
                                    latitude = self._parse_float(row[key])
                                    break
                            if latitude is None:
                                latitude = 0.0
                                
                            # 查找经度
                            longitude = None
                            for key in row.keys():
                                if '经度' in key:
                                    longitude = self._parse_float(row[key])
                                    break
                            if longitude is None:
                                longitude = 0.0
                                
                            # 查找海拔/测站高度
                            elevation = None
                            for key in row.keys():
                                if '测站高度' in key or '海拔' in key:
                                    elevation = self._parse_float(row[key])
                                    break
                                    
                            # 查找省份
                            province = None
                            for key in row.keys():
                                if '省名' in key or '省份' in key:
                                    province = row[key]
                                    break
                                    
                            # 查找地市
                            region = None
                            for key in row.keys():
                                if '地市' in key:
                                    region = row[key]
                                    break
                                
                            # 合并区县名和乡镇名作为notes
                            notes_parts = []
                            for key in row.keys():
                                if '区县' in key and row[key]:
                                    notes_parts.append(row[key])
                                    break
                            
                            for key in row.keys():
                                if '乡镇' in key and row[key]:
                                    notes_parts.append(row[key])
                                    break
                                    
                            notes = " ".join(notes_parts).strip() if notes_parts else None
                            
                            # 添加到批量数据列表
                            stations_data.append({
                                'station_id': station_id,
                                'name': name,
                                'latitude': latitude,
                                'longitude': longitude,
                                'elevation': elevation,
                                'region': region,
                                'country': province,
                                'install_date': None,
                                'notes': notes
                            })
                        
                    success = True
                    break  # 如果成功读取，跳出编码尝试循环
                    
                except UnicodeDecodeError:
                    print(f"尝试使用 {encoding} 编码读取失败，尝试下一种编码...")
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取时出错: {e}")
                    continue
                    
            if not success:
                print(f"错误: 无法以支持的编码格式读取CSV文件 '{csv_file}'")
                return False
            
            # 批量插入数据
            print(f"开始批量插入 {len(stations_data)} 条站点记录...")
            success_count, error_count = self.add_stations_batch(stations_data, batch_size)
            
            # 更新进度到100%
            if progress_callback:
                progress_callback(total_rows * 2, total_rows * 2)
                
            print(f"批量导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
            return success_count > 0
            
        except FileNotFoundError:
            print(f"错误: CSV文件 '{csv_file}' 未找到")
            return False
        except Exception as e:
            print(f"从 '{csv_file}' 批量导入站点数据时出错: {e}")
            return False

    def import_observations_from_csv_batch(self, csv_file, progress_callback=None, batch_size=1000):
        """从CSV文件批量导入观测数据，优化大数据量导入性能

        Args:
            csv_file: CSV文件路径
            progress_callback: 进度回调函数，接收两个参数(current, total)
            batch_size: 批量处理大小，默认1000条
        """
        observations_data = []

        try:
            # 尝试不同的编码方式打开文件
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'gb18030']
            used_encoding = None

            # 尝试使用不同的编码打开文件
            for encoding in encodings:
                try:
                    with open(csv_file, 'r', encoding=encoding) as f:
                        # 尝试读取几行来验证编码
                        for _ in range(5):
                            f.readline()
                        used_encoding = encoding
                        print(f"成功使用 {encoding} 编码打开文件")
                        break
                except (UnicodeDecodeError, UnicodeError):
                    continue
                except Exception as e:
                    print(f"尝试使用 {encoding} 编码时出错: {e}")
                    continue

            if not used_encoding:
                print(f"错误: 无法以任何支持的编码方式打开文件 '{csv_file}'")
                return False

            # 计算总行数用于进度显示
            total_rows = 0
            with open(csv_file, 'r', encoding=used_encoding) as f:
                total_rows = sum(1 for _ in f) - 1  # 减去标题行

            if total_rows <= 0:
                print(f"警告: 文件 '{csv_file}' 不包含数据行")
                if progress_callback:
                    progress_callback(0, 0)
                return False

            # 读取CSV文件
            with open(csv_file, 'r', encoding=used_encoding) as f:
                # 读取标题行
                header_line = f.readline().strip()
                column_names = [col.strip() for col in header_line.split(',')]

                print(f"检测到的列名: {column_names}")

                # 创建列名映射
                column_mapping = {}
                station_id_col = None
                date_col = None

                # 映射列名
                for i, col in enumerate(column_names):
                    # 站点ID列
                    if col in ["区站号", "区站号(字符)", "站号", "站点ID"]:
                        station_id_col = i
                        column_mapping[i] = "station_id"
                    # 日期/时间列
                    elif col in ["资料时间", "日期", "时间", "观测日期"]:
                        date_col = i
                        column_mapping[i] = "timestamp"
                    # 温度列
                    elif "气温" in col or "温度" in col:
                        if "最高" in col:
                            column_mapping[i] = "max_temperature"
                        elif "最低" in col:
                            column_mapping[i] = "min_temperature"
                        else:
                            column_mapping[i] = "temperature"
                    # 湿度列
                    elif "湿度" in col:
                        if "最小" in col:
                            column_mapping[i] = "min_relative_humidity"
                        else:
                            column_mapping[i] = "humidity"
                    # 降水量列
                    elif "降水量" in col or "雨量" in col:
                        if "20-20" in col:
                            column_mapping[i] = "precipitation"
                        elif "08-08" in col:
                            column_mapping[i] = "precipitation_08_08"
                        else:
                            column_mapping[i] = "precipitation"
                    # 风速列
                    elif "风速" in col:
                        if "最大" in col:
                            column_mapping[i] = "max_wind_speed"
                        elif "极大" in col:
                            column_mapping[i] = "gust_wind_speed"
                        else:
                            column_mapping[i] = "wind_speed"
                    # 站名列
                    elif col == "站名":
                        column_mapping[i] = "station_name"

                # 检查必要的列是否存在
                if station_id_col is None or date_col is None:
                    print(f"错误: 文件缺少必要的列 (区站号/站点ID 或 时间/日期)")
                    print(f"找到的列名: {column_names}")
                    return False

                # 处理每一行数据
                for i in range(total_rows):
                    line = f.readline().strip()
                    if not line:
                        continue

                    # 更新进度（解析阶段）
                    if progress_callback:
                        progress_callback(i, total_rows * 2)  # 解析占50%，插入占50%

                    # 解析CSV行
                    values = [val.strip() for val in line.split(',')]
                    if len(values) < len(column_names):
                        continue

                    # 获取站点ID和时间戳
                    station_id = values[station_id_col]
                    timestamp = values[date_col]

                    if not station_id or not timestamp:
                        continue

                    # 标准化时间格式
                    timestamp = self._normalize_timestamp(timestamp)
                    if not timestamp:
                        continue

                    # 创建观测数据字典
                    obs_data = {
                        'station_id': station_id,
                        'timestamp': timestamp
                    }

                    # 提取其他字段
                    for col_idx, field_name in column_mapping.items():
                        if col_idx < len(values) and field_name not in ['station_id', 'timestamp', 'station_name']:
                            value = values[col_idx]
                            # 跳过占位符值
                            if value not in ["999990.0", "999999", "999990", ""]:
                                obs_data[field_name] = self._parse_float(value)

                    # 添加到批量数据列表
                    observations_data.append(obs_data)

            # 批量插入数据
            print(f"开始批量插入 {len(observations_data)} 条观测记录...")
            success_count, error_count = self.add_observations_batch(observations_data, batch_size)

            # 更新进度到100%
            if progress_callback:
                progress_callback(total_rows * 2, total_rows * 2)

            print(f"批量导入完成: 成功 {success_count} 条, 失败 {error_count} 条")
            return success_count > 0

        except FileNotFoundError:
            print(f"错误: CSV文件 '{csv_file}' 未找到")
            return False
        except Exception as e:
            print(f"从 '{csv_file}' 批量导入观测数据时出错: {e}")
            traceback.print_exc()
            return False
