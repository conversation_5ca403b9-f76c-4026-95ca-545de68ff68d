from matplotlib import pyplot as plt
import numpy as np
import pandas as pd
from scipy.spatial import cKDTree
import geopandas as gpd
import cartopy.feature as cfeature
import cartopy.mpl.ticker as cticker
import cartopy.crs as ccrs
from matplotlib.path import Path
from cartopy.mpl.patch import geos_to_path

# 添加中文字体配置（放在所有绘图代码之前）
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文显示字体为黑体（SimHei）
plt.rcParams['axes.unicode_minus'] = False     # 正确显示负号


file_path = r'D:\python\data\lf国家站降水数据(1).xlsx'
data = pd.read_excel(file_path, sheet_name='Sheet1')

station_id = data['stationId']
lon = data['lon'].values
lat = data['lat'].values
precipitation = data['val'].values
'''
print("Station ID:", station_id)
print("Longitude:", lon)
print("Latitude:", lat)
print("Precipitation:", precipitation)
'''
# IDW 插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values) - 1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)

# 定义网格
x_min, x_max =110, 113
y_min, y_max = 35,  37
x_grid, y_grid = np.meshgrid(np.arange(x_min, x_max, 0.05), np.arange(y_min, y_max, 0.05))

# 进行插值
interpolated_precipitation = safe_idw_interpolation(np.column_stack((lon, lat)), precipitation, x_grid, y_grid,  power=2, k=10)
# 输出插值结果
#print("Interpolated Precipitation:")
print(interpolated_precipitation.shape)
print(interpolated_precipitation.min())

print((interpolated_precipitation[interpolated_precipitation < 0.1]).shape)


shp = gpd.read_file(r'D:\python\data\山西省\山西省_市.shp',encoding='gbk')
a = shp['geometry'][shp['市'] == '临汾市']

def contour_map(fig,img_extent):
    img_extent = [110,  113, 35,  37]
    fig.set_extent(img_extent, crs=ccrs.PlateCarree())
    fig.add_feature(cfeature.COASTLINE.with_scale('50m'),alpha=0.5)
    fig.add_feature(cfeature.LAKES,alpha=0.5)
    fig.set_xticks(np.arange(110, 113, 0.5),crs=ccrs.PlateCarree())
    fig.set_yticks(np.arange(35,37, 0.5),crs=ccrs.PlateCarree())
    lon_formatter = cticker.LongitudeFormatter()
    lat_formatter = cticker.LatitudeFormatter()
    fig.xaxis.set_major_formatter(lon_formatter)
    fig.yaxis.set_major_formatter(lat_formatter)

levels1 = ('0.1','10','25','50','100','250')
colors = ('#F6F6F6', '#A6F38D', '#3ABC3B', '#60B8FF', '#0100FC' , '#FB01FB' , '#81003C')


# ------------------绘图---------------------
fig = plt.figure(figsize=(15, 10))
proj = ccrs.PlateCarree()
ax = fig.add_axes([0.05, 0.3, 0.5, 0.5], projection=proj)
contour_map(ax,[110,  113, 35,  37])
contour = ax.contourf(x_grid, y_grid, interpolated_precipitation,zorder=0,levels=levels1,extend = 'both', transform=ccrs.PlateCarree(), colors=colors)

ax.scatter(lon, lat, c='red',  s=40, linewidth=0.5)

# 添加监测站点的标注
for i in range(len(lon)):
    #plt.annotate(data['stationId'][i], (lon[i],lat[i]), textcoords="offset points", xytext=(0, 10), ha='center', fontsize=8, color='black')
    plt.annotate(data['val'][i], (lon[i],lat[i]), textcoords="offset points", xytext=(0, 10), ha='center', fontsize=8, color='black')



# 生成裁剪路径
path_clip = Path.make_compound_path(*geos_to_path(a.to_list()))
# 将裁剪路径应用到图层
contour.set_clip_path(path_clip, transform=ax.transData)
# 绘制多边形边缘线
ax.add_geometries(a, crs=ccrs.PlateCarree(), facecolor='none', edgecolor='black')
plt.colorbar(contour, label=f'插值降水数据')
# 设置坐标轴标签
ax.set_xlabel('经度')
ax.set_ylabel('纬度')
# 设置坐标轴范围
'''ax.set_xlim(lon.min(), lon.max())
ax.set_ylim(lat.min(), lat.max())'''

plt.show()
