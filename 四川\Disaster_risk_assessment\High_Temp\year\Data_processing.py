import numpy as np
import pandas as pd


# 前处理
def read_data(file_path,station_ID):

    data = pd.read_excel(file_path)
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']
    data = data[data['站号'] == station_ID]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d')
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    # 按日期排序
    data.sort_index(inplace=True)

    # -------------------- 计算高温过程 --------------------

    # 定义高温过程的阈值、连续3天及以上最高气温≥35℃作为一个高温过程
    threshold = 35
    # 初始化高温过程标志
    high_temp_periods = np.zeros_like(data['最高气温'], dtype=bool)

    # 遍历每一行数据
    for i in range(len(data)):
        # 初始化连续高温天数
        consecutive_days = 0
        # 计算连续高温天数
        for j in range(i, len(data)):
            if data.iloc[j]['最高气温'] >= threshold:
                consecutive_days += 1
            else:
                break
        # 如果连续高温天数达到3天，标记为高温过程
        if consecutive_days >= 3:
            high_temp_periods[i:i+consecutive_days] = True
    # 将高温过程标志应用到数据中
    data['高温过程'] = high_temp_periods.astype(int)
    #print(data)

    # -------------------- 计算高温过程数 --------------------
    # 高温过程列连续值为1的段落数为高温过程数
    high_temp_periods_count = 0
    # 初始化连续高温天数
    consecutive_days = 0
    # 遍历每一行数据
    for i in range(len(data)):
        # 如果当前行的高温过程标志为1
        if data.iloc[i]['高温过程'] == 1:
            # 连续高温天数加1
            consecutive_days += 1
        else:
            # 如果连续高温天数达到3天，高温过程数加1
            if consecutive_days >= 3:
                high_temp_periods_count += 1
            # 重置连续高温天数
            consecutive_days = 0

    #print("高温过程数为：", high_temp_periods_count)

    # -------------------- 计算每个高温过程的持续日数 --------------------
    # 初始化持续日数列表
    year_high_temp_list = []
    # 初始化连续高温天数
    consecutive_days = 0
    # 遍历每一行数据
    for i in range(len(data)):
        # 如果当前行的高温过程标志为1
        if data.iloc[i]['高温过程'] == 1:
            # 连续高温天数加1
            consecutive_days += 1
        else:
            # 如果连续高温天数达到3天，将持续日数添加到列表中
            if consecutive_days >= 3:
                year_high_temp_list.append(consecutive_days)
            # 重置连续高温天数
            consecutive_days = 0

    #print("每个高温过程的持续日数为：", year_high_temp_list)

    # -------------------- 计算致灾因子 --------------------
    # 选择以下3个指标来计算：
    # a.年最高气温：统计年内日最高气温最大值，多年取最大；
    # b.累计高温日数：统计年内高温过程累计日数，多年取平均；
    # c.最长连续高温日数：统计年内最长高温过程日数，多年取最大。

    # 计算年最高气温
    year_high_temp = data['最高气温'].max()
    #print("年最高气温为：", year_high_temp)

    # 计算累计高温日数
    # 初始化累计高温日数
    cumulative_high_temp_days = 0
    # 遍历每一行数据
    for i in range(len(data)):
        # 如果当前行的高温过程标志为1
        if data.iloc[i]['高温过程'] == 1:
            # 累计高温日数加1
            cumulative_high_temp_days += 1

    #print("累计高温日数为：", cumulative_high_temp_days)


    # 计算最长连续高温日数
    longest_consecutive_high_temp_days = 0
    # 遍历每一行数据
    for i in range(len(data)):
        # 如果当前行的高温过程标志为1
        if data.iloc[i]['高温过程'] == 1:
            # 初始化连续高温天数
            consecutive_days = 0
            # 计算连续高温天数
            for j in range(i, len(data)):
                if data.iloc[j]['高温过程'] == 1:
                    consecutive_days += 1
                else:
                    break
            # 如果连续高温天数大于最长连续高温日数，更新最长连续高温日数
            if consecutive_days > longest_consecutive_high_temp_days:
                longest_consecutive_high_temp_days = consecutive_days
    #print("最长连续高温日数为：", longest_consecutive_high_temp_days)


    return high_temp_periods_count,year_high_temp_list,year_high_temp, cumulative_high_temp_days, longest_consecutive_high_temp_days

# 测试
file_path = r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\day.xlsx'
station_56272 = read_data(file_path, 56272)
station_56276 = read_data(file_path, 56276)
station_56284 = read_data(file_path, 56284)
station_56281 = read_data(file_path, 56281)
station_56181 = read_data(file_path, 56181)
station_56188 = read_data(file_path, 56188)
station_56189 = read_data(file_path, 56189)
#print(station_56272,'\n',station_56276,'\n',station_56284,'\n',station_56281,'\n',station_56181,'\n',station_56188,'\n',station_56189)


# -------------------- 归一化处理 --------------------
# 归一化公式：0.5+0.5*（value-min）/（max-min）
# 最大、最小分别为相同区域内相同因子的最大、最小值

# 归一化年最高气温
min_year_high_temp = min(station_56272[2], station_56276[2], station_56284[2], station_56281[2], station_56188[2], station_56181[2], station_56189[2])
max_year_high_temp = max(station_56272[2], station_56276[2], station_56284[2], station_56281[2], station_56188[2], station_56181[2], station_56189[2])
normalized_year_high_temp_56272 = 0.5 + 0.5 * (station_56272[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
normalized_year_high_temp_56276 = 0.5 + 0.5 * (station_56276[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
normalized_year_high_temp_56284 = 0.5 + 0.5 * (station_56284[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
normalized_year_high_temp_56281 = 0.5 + 0.5 * (station_56281[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
normalized_year_high_temp_56188 = 0.5 + 0.5 * (station_56188[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
normalized_year_high_temp_56181 = 0.5 + 0.5 * (station_56181[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
normalized_year_high_temp_56189 = 0.5 + 0.5 * (station_56189[2] - min_year_high_temp) / (max_year_high_temp - min_year_high_temp)
#print(normalized_year_high_temp_56272, normalized_year_high_temp_56276, normalized_year_high_temp_56284, normalized_year_high_temp_56281, normalized_year_high_temp_56188, normalized_year_high_temp_56181, normalized_year_high_temp_56189)

# 归一化累计高温日数
min_cumulative_high_temp_days = min(station_56272[3], station_56276[3], station_56284[3], station_56281[3], station_56188[3], station_56181[3], station_56189[3])
max_cumulative_high_temp_days = max(station_56272[3], station_56276[3], station_56284[3], station_56281[3], station_56188[3], station_56181[3], station_56189[3])
normalized_cumulative_high_temp_days_56272 = 0.5 + 0.5 * (station_56272[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
normalized_cumulative_high_temp_days_56276 = 0.5 + 0.5 * (station_56276[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
normalized_cumulative_high_temp_days_56284 = 0.5 + 0.5 * (station_56284[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
normalized_cumulative_high_temp_days_56281 = 0.5 + 0.5 * (station_56281[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
normalized_cumulative_high_temp_days_56188 = 0.5 + 0.5 * (station_56188[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
normalized_cumulative_high_temp_days_56181 = 0.5 + 0.5 * (station_56181[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
normalized_cumulative_high_temp_days_56189 = 0.5 + 0.5 * (station_56189[3] - min_cumulative_high_temp_days) / (max_cumulative_high_temp_days - min_cumulative_high_temp_days)
#print(normalized_cumulative_high_temp_days_56272, normalized_cumulative_high_temp_days_56276, normalized_cumulative_high_temp_days_56284, normalized_cumulative_high_temp_days_56281, normalized_cumulative_high_temp_days_56188, normalized_cumulative_high_temp_days_56181, normalized_cumulative_high_temp_days_56189)



# 归一化最长连续高温日数
min_longest_consecutive_high_temp_days = min(station_56272[4], station_56276[4], station_56284[4], station_56281[4], station_56188[4], station_56181[4], station_56189[4])
max_longest_consecutive_high_temp_days = max(station_56272[4], station_56276[4], station_56284[4], station_56281[4], station_56188[4], station_56181[4], station_56189[4])
normalized_longest_consecutive_high_temp_days_56272 = 0.5 + 0.5 * (station_56272[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
normalized_longest_consecutive_high_temp_days_56276 = 0.5 + 0.5 * (station_56276[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
normalized_longest_consecutive_high_temp_days_56284 = 0.5 + 0.5 * (station_56284[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
normalized_longest_consecutive_high_temp_days_56281 = 0.5 + 0.5 * (station_56281[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
normalized_longest_consecutive_high_temp_days_56188 = 0.5 + 0.5 * (station_56188[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
normalized_longest_consecutive_high_temp_days_56181 = 0.5 + 0.5 * (station_56181[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
normalized_longest_consecutive_high_temp_days_56189 = 0.5 + 0.5 * (station_56189[4] - min_longest_consecutive_high_temp_days) / (max_longest_consecutive_high_temp_days - min_longest_consecutive_high_temp_days)
#print(normalized_longest_consecutive_high_temp_days_56272, normalized_longest_consecutive_high_temp_days_56276, normalized_longest_consecutive_high_temp_days_56284, normalized_longest_consecutive_high_temp_days_56281, normalized_longest_consecutive_high_temp_days_56188, normalized_longest_consecutive_high_temp_days_56181, normalized_longest_consecutive_high_temp_days_56189)



# -------------------- 计算危险性 --------------------
# 计算高温致灾因子危险性指数 H = Σ(a_i * x_i)
# a_i 为权重，x_i 为归一化后的因子值

def calculate_disaster_risk(data, weight_method='equal'):
    # 计算权重
    if weight_method == 'equal':
        weights = [1/3] * 3  # 等权重
    elif weight_method == 'custom':
        weights = [0.2, 0.3, 0.1]  # 自定义权重
    else:
        raise ValueError("Invalid weight method. Choose 'equal', 'custom', or 'entropy'.")
    
    # 计算危险性指数
    disaster_risk = sum(weights[i] * data[i] for i in range(len(data)))
    return disaster_risk


factors_56272 = (normalized_year_high_temp_56272, normalized_cumulative_high_temp_days_56272, normalized_longest_consecutive_high_temp_days_56272)
factors_56276 = (normalized_year_high_temp_56276, normalized_cumulative_high_temp_days_56276, normalized_longest_consecutive_high_temp_days_56276)
factors_56284 = (normalized_year_high_temp_56284, normalized_cumulative_high_temp_days_56284, normalized_longest_consecutive_high_temp_days_56284)
factors_56281 = (normalized_year_high_temp_56281, normalized_cumulative_high_temp_days_56281, normalized_longest_consecutive_high_temp_days_56281)
factors_56188 = (normalized_year_high_temp_56188, normalized_cumulative_high_temp_days_56188, normalized_longest_consecutive_high_temp_days_56188)
factors_56181 = (normalized_year_high_temp_56181, normalized_cumulative_high_temp_days_56181, normalized_longest_consecutive_high_temp_days_56181)
factors_56189 = (normalized_year_high_temp_56189, normalized_cumulative_high_temp_days_56189, normalized_longest_consecutive_high_temp_days_56189)

#print("归一化后的因子值：", factors_56272, factors_56276, factors_56284, factors_56281, factors_56188, factors_56181, factors_56189)


result_56272 = calculate_disaster_risk(factors_56272, weight_method='equal')
result_56276 = calculate_disaster_risk(factors_56276, weight_method='equal')
result_56284 = calculate_disaster_risk(factors_56284, weight_method='equal')
result_56281 = calculate_disaster_risk(factors_56281, weight_method='equal')
result_56188 = calculate_disaster_risk(factors_56188, weight_method='equal')
result_56181 = calculate_disaster_risk(factors_56181, weight_method='equal')
result_56189 = calculate_disaster_risk(factors_56189, weight_method='equal')
#print("高温致灾因子危险性指数：", result_56272, result_56276, result_56284, result_56281, result_56188, result_56181, result_56189)


# -------------------- 危险性等级划分 --------------------
# 使用自然断点法进行危险性等级划分
def classify_risk_level(risk_score):
    import jenkspy
    # 使用自然断点分类法，分为5级
    breaks = jenkspy.jenks_breaks(risk_score, n_classes=5)
    # 根据断点划分等级
    risk_level = []
    for score in risk_score:
        if score <= breaks[1]:
            risk_level.append('1')
        elif  breaks[1] < score <= breaks[2]:
            risk_level.append('2')
        elif breaks[2] < score <= breaks[3]:
            risk_level.append('3')
        elif breaks[3] < score <= breaks[4]:
            risk_level.append('4')
        else:
            risk_level.append('5')
    return risk_level,breaks

risk_score = [result_56272, result_56276, result_56284, result_56281, result_56188, result_56181, result_56189]
risk_level,breaks = classify_risk_level(risk_score)
#print(breaks)
#print("危险性等级：", risk_level)


# -------------------- 整合结果 --------------------
# 将结果整合到一个DataFrame中
results = pd.DataFrame({
    '站号': [56272, 56276, 56284, 56281, 56188, 56181, 56189],
    '纬度': [30.8289, 30.4583, 30.4433, 30.2003, 30.9958, 30.6708, 30.9622],
    '经度': [103.8986, 103.8211, 103.4386, 103.4892, 103.6736, 103.6989, 103.9464],
    '高温过程数': [station_56272[0], station_56276[0], station_56284[0], station_56281[0], station_56188[0], station_56181[0], station_56189[0]],
    '高温过程持续日数': [station_56272[1], station_56276[1], station_56284[1], station_56281[1], station_56188[1], station_56181[1], station_56189[1]],
    '年最高气温': [station_56272[2], station_56276[2], station_56284[2], station_56281[2], station_56188[2], station_56181[2], station_56189[2]],
    '累计高温日数': [station_56272[3], station_56276[3], station_56284[3], station_56281[3], station_56188[3], station_56181[3], station_56189[3]],
    '最长连续高温日数': [station_56272[4], station_56276[4], station_56284[4], station_56281[4], station_56188[4], station_56181[4], station_56189[4]],
    '危险性指数': [result_56272, result_56276, result_56284, result_56281, result_56188, result_56181, result_56189]
})  

# 输出为Excel文件
results.to_excel(r'D:\python\Disaster_risk_assessment\High_Temp\year\data\result.xlsx', index=False)
