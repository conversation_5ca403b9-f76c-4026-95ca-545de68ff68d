# In[0] :

from matplotlib import pyplot as plt
import numpy as np
import pandas as pd
from scipy.spatial import cKDTree
import geopandas as gpd
import cartopy.feature as cfeature
import cartopy.mpl.ticker as cticker
import cartopy.crs as ccrs
from matplotlib.path import Path
from cartopy.mpl.patch import geos_to_path

plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文显示字体为黑体（SimHei）
plt.rcParams['axes.unicode_minus'] = False     # 正确显示负号

# 读取excel文件
file_path = r'D:\python\monitoring\data\station\SURF_CHN_MUL_HOR_N.xlsx'
data = pd.read_excel(file_path)

station_ids = data['区站号(字符)'].astype(str).unique()
#print(station_ids)

# 读取站点信息文件
file_path1 = r'D:\python\monitoring\data\station\station_info.xlsx'
station_info = pd.read_excel(file_path1)
station_info = station_info[station_info['station_id'].astype(str).isin(station_ids)]
station_ids2 = station_info['station_id'].astype(str).values

# 获取年月日列
year = data['年']
month = data['月']
day = data['日']
hour = data['时']


# 将年月日和小时组合成datetime格式
date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d') + pd.to_timedelta(hour, unit='h')
# 删除原始的年月日和小时列
data.drop(['年', '月', '日', '时'], axis=1, inplace=True)
# 添加新的日期列并设置为索引
data['date'] = date_time
data.set_index('date', inplace=True)

# 按日期排序
data.sort_index(inplace=True)

# IDW 插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values) - 1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)



# 不同时间尺度的不同要素计算与绘图函数
def precipitation_with_time_scale(station_ids, data, var_name, Statistical_period, Compare_period, cmap,
                                  file_path=None):
    # 初始化数据列表
    precipitation = []

    for station_id in station_ids:
        # 使用原始数据的副本进行筛选
        station_data = data[data['区站号(字符)'].astype(str) == station_id].copy()
        station_data.replace([999990,999999], np.nan, inplace=True)

        # 提取指定统计时段的数据
        Statistical_data = station_data[var_name]
        Statistical_data = Statistical_data[Statistical_period]
        Statistical_cumulative_var = Statistical_data.max()
        Statistical_cumulative_time = Statistical_data.idxmax()

        # 提取指定比较时段的数据
        Compare_data = station_data[var_name]
        Compare_data = Compare_data[Compare_period]
        Compare_cumulative_var = Compare_data.max()
        Compare_cumulative_time = Compare_data.idxmax()

        # 获取站点经纬度信息
        station_row = station_info[station_info['station_id'].astype(str) == station_id]
        lon = station_row['lon'].values[0]
        lat = station_row['lat'].values[0]

        # 将统计时段累计不同要素量存到数组中，包含站点 ID、经纬度和累计不同要素量
        precipitation.append({
            'station_id': station_id,
            'lon': lon,
            'lat': lat,
            'Statistical_cumulative_var': Statistical_cumulative_var,
            'Statistical_cumulative_time': Statistical_cumulative_time,
            'Compare_cumulative_var': Compare_cumulative_var,
            'Compare_cumulative_time': Compare_cumulative_time
        })

    # 将列表转换为 DataFrame
    cumulative_df = pd.DataFrame(precipitation)

    # -------------插值--------------------
    lon = cumulative_df['lon'].values
    lat = cumulative_df['lat'].values
    precipitation_values = cumulative_df['Statistical_cumulative_var'].values
    x = np.linspace(96, 109, 100)
    y = np.linspace(25.5, 35, 100)
    xx, yy = np.meshgrid(x, y)
    zz = safe_idw_interpolation(np.column_stack((lon, lat)), precipitation_values, xx, yy, power=2, k=10)

    leftlon,rightlon,lowerlat,upperlat = (96, 109, 25.5, 35)
    def contour_map(fig,img_extent):
        img_extent = [leftlon,rightlon,lowerlat,upperlat]
        fig.set_extent(img_extent, crs=ccrs.PlateCarree())
        fig.add_feature(cfeature.COASTLINE.with_scale('50m'),alpha=0.5)
        fig.add_feature(cfeature.LAKES,alpha=0.5)
        fig.set_xticks(np.arange(96, 109, 2.5),crs=ccrs.PlateCarree())
        fig.set_yticks(np.arange(25.5, 35, 1),crs=ccrs.PlateCarree())
        lon_formatter = cticker.LongitudeFormatter()
        lat_formatter = cticker.LatitudeFormatter()
        fig.xaxis.set_major_formatter(lon_formatter)
        fig.yaxis.set_major_formatter(lat_formatter)
    
    shp = gpd.read_file(r'D:\python\monitoring\data\shp\省界_region.shp',encoding='gbk')
    a = shp['geometry']

    # ------------------绘图---------------------
    fig = plt.figure(figsize=(15, 10))
    proj = ccrs.PlateCarree()
    ax = fig.add_axes([0.05, 0.3, 0.5, 0.5], projection=proj)
    contour_map(ax,[leftlon,rightlon,lowerlat,upperlat])
    contour = ax.contourf(xx, yy, zz, cmap=cmap)

    #绘制散点，统计时段最大值大于对比时段最大值的点
    mask = (cumulative_df['Statistical_cumulative_var'] >= cumulative_df['Compare_cumulative_var']) & (cumulative_df['Compare_cumulative_var'] != 0)
    lon = cumulative_df.loc[mask, 'lon']
    lat = cumulative_df.loc[mask, 'lat']
    precipitation_values = cumulative_df.loc[mask, 'Statistical_cumulative_var']

    ax.scatter(lon, lat, c='red',  s=40, linewidth=0.5)

    # 在图上输出破对比时段站数量
    ax.text(0.05, 0.1, f'统计时段：{Statistical_period[0]}-{Statistical_period[-1]}', transform=ax.transAxes, fontsize=12, verticalalignment='top')
    ax.text(0.05, 0.15, f'对比时段：{Compare_period[0]}-{Compare_period[-1]}', transform=ax.transAxes, fontsize=12, verticalalignment='top')
    ax.text(0.05, 0.95, f'破对比时段站数量：{len(lon)}', transform=ax.transAxes, fontsize=12, verticalalignment='top')


    # 生成裁剪路径
    path_clip = Path.make_compound_path(*geos_to_path(a.to_list()))
    # 将裁剪路径应用到图层
    contour.set_clip_path(path_clip, transform=ax.transData)
    # 绘制多边形边缘线
    ax.add_geometries(a, crs=ccrs.PlateCarree(), facecolor='none', edgecolor='black')
    ax.set_title(f'{var_name} {Statistical_period[0]}-{Statistical_period[-1]}')
    plt.colorbar(contour, label=f'{var_name}')
    # 设置坐标轴标签
    ax.set_xlabel('经度')
    ax.set_ylabel('纬度')
    # 设置坐标轴范围
    ax.set_xlim(leftlon, rightlon)
    ax.set_ylim(lowerlat, upperlat)

    if file_path:
        plt.savefig(rf'{file_path}', dpi=300, bbox_inches='tight')
    plt.show()

    return cumulative_df


# In[1] :降水

Statistical_period = pd.date_range(start='2024-05-01 16:00:00', end='2024-05-10 16:00:00', freq='h')


Compare_period = pd.date_range(start='2024-05-01 16:00:00', end='2024-05-30 16:00:00', freq='h')


cumulative_df = precipitation_with_time_scale(station_ids, data, '过去1小时降水量', Statistical_period, Compare_period, cmap='Blues',
                file_path=r'D:\python\monitoring\Extreme_value_statistics\photo\hour\precipitation_distribution.png'
                )
print(cumulative_df)

cumulative_df.to_excel(r'D:\python\monitoring\Extreme_value_statistics\data\hourly_precipitation_result.xlsx', index=False)



# %%
