import os
import struct
import datetime
import numpy as np
import xarray as xr


def parse_binary_file(file_path):
    data = []

    try:
        # 读取源文件并提取数据
        with open(file_path, 'rb') as f:
            f.seek(52)
            benchmark = struct.unpack('h', f.read(2))[0]
            scale_factor = struct.unpack('h', f.read(2))[0]

            f.seek(78)
            max_lat = struct.unpack('h', f.read(2))[0] / 100
            min_lon = struct.unpack('h', f.read(2))[0] / 100
            min_lat = struct.unpack('h', f.read(2))[0] / 100
            max_lon = struct.unpack('h', f.read(2))[0] / 100

            f.seek(92)
            lon_size = struct.unpack('h', f.read(2))[0]
            lat_size = struct.unpack('h', f.read(2))[0]

            f.seek(484)
            for _ in range(lon_size * lat_size):
                data.append(struct.unpack('h', f.read(2))[0])

        # 创建网格数据
        grid = np.reshape(data, [1, lon_size, lat_size])
        grid = (grid - benchmark) / scale_factor

        lon = np.linspace(min_lon, max_lon, lon_size)
        lat = np.linspace(max_lat, min_lat, lat_size)

        return grid, lon, lat

    except Exception as e:
        print(f"解析文件时出错: {e}")
        return None, None, None


def save_to_netcdf(grid, lon, lat, data_time, output_path):
    try:
        data_time = np.datetime64(datetime.datetime.strptime(data_time, '%Y-%m-%d'))
        data_attr = {
            "longname": 'SSI',
            "units": "MJ/㎡",
        }
        da = xr.DataArray(
            data=grid,
            dims=["time", "lat", "lon"],
            coords={
                "time": [data_time],
                "lat": lat,
                "lon": lon
            },
            attrs=data_attr
        )
        ds = da.to_dataset(name='SSI')
        ds.to_netcdf(output_path, engine='netcdf4')
        print(f"文件 {output_path} 创建成功。")
        ds.close()
    except Exception as e:
        print(f"保存为 NetCDF 文件时出错: {e}")


if __name__ == "__main__":
    file_path = r'D:\python\data\Z_SATE_C_BAWX_20250325200917_P_FY2G_SSI_VIS_OTG_20250325_TOAD.AWX'
    output_path = r'D:\python\data\output.nc'
    data_time = '2025-03-25'

    grid, lon, lat = parse_binary_file(file_path)
    if grid is not None and lon is not None and lat is not None:
        save_to_netcdf(grid, lon, lat, data_time, output_path)


    file_path = r"D:\python\data\output.nc"
    ds = xr.open_dataset(file_path)
    print(ds)
    print(ds["SSI"][0, 0, 0].values)

    