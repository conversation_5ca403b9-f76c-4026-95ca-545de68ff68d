#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据库管理系统 - 修复版本
提供气象站点和观测数据的存储、查询、导入导出功能
支持批量导入优化，提升大数据量导入性能
"""

import sqlite3
import csv
import os
import sys
import traceback
import pandas as pd

# 设置编码
try:
    if sys.stdout.encoding != 'utf-8':
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
except Exception as e:
    print(f"设置编码时出错: {e}")
    pass

from datetime import datetime

class WeatherDatabase:
    def __init__(self, db_name='weather_stations.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        self.cursor = self.conn.cursor()
        self.create_tables()

    def create_tables(self):
        """创建数据库表结构"""
        # 创建站点表
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS stations (
            station_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            latitude REAL NOT NULL,
            longitude REAL NOT NULL,
            elevation REAL,
            region TEXT,
            country TEXT,
            install_date TEXT,
            notes TEXT
        )
        ''')

        # 创建观测数据表
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS observations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            pressure REAL,
            max_station_pressure REAL,
            min_station_pressure REAL,
            temperature REAL,
            max_temperature REAL,
            min_temperature REAL,
            avg_water_vapor_pressure REAL,
            humidity REAL,
            min_relative_humidity REAL,
            precipitation REAL,
            precipitation_08_08 REAL,
            wind_speed REAL,
            avg_10min_wind_speed REAL,
            max_wind_speed REAL,
            gust_wind_speed REAL,
            wind_direction REAL,
            avg_ground_temperature REAL,
            max_ground_temperature REAL,
            min_ground_temperature REAL,
            sunshine_duration REAL,
            FOREIGN KEY (station_id) REFERENCES stations (station_id)
        )
        ''')

        self.conn.commit()

    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()

    def _parse_float(self, value):
        """安全地将字符串转换为浮点数"""
        if value is None or value == '' or str(value).strip() == '':
            return None
        try:
            # 处理可能的占位符值
            if str(value) in ['999990.0', '999999', '999990', '--', 'N/A']:
                return None
            return float(str(value).strip())
        except (ValueError, TypeError):
            return None

    def _validate_coordinates(self, latitude, longitude):
        """验证经纬度的有效性"""
        if latitude is None or longitude is None:
            raise ValueError("经纬度不能为空")
        if not (-90 <= latitude <= 90):
            raise ValueError(f"纬度必须在-90到90之间，当前值: {latitude}")
        if not (-180 <= longitude <= 180):
            raise ValueError(f"经度必须在-180到180之间，当前值: {longitude}")

    def add_station(self, station_id, name, latitude, longitude, elevation=None, region=None, country=None, install_date=None, notes=None):
        """添加或更新一个气象站点"""
        try:
            # 验证必需参数
            if not station_id or not name:
                raise ValueError("站点ID和名称不能为空")

            # 验证经纬度
            self._validate_coordinates(latitude, longitude)

            self.cursor.execute('''
            INSERT OR REPLACE INTO stations
            (station_id, name, latitude, longitude, elevation, region, country, install_date, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (station_id, name, latitude, longitude, elevation, region, country, install_date, notes))
            self.conn.commit()
            print(f"站点 '{name}' ({station_id}) 已成功添加/更新")
            return True
        except ValueError as e:
            print(f"数据验证错误: {e}")
            return False
        except sqlite3.Error as e:
            print(f"添加/更新站点 {station_id} 时数据库出错: {e}")
            return False
        except Exception as e:
            print(f"添加/更新站点 {station_id} 时发生意外错误: {e}")
            return False

    def _normalize_timestamp(self, timestamp):
        """标准化时间戳格式，统一为 YYYY-MM-DD 格式

        支持的输入格式:
        - 2023-11-30 00:00:00 -> 2023-11-30
        - 2023-11-30 -> 2023-11-30
        - 2023/11/30 -> 2023-11-30
        - 其他格式尝试自动解析
        """
        if not timestamp:
            return None

        timestamp_str = str(timestamp).strip()

        # 如果已经是 YYYY-MM-DD 格式，直接返回
        if len(timestamp_str) == 10 and timestamp_str.count('-') == 2:
            try:
                # 验证日期格式是否正确
                from datetime import datetime
                datetime.strptime(timestamp_str, '%Y-%m-%d')
                return timestamp_str
            except ValueError:
                pass

        # 处理包含时间的格式 (如: 2023-11-30 00:00:00)
        if ' ' in timestamp_str:
            date_part = timestamp_str.split(' ')[0]
            # 递归调用处理日期部分
            return self._normalize_timestamp(date_part)

        # 处理斜杠分隔的日期格式 (如: 2023/11/30)
        if '/' in timestamp_str:
            try:
                from datetime import datetime
                # 尝试解析 YYYY/MM/DD 格式
                dt = datetime.strptime(timestamp_str, '%Y/%m/%d')
                return dt.strftime('%Y-%m-%d')
            except ValueError:
                try:
                    # 尝试解析 MM/DD/YYYY 格式
                    dt = datetime.strptime(timestamp_str, '%m/%d/%Y')
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    pass

        # 尝试其他常见格式
        try:
            from datetime import datetime
            import pandas as pd

            # 使用pandas的智能日期解析
            dt = pd.to_datetime(timestamp_str)
            return dt.strftime('%Y-%m-%d')
        except:
            # 如果所有解析都失败，返回原始字符串
            print(f"警告: 无法解析时间格式 '{timestamp_str}'，保持原样")
            return timestamp_str

    def add_observation(self, station_id, timestamp, pressure=None, max_station_pressure=None, min_station_pressure=None,
                       temperature=None, max_temperature=None, min_temperature=None, avg_water_vapor_pressure=None,
                       humidity=None, min_relative_humidity=None, precipitation=None, precipitation_08_08=None,
                       wind_speed=None, avg_10min_wind_speed=None, max_wind_speed=None, gust_wind_speed=None,
                       wind_direction=None, avg_ground_temperature=None, max_ground_temperature=None,
                       min_ground_temperature=None, sunshine_duration=None):
        """添加观测数据"""
        try:
            # 验证必需参数
            if not station_id or not timestamp:
                raise ValueError("站点ID和时间戳不能为空")

            # 标准化时间格式
            timestamp = self._normalize_timestamp(timestamp)
            if not timestamp:
                raise ValueError("无法解析时间格式")

            # 检查站点是否存在
            self.cursor.execute('SELECT 1 FROM stations WHERE station_id = ?', (station_id,))
            if not self.cursor.fetchone():
                raise ValueError(f"站点ID '{station_id}' 不存在")

            self.cursor.execute('''
            INSERT OR REPLACE INTO observations
            (station_id, timestamp, pressure, max_station_pressure, min_station_pressure,
             temperature, max_temperature, min_temperature, avg_water_vapor_pressure,
             humidity, min_relative_humidity, precipitation, precipitation_08_08,
             wind_speed, avg_10min_wind_speed, max_wind_speed, gust_wind_speed,
             wind_direction, avg_ground_temperature, max_ground_temperature,
             min_ground_temperature, sunshine_duration)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (station_id, timestamp, pressure, max_station_pressure, min_station_pressure,
                  temperature, max_temperature, min_temperature, avg_water_vapor_pressure,
                  humidity, min_relative_humidity, precipitation, precipitation_08_08,
                  wind_speed, avg_10min_wind_speed, max_wind_speed, gust_wind_speed,
                  wind_direction, avg_ground_temperature, max_ground_temperature,
                  min_ground_temperature, sunshine_duration))
            self.conn.commit()
            return True
        except ValueError as e:
            print(f"数据验证错误: {e}")
            return False
        except sqlite3.Error as e:
            print(f"添加观测数据时数据库出错: {e}")
            return False
        except Exception as e:
            print(f"添加观测数据时发生意外错误: {e}")
            return False

    def get_station_info(self, station_id):
        """获取站点信息"""
        try:
            self.cursor.execute('SELECT * FROM stations WHERE station_id = ?', (station_id,))
            row = self.cursor.fetchone()
            if row:
                columns = [desc[0] for desc in self.cursor.description]
                return dict(zip(columns, row))
            return None
        except sqlite3.Error as e:
            print(f"查询站点信息时出错: {e}")
            return None

    def get_station_by_id(self, station_id):
        """Web应用使用的方法，获取指定站点信息"""
        return self.get_station_info(station_id)

    def get_observations(self, station_id, limit=1000):
        """获取指定站点的观测数据"""
        try:
            query = '''
            SELECT * FROM observations 
            WHERE station_id = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            '''
            self.cursor.execute(query, (station_id, limit))
            rows = self.cursor.fetchall()
            
            columns = [desc[0] for desc in self.cursor.description]
            return [dict(zip(columns, row)) for row in rows]
        except sqlite3.Error as e:
            print(f"查询观测数据时出错: {e}")
            return []

    def get_observations_by_station_id(self, station_id, limit=1000):
        """Web应用使用的方法，获取指定站点的所有观测数据"""
        return self.get_observations(station_id, limit=limit)

    def get_observations_paginated(self, station_id, limit=50, offset=0, search=''):
        """分页获取观测数据（用于弹窗显示）"""
        try:
            cursor = self.conn.cursor()

            # 构建查询条件
            where_clause = "WHERE station_id = ?"
            params = [station_id]

            if search:
                where_clause += " AND (timestamp LIKE ? OR CAST(temperature AS TEXT) LIKE ? OR CAST(precipitation AS TEXT) LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            # 查询数据
            sql = f'''
                SELECT id, station_id, timestamp, pressure, max_station_pressure, min_station_pressure,
                       temperature, max_temperature, min_temperature, avg_water_vapor_pressure,
                       humidity, min_relative_humidity, precipitation, precipitation_08_08,
                       wind_speed, avg_10min_wind_speed, max_wind_speed, gust_wind_speed,
                       wind_direction, avg_ground_temperature, max_ground_temperature,
                       min_ground_temperature, sunshine_duration
                FROM observations
                {where_clause}
                ORDER BY timestamp DESC
                LIMIT ? OFFSET ?
            '''

            params.extend([limit, offset])
            cursor.execute(sql, params)
            rows = cursor.fetchall()

            # 转换为字典列表，并处理异常值
            observations = []
            for row in rows:
                obs = {
                    'id': row[0],
                    'station_id': row[1],
                    'timestamp': row[2],
                    'pressure': self._handle_abnormal_value(row[3]),
                    'max_station_pressure': self._handle_abnormal_value(row[4]),
                    'min_station_pressure': self._handle_abnormal_value(row[5]),
                    'temperature': self._handle_abnormal_value(row[6]),
                    'max_temperature': self._handle_abnormal_value(row[7]),
                    'min_temperature': self._handle_abnormal_value(row[8]),
                    'avg_water_vapor_pressure': self._handle_abnormal_value(row[9]),
                    'humidity': self._handle_abnormal_value(row[10]),
                    'min_relative_humidity': self._handle_abnormal_value(row[11]),
                    'precipitation': self._handle_abnormal_value(row[12]),
                    'precipitation_08_08': self._handle_abnormal_value(row[13]),
                    'wind_speed': self._handle_abnormal_value(row[14]),
                    'avg_10min_wind_speed': self._handle_abnormal_value(row[15]),
                    'max_wind_speed': self._handle_abnormal_value(row[16]),
                    'gust_wind_speed': self._handle_abnormal_value(row[17]),
                    'wind_direction': row[18],
                    'avg_ground_temperature': self._handle_abnormal_value(row[19]),
                    'max_ground_temperature': self._handle_abnormal_value(row[20]),
                    'min_ground_temperature': self._handle_abnormal_value(row[21]),
                    'sunshine_duration': self._handle_abnormal_value(row[22])
                }
                observations.append(obs)

            return observations

        except Exception as e:
            print(f"分页获取观测数据失败: {e}")
            return []

    def get_observations_count(self, station_id, search=''):
        """获取观测数据总数（用于分页）"""
        try:
            cursor = self.conn.cursor()

            # 构建查询条件
            where_clause = "WHERE station_id = ?"
            params = [station_id]

            if search:
                where_clause += " AND (timestamp LIKE ? OR CAST(temperature AS TEXT) LIKE ? OR CAST(precipitation AS TEXT) LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            sql = f"SELECT COUNT(*) FROM observations {where_clause}"
            cursor.execute(sql, params)

            return cursor.fetchone()[0]

        except Exception as e:
            print(f"获取观测数据总数失败: {e}")
            return 0

    def _handle_abnormal_value(self, value):
        """处理异常值，将999999, 999998, 999990转换为0"""
        if value is None:
            return None
        if value in [999999, 999998, 999990, 999999.0, 999998.0, 999990.0]:
            return 0
        return value

    def get_observation_statistics(self, station_id):
        """获取站点观测数据统计信息"""
        try:
            cursor = self.conn.cursor()

            # 基本统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_records,
                    MIN(timestamp) as earliest_date,
                    MAX(timestamp) as latest_date,
                    COUNT(DISTINCT substr(timestamp, 1, 4)) as years_count,
                    COUNT(DISTINCT substr(timestamp, 1, 7)) as months_count
                FROM observations
                WHERE station_id = ?
            ''', (station_id,))

            basic_stats = cursor.fetchone()



            # 月度统计（最近12个月，处理异常值）
            cursor.execute('''
                SELECT
                    substr(timestamp, 1, 7) as month,
                    COUNT(*) as records_count,
                    AVG(CASE
                        WHEN temperature IS NOT NULL AND temperature NOT IN (999999, 999998, 999990)
                        THEN temperature
                        WHEN temperature IN (999999, 999998, 999990)
                        THEN 0
                        END) as avg_temp,
                    SUM(CASE
                        WHEN precipitation IS NOT NULL AND precipitation NOT IN (999999, 999998, 999990)
                        THEN precipitation
                        WHEN precipitation IN (999999, 999998, 999990)
                        THEN 0
                        ELSE 0 END) as total_precip
                FROM observations
                WHERE station_id = ?
                GROUP BY substr(timestamp, 1, 7)
                ORDER BY month DESC
                LIMIT 12
            ''', (station_id,))

            monthly_stats = cursor.fetchall()

            # 数据完整性统计（排除异常值）
            cursor.execute('''
                SELECT
                    COUNT(CASE WHEN temperature IS NOT NULL AND temperature NOT IN (999999, 999998, 999990) THEN 1 END) * 100.0 / COUNT(*) as temp_completeness,
                    COUNT(CASE WHEN precipitation IS NOT NULL AND precipitation NOT IN (999999, 999998, 999990) THEN 1 END) * 100.0 / COUNT(*) as precip_completeness,
                    COUNT(CASE WHEN humidity IS NOT NULL AND humidity NOT IN (999999, 999998, 999990) THEN 1 END) * 100.0 / COUNT(*) as humidity_completeness,
                    COUNT(CASE WHEN wind_speed IS NOT NULL AND wind_speed NOT IN (999999, 999998, 999990) THEN 1 END) * 100.0 / COUNT(*) as wind_completeness,
                    COUNT(CASE WHEN pressure IS NOT NULL AND pressure NOT IN (999999, 999998, 999990) THEN 1 END) * 100.0 / COUNT(*) as pressure_completeness
                FROM observations
                WHERE station_id = ?
            ''', (station_id,))

            completeness_stats = cursor.fetchone()

            # 最近记录
            cursor.execute('''
                SELECT timestamp, temperature, precipitation, humidity, wind_speed, pressure
                FROM observations
                WHERE station_id = ?
                ORDER BY timestamp DESC
                LIMIT 10
            ''', (station_id,))

            recent_records = cursor.fetchall()

            # 组装结果
            result = {
                'basic': {
                    'total_records': basic_stats[0] if basic_stats[0] else 0,
                    'earliest_date': basic_stats[1],
                    'latest_date': basic_stats[2],
                    'years_count': basic_stats[3] if basic_stats[3] else 0,
                    'months_count': basic_stats[4] if basic_stats[4] else 0
                },

                'monthly': [
                    {
                        'month': row[0],
                        'records_count': row[1],
                        'avg_temp': round(row[2], 1) if row[2] else None,
                        'total_precip': round(row[3], 1) if row[3] else None
                    } for row in monthly_stats
                ],
                'completeness': {
                    'temperature': round(completeness_stats[0], 1) if completeness_stats[0] else 0,
                    'precipitation': round(completeness_stats[1], 1) if completeness_stats[1] else 0,
                    'humidity': round(completeness_stats[2], 1) if completeness_stats[2] else 0,
                    'wind_speed': round(completeness_stats[3], 1) if completeness_stats[3] else 0,
                    'pressure': round(completeness_stats[4], 1) if completeness_stats[4] else 0
                } if completeness_stats else {},
                'recent_records': [
                    {
                        'timestamp': row[0],
                        'temperature': row[1],
                        'precipitation': row[2],
                        'humidity': row[3],
                        'wind_speed': row[4],
                        'pressure': row[5]
                    } for row in recent_records
                ]
            }

            return result

        except Exception as e:
            print(f"获取观测数据统计失败: {e}")
            return {
                'basic': {'total_records': 0, 'earliest_date': None, 'latest_date': None, 'years_count': 0, 'months_count': 0},
                'monthly': [],
                'completeness': {},
                'recent_records': []
            }

    def get_observations_by_station_id_and_date_range(self, station_id, start_date, end_date):
        """
        根据站点ID和日期范围获取观测数据

        Args:
            station_id: 站点ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            list: 观测数据列表
        """
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT id, station_id, timestamp, temperature, precipitation,
                       humidity, wind_speed, pressure, max_temperature,
                       min_temperature, wind_direction
                FROM observations
                WHERE station_id = ? AND timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp
            ''', (station_id, start_date, end_date))

            rows = cursor.fetchall()
            observations = []

            for row in rows:
                observations.append({
                    'id': row[0],
                    'station_id': row[1],
                    'timestamp': row[2],
                    'temperature': row[3],
                    'precipitation': row[4],
                    'humidity': row[5],
                    'wind_speed': row[6],
                    'pressure': row[7],
                    'max_temperature': row[8],
                    'min_temperature': row[9],
                    'wind_direction': row[10]
                })

            return observations

        except Exception as e:
            print(f"根据日期范围获取观测数据时出错: {e}")
            return []

    def get_all_stations(self):
        """获取所有站点信息"""
        try:
            query = """
            SELECT s.*, COUNT(o.id) as observation_count 
            FROM stations s
            LEFT JOIN observations o ON s.station_id = o.station_id
            GROUP BY s.station_id
            ORDER BY s.station_id
            """
            
            self.cursor.execute(query)
            columns = [desc[0] for desc in self.cursor.description]
            rows = self.cursor.fetchall()
            return [dict(zip(columns, row)) for row in rows]
        except sqlite3.Error as e:
            print(f"查询所有站点时出错: {e}")
            return []

    def get_all_provinces(self):
        """获取所有省份列表"""
        try:
            query = """
            SELECT DISTINCT country FROM stations WHERE country IS NOT NULL ORDER BY country
            """
            
            self.cursor.execute(query)
            rows = self.cursor.fetchall()
            return [row[0] for row in rows if row[0]]  # 过滤掉空值
        except sqlite3.Error as e:
            print(f"查询省份列表时出错: {e}")
            return []

    def import_stations_from_csv(self, csv_file, progress_callback=None):
        """从CSV文件导入站点数据，使用批量导入优化性能"""
        # 导入批量优化模块
        from weather_database_batch_optimized import WeatherDatabaseBatchOptimized

        # 创建批量导入实例，使用相同的数据库
        batch_importer = WeatherDatabaseBatchOptimized(self.db_name)
        try:
            result = batch_importer.import_stations_from_csv_batch(csv_file, progress_callback)
            return result
        finally:
            batch_importer.close()

    def import_observations_from_csv(self, csv_file, progress_callback=None):
        """从CSV文件导入观测数据，使用批量导入优化性能"""
        # 导入批量优化模块
        from weather_database_batch_optimized import WeatherDatabaseBatchOptimized

        # 创建批量导入实例，使用相同的数据库
        batch_importer = WeatherDatabaseBatchOptimized(self.db_name)
        try:
            result = batch_importer.import_observations_from_csv_batch(csv_file, progress_callback)
            return result
        finally:
            batch_importer.close()

    def import_combined_data_from_csv(self, csv_file, progress_callback=None):
        """从CSV文件导入组合数据（站点和观测数据）"""
        try:
            # 先尝试导入站点数据
            stations_result = self.import_stations_from_csv(csv_file, progress_callback)

            # 再尝试导入观测数据
            observations_result = self.import_observations_from_csv(csv_file, progress_callback)

            # 只要有一个成功就算成功
            return stations_result or observations_result
        except Exception as e:
            print(f"从 '{csv_file}' 导入组合数据时出错: {e}")
            return False

    def import_stations_from_excel(self, excel_file, progress_callback=None):
        """从Excel文件导入站点数据"""
        imported_count = 0
        try:
            # 检查文件是否存在
            if not os.path.exists(excel_file):
                print(f"错误: Excel文件 '{excel_file}' 不存在")
                return False

            # 使用pandas读取Excel文件
            print(f"正在读取Excel文件: {excel_file}")

            # 尝试不同的读取方式
            try:
                # 先尝试使用openpyxl引擎
                df = pd.read_excel(excel_file, engine='openpyxl')
                print("使用openpyxl引擎成功读取Excel")
            except Exception as e1:
                print(f"openpyxl读取失败: {e1}")
                try:
                    # 尝试使用xlrd引擎
                    df = pd.read_excel(excel_file, engine='xlrd')
                    print("使用xlrd引擎成功读取Excel")
                except Exception as e2:
                    print(f"xlrd读取失败: {e2}")
                    # 最后尝试默认引擎
                    df = pd.read_excel(excel_file)
                    print("使用默认引擎成功读取Excel")

            # 检查数据帧是否为空
            if df.empty:
                print("错误: Excel文件不包含数据")
                return False

            # 计算总行数，用于进度显示
            total_rows = len(df)

            # 如果文件为空，直接返回
            if total_rows <= 0:
                if progress_callback:
                    progress_callback(0, 0)
                return False

            # 处理数据
            for i, row in df.iterrows():
                # 更新进度
                if progress_callback:
                    progress_callback(i, total_rows)

                # 将Series转换为字典，以便与CSV处理逻辑保持一致
                row_dict = row.to_dict()

                # 检查区站号字段 (支持多种可能的列名)
                station_id = None
                for key in row_dict.keys():
                    if isinstance(key, str) and '区站号' in key and pd.notna(row_dict[key]):
                        station_id = str(row_dict[key])
                        break

                if not station_id:
                    print(f"跳过行，缺少区站号: {row_dict}")
                    continue

                # 查找站名
                name = None
                for key in row_dict.keys():
                    if isinstance(key, str) and '站名' in key and pd.notna(row_dict[key]):
                        name = str(row_dict[key])
                        break
                if not name:
                    name = station_id  # 站名默认使用站点ID

                # 查找纬度
                latitude = None
                for key in row_dict.keys():
                    if isinstance(key, str) and '纬度' in key and pd.notna(row_dict[key]):
                        latitude = self._parse_float(str(row_dict[key]))
                        break
                if latitude is None:
                    latitude = 0.0

                # 查找经度
                longitude = None
                for key in row_dict.keys():
                    if isinstance(key, str) and '经度' in key and pd.notna(row_dict[key]):
                        longitude = self._parse_float(str(row_dict[key]))
                        break
                if longitude is None:
                    longitude = 0.0

                # 查找海拔/测站高度
                elevation = None
                for key in row_dict.keys():
                    if isinstance(key, str) and ('测站高度' in key or '海拔' in key) and pd.notna(row_dict[key]):
                        elevation = self._parse_float(str(row_dict[key]))
                        break

                # 查找省份
                province = None
                for key in row_dict.keys():
                    if isinstance(key, str) and ('省名' in key or '省份' in key) and pd.notna(row_dict[key]):
                        province = str(row_dict[key])
                        break

                # 查找地市
                region = None
                for key in row_dict.keys():
                    if isinstance(key, str) and '地市' in key and pd.notna(row_dict[key]):
                        region = str(row_dict[key])
                        break

                # 合并区县名和乡镇名作为notes
                notes_parts = []
                for key in row_dict.keys():
                    if isinstance(key, str) and '区县' in key and pd.notna(row_dict[key]):
                        notes_parts.append(str(row_dict[key]))
                        break

                for key in row_dict.keys():
                    if isinstance(key, str) and '乡镇' in key and pd.notna(row_dict[key]):
                        notes_parts.append(str(row_dict[key]))
                        break

                notes = " ".join(notes_parts).strip() if notes_parts else None

                # 打印调试信息
                print(f"正在导入站点: ID={station_id}, 名称={name}, 省份={province}")

                if self.add_station(
                    station_id=station_id,
                    name=name,
                    latitude=latitude,
                    longitude=longitude,
                    elevation=elevation,
                    region=region,
                    country=province,
                    install_date=None,  # install_date is not in Excel
                    notes=notes
                ):
                    imported_count += 1

            # 完成导入，设置最终进度
            if progress_callback:
                progress_callback(total_rows, total_rows)  # 确保进度显示100%

            print(f"成功从 '{excel_file}' 导入 {imported_count} 条站点记录")
            return True

        except FileNotFoundError:
            print(f"错误: Excel文件 '{excel_file}' 未找到")
            return False
        except Exception as e:
            print(f"从 '{excel_file}' 导入站点数据时出错: {e}")
            return False

    def import_observations_from_excel(self, excel_file, progress_callback=None):
        """从Excel文件导入观测数据"""
        imported_count = 0
        try:
            # 检查文件是否存在
            if not os.path.exists(excel_file):
                print(f"错误: Excel文件 '{excel_file}' 不存在")
                return False

            # 使用pandas读取Excel文件
            print(f"正在读取Excel文件: {excel_file}")

            # 尝试不同的读取方式
            try:
                # 先尝试使用openpyxl引擎
                df = pd.read_excel(excel_file, engine='openpyxl')
                print("使用openpyxl引擎成功读取Excel")
            except Exception as e1:
                print(f"openpyxl读取失败: {e1}")
                try:
                    # 尝试使用xlrd引擎
                    df = pd.read_excel(excel_file, engine='xlrd')
                    print("使用xlrd引擎成功读取Excel")
                except Exception as e2:
                    print(f"xlrd读取失败: {e2}")
                    # 最后尝试默认引擎
                    df = pd.read_excel(excel_file)
                    print("使用默认引擎成功读取Excel")

            # 检查数据帧是否为空
            if df.empty:
                print("错误: Excel文件不包含数据")
                return False

            # 计算总行数，用于进度显示
            total_rows = len(df)

            # 如果文件为空，直接返回
            if total_rows <= 0:
                if progress_callback:
                    progress_callback(0, 0)
                return False

            # 处理数据
            for i, row in df.iterrows():
                # 更新进度
                if progress_callback:
                    progress_callback(i, total_rows)

                # 将Series转换为字典，以便与CSV处理逻辑保持一致
                row_dict = row.to_dict()

                # 检查区站号字段 (支持多种可能的列名)
                station_id = None
                for key in row_dict.keys():
                    if isinstance(key, str) and ('区站号' in key) and pd.notna(row_dict[key]):
                        station_id = str(row_dict[key]).strip()
                        break

                if not station_id:
                    print(f"跳过行，缺少区站号: {row_dict}")
                    continue

                # 查找时间戳
                timestamp = None
                for key in row_dict.keys():
                    if isinstance(key, str) and ('时间' in key or '日期' in key) and pd.notna(row_dict[key]):
                        # 如果是pandas的Timestamp对象，转换为字符串
                        if isinstance(row_dict[key], pd.Timestamp):
                            timestamp = row_dict[key].strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            timestamp = str(row_dict[key])
                        break

                if not timestamp:
                    print(f"跳过行，缺少时间戳: {row_dict}")
                    continue

                # 标准化时间格式
                timestamp = self._normalize_timestamp(timestamp)
                if not timestamp:
                    print(f"跳过行，无法解析时间格式: {row_dict}")
                    continue

                # 初始化观测数据变量
                temperature = None
                max_temperature = None
                min_temperature = None
                humidity = None
                min_humidity = None
                pressure = None
                precipitation = None
                precipitation_08_08 = None
                wind_speed = None
                max_wind_speed = None
                gust_wind_speed = None
                wind_direction = None

                # 查找各种观测字段
                for key in row_dict.keys():
                    if not isinstance(key, str):
                        continue

                    value = row_dict[key]
                    if pd.isna(value) or value == 999990.0 or str(value).strip() == '':
                        continue

                    # 处理不同类型的温度字段
                    if '平均气温' in key or '平均温度' in key:
                        temperature = self._parse_float(str(value))
                    elif '最高气温' in key or '最高温度' in key:
                        max_temperature = self._parse_float(str(value))
                    elif '最低气温' in key or '最低温度' in key:
                        min_temperature = self._parse_float(str(value))
                    # 湿度字段
                    elif '相对湿度' in key and '最小' not in key:
                        humidity = self._parse_float(str(value))
                    elif '最小相对湿度' in key:
                        min_humidity = self._parse_float(str(value))
                    # 降水字段
                    elif '降水量' in key and '20-20' in key:
                        precipitation = self._parse_float(str(value))
                    elif '降水量' in key and '08-08' in key:
                        precipitation_08_08 = self._parse_float(str(value))
                    elif '降水' in key or '雨量' in key:
                        if precipitation is None:  # 只在没有更精确字段时使用
                            precipitation = self._parse_float(str(value))
                    # 风速字段
                    elif '平均风速' in key or ('风速' in key and '最' not in key and '极' not in key):
                        wind_speed = self._parse_float(str(value))
                    elif '最大风速' in key:
                        max_wind_speed = self._parse_float(str(value))
                    elif '极大风速' in key or '阵风' in key:
                        gust_wind_speed = self._parse_float(str(value))
                    # 风向字段
                    elif '风向' in key:
                        wind_direction = self._parse_float(str(value))
                    # 气压字段
                    elif '气压' in key:
                        pressure = self._parse_float(str(value))

                # 添加观测记录
                if self.add_observation(
                    station_id=station_id,
                    timestamp=timestamp,
                    temperature=temperature,
                    max_temperature=max_temperature,
                    min_temperature=min_temperature,
                    humidity=humidity,
                    min_relative_humidity=min_humidity,
                    precipitation=precipitation,
                    precipitation_08_08=precipitation_08_08,
                    wind_speed=wind_speed,
                    max_wind_speed=max_wind_speed,
                    gust_wind_speed=gust_wind_speed,
                    wind_direction=wind_direction,
                    pressure=pressure
                ):
                    imported_count += 1

            # 完成导入，设置最终进度
            if progress_callback:
                progress_callback(total_rows, total_rows)  # 确保进度显示100%

            print(f"成功从 '{excel_file}' 导入 {imported_count} 条观测记录")
            return imported_count > 0  # 只有成功导入数据时才返回True

        except FileNotFoundError:
            print(f"错误: Excel文件 '{excel_file}' 未找到")
            return False
        except Exception as e:
            print(f"从 '{excel_file}' 导入观测数据时出错: {e}")
            traceback.print_exc()  # 打印详细的异常堆栈信息
            return False

    def import_combined_data_from_excel(self, excel_file, progress_callback=None):
        """从Excel文件导入组合数据（站点和观测数据）"""
        try:
            # 首先检查文件是否存在
            if not os.path.exists(excel_file):
                print(f"错误: Excel文件 '{excel_file}' 不存在")
                return False

            # 尝试读取Excel文件以验证是否可访问
            try:
                pd.read_excel(excel_file, nrows=1, engine='openpyxl')
                print(f"成功测试读取Excel文件: {excel_file}")
            except Exception as e:
                print(f"测试读取Excel文件失败: {e}")
                return False

            # 首先导入站点数据
            print("开始导入站点数据...")
            stations_result = self.import_stations_from_excel(excel_file, progress_callback)

            # 然后导入观测数据
            print("开始导入观测数据...")
            observations_result = self.import_observations_from_excel(excel_file, progress_callback)

            return stations_result and observations_result
        except Exception as e:
            print(f"从 '{excel_file}' 导入组合数据时发生严重错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def delete_observation(self, observation_id):
        """删除指定的观测记录"""
        try:
            self.cursor.execute('DELETE FROM observations WHERE id = ?', (observation_id,))
            self.conn.commit()
            return self.cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"删除观测记录时出错: {e}")
            return False

    def delete_observations_batch(self, observation_ids):
        """批量删除观测记录"""
        try:
            if not observation_ids:
                return False

            # 构建SQL语句
            placeholders = ','.join(['?' for _ in observation_ids])
            sql = f'DELETE FROM observations WHERE id IN ({placeholders})'

            self.cursor.execute(sql, observation_ids)
            self.conn.commit()
            return self.cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"批量删除观测记录时出错: {e}")
            return False


if __name__ == "__main__":
    # 示例用法
    db = WeatherDatabase(db_name='test_weather_data.db')
    print("--- WeatherDatabase 测试运行 ---")

    # 可以在这里添加测试调用，例如：
    # db.import_combined_data_from_csv('path_to_your_example_data.csv')
    # db.import_stations_from_excel('path_to_your_example_data.xlsx')
    # station_info = db.get_station_info('some_station_id')
    # if station_info: print(f"测试站点信息: {station_info}")
    # observations = db.get_observations('some_station_id', limit=5)
    # if observations: print(f"测试观测数据: {observations}")

    db.close()
    print("--- 测试运行完成 ---")
