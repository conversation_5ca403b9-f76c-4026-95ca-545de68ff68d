# 导入OpenAI库的客户端模块
from openai import OpenAI
import tkinter as tk
from tkinter import scrolledtext, END

# 创建API客户端实例，配置DeepSeek的访问参数
client = OpenAI(api_key="***********************************", base_url="https://api.deepseek.com")

# 初始化对话历史
messages = [
    {"role": "system", "content": "You are a helpful assistant"}
]

print("欢迎使用AI聊天助手！输入消息开始对话（输入 exit 退出）")

while True:
    try:
        # 获取用户输入（增加空输入处理）
        user_input = input("\nYou: ").strip()
        if not user_input:
            print("请输入有效内容")
            continue
            
        if user_input.lower() in ["exit", "quit"]:
            print("对话结束，再见！")
            break
        
        # 添加用户消息到历史
        messages.append({"role": "user", "content": user_input})
        
        # 添加加载状态提示
        print("AI思考中...", end="", flush=True)
        
        # 创建聊天请求
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=messages,
            stream=False
        )
        
        # 获取并显示AI回复
        ai_response = response.choices[0].message.content
        print(f"\rAI: {ai_response}")  # 立即覆盖思考提示
        print()  # 添加换行保持输入提示在下一行
        
        # 添加AI回复到对话历史
        messages.append({"role": "assistant", "content": ai_response})

    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        break

# 创建GUI界面
class ChatApp:
    def __init__(self, master):
        self.master = master
        master.title("AI聊天助手")
        
        # 创建聊天历史显示框
        self.history = scrolledtext.ScrolledText(master, wrap=tk.WORD, width=50, height=20)
        self.history.pack(padx=10, pady=10)
        
        # 创建输入框和发送按钮
        input_frame = tk.Frame(master)
        input_frame.pack(pady=10)
        
        self.user_input = tk.Entry(input_frame, width=40)
        self.user_input.pack(side=tk.LEFT)
        self.user_input.bind("<Return>", lambda event: self.send_message())
        
        send_btn = tk.Button(input_frame, text="发送", command=self.send_message)
        send_btn.pack(side=tk.LEFT, padx=5)
        
        # 增加状态提示标签
        self.status = tk.Label(master, text="就绪", anchor=tk.W)
        self.status.pack(fill=tk.X, padx=10)
        
        # 绑定窗口关闭事件
        master.protocol("WM_DELETE_WINDOW", self.on_close)

    def on_close(self):
        """处理窗口关闭事件"""
        self.master.destroy()

    def send_message(self):
        text = self.user_input.get().strip()
        if not text:
            return
            
        if text.lower() in ["exit", "quit"]:
            self.master.destroy()
            return
            
        # 显示用户消息
        self.history.insert(END, f"\nYou: {text}\n")
        self.user_input.delete(0, END)
        
        # 禁用输入框和按钮
        self.user_input.config(state=tk.DISABLED)
        self.master.update()
        
        try:
            # 原有API调用逻辑
            messages.append({"role": "user", "content": text})
            response = client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                stream=False
            )
            ai_response = response.choices[0].message.content
            messages.append({"role": "assistant", "content": ai_response})
            
            # 显示AI回复
            self.history.insert(END, f"AI: {ai_response}\n")
            self.history.see(END)
            
        except Exception as e:
            self.history.insert(END, f"\n错误: {str(e)}\n")
        finally:
            self.user_input.config(state=tk.NORMAL)

    def process_request(self, text):
        """在新线程中处理请求"""
        try:
            # 原有API调用逻辑
            messages.append({"role": "user", "content": text})
            response = client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                stream=False
            )
            ai_response = response.choices[0].message.content
            messages.append({"role": "assistant", "content": ai_response})
            
            # 更新UI需要在主线程执行
            self.master.after(0, self.update_display, ai_response)
            
        except Exception as e:
            self.master.after(0, self.show_error, str(e))
        finally:
            self.master.after(0, self.reset_ui)

    def update_display(self, response):
        """更新聊天显示"""
        self.history.insert(END, f"AI: {response}\n")
        self.history.see(END)
        self.status.config(text="就绪")

    def show_error(self, msg):
        """显示错误信息"""
        self.history.insert(END, f"\n错误: {msg}\n")
        self.status.config(text="请求失败")

    def reset_ui(self):
        """恢复UI状态"""
        self.user_input.config(state=tk.NORMAL)
        self.user_input.focus()

# 启动GUI
root = tk.Tk()
app = ChatApp(root)
root.mainloop()