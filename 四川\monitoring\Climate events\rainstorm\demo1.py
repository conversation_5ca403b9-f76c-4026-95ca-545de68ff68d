#%%
import pandas as pd
import numpy as np

filepath = r'D:\python\四川\monitoring\Climate events\rainstorm\data\20-24降水.xlsx'
data = pd.read_excel(filepath)
data = data.sort_values(by='资料时间')

# 数据质量控制：处理缺测和异常值
print('开始数据质量控制...')
print(f'原始数据量: {len(data)}')

# 1. 处理缺测值
missing_values = [999999, 32700, 30000, -999, 9999]
data.loc[data['20-20时降水量'].isin(missing_values), '20-20时降水量'] = np.nan
# 2. 处理异常大值
data.loc[data['20-20时降水量'] >= 10000, '20-20时降水量'] = np.nan
# 3. 处理负值
data.loc[data['20-20时降水量'] < 0, '20-20时降水量'] = np.nan


# 选项2：用0填充缺测值
data['20-20时降水量'] = data['20-20时降水量'].fillna(0)

print(f'质控后数据量: {len(data)}')
print(f'降水量范围: {data["20-20时降水量"].min():.1f} - {data["20-20时降水量"].max():.1f} mm')
print('数据质量控制完成...')
print('资料读取完成...')

#print(data)
#%%
import numpy as np
from tqdm import tqdm

station_info = pd.read_excel(r'D:\python\四川\monitoring\Climate events\drought\data\station_info.xlsx')
stations = station_info[['站号', '纬度', '经度']].values

# 经纬度计算距离 - 使用指定公式(1)
def distance(lat1, lon1, lat2, lon2):
    """
    使用指定公式计算两个经纬度点之间的距离（公里）
    公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180

    参数：
    lat1, lon1: 站点A的纬度和经度
    lat2, lon2: 站点B的纬度和经度

    返回：距离（公里）
    """
    import math

    # 地球平均半径，取6371公里；π=3.14
    R = 6371
    pi = 3.14

    # 将经纬度转换为弧度
    '''    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    lon1_rad = math.radians(lon1)
    lon2_rad = math.radians(lon2)'''

    # 应用公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180
    cos_angle = (math.sin(lat1) * math.sin(lat2) +
                 math.cos(lat1) * math.cos(lat2) *
                 math.cos(lon1 - lon2))

    # 防止数值误差导致的域错误（arccos的定义域是[-1,1]）
    cos_angle = max(-1, min(1, cos_angle))

    # 计算距离
    distance_km = R * math.acos(cos_angle) * pi / 180

    return distance_km


# 计算所有站点之间的距离矩阵
print("正在计算站点间距离...")
print("使用公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180")
print("其中：R = 6371公里，π = 3.14")
distance_matrix = np.zeros((len(stations), len(stations)))
for i, (id1, lat1, lon1) in tqdm(enumerate(stations)):
    for j, (id2, lat2, lon2) in enumerate(stations):
        if i < j:
            dist = distance(lat1, lon1, lat2, lon2)
            distance_matrix[i][j] = dist
            distance_matrix[j][i] = dist

# 构建相邻站点字典（300km）
adjacent_stations = {}
threshold = 300 
for i, (id1, _, _) in enumerate(stations):
    adjacent = stations[distance_matrix[i] <= threshold, 0].tolist()
    adjacent.remove(id1)
    adjacent_stations[id1] = adjacent

print("相邻站点构建完成。")


# 2. 构建监测区域（所有站点组成一个大区域）
all_stations = station_info['站号'].unique().tolist()
print(f"监测区域包含{len(all_stations)}个站点。")

#%%

# 3. 预处理降水数据
print('预处理降水数据...')

data_regional = data[['区站号(字符)', '资料时间', '20-20时降水量']].copy()
# 确保时间列是 datetime 类型
data_regional['资料时间'] = pd.to_datetime(data_regional['资料时间'])

# 区分高原站和平地站
def is_plateau_station(station_id):
    """判断是否为高原站点（根据具体站点列表）"""
    # 高原站点列表
    plateau_stations = {
        '56257', '56183', '56184', '56178', '56173', '56185', '56180', '56079',
        '56038', '56357', '56147', '56182', '56158', '56247', '56441', '56152',
        '56251', '56267', '56168', '56462', '56144', '56443', '56263', '56167',
        '56371', '56172', '56171', '56374', '56146', '56164', '56097'
    }
    station_str = str(station_id)
    return station_str in plateau_stations

# 添加站点类型标识
data_regional['是否高原站'] = data_regional['区站号(字符)'].apply(is_plateau_station)

# 根据站点类型设置不同的暴雨标准
def classify_rainstorm_level(row):
    """根据站点类型和降水量分类暴雨等级"""
    precipitation = row['20-20时降水量']
    is_plateau = row['是否高原站']

    if is_plateau:
        # 高原站标准：25-50, 50-100, 100以上
        if precipitation >= 100:
            return '特大暴雨'
        elif precipitation >= 50:
            return '大暴雨'
        elif precipitation >= 25:
            return '暴雨'
        else:
            return '无暴雨'
    else:
        # 平地站标准：50-100, 100-250, 250以上
        if precipitation >= 250:
            return '特大暴雨'
        elif precipitation >= 100:
            return '大暴雨'
        elif precipitation >= 50:
            return '暴雨'
        else:
            return '无暴雨'

# 应用分类函数
data_regional['暴雨等级'] = data_regional.apply(classify_rainstorm_level, axis=1)
data_regional['暴雨日'] = data_regional['暴雨等级'] != '无暴雨'

# 统计站点类型
plateau_stations = data_regional[data_regional['是否高原站']]['区站号(字符)'].nunique()
plain_stations = data_regional[~data_regional['是否高原站']]['区站号(字符)'].nunique()
print(f"高原站点数: {plateau_stations}")
print(f"平地站点数: {plain_stations}")
print("数据预处理完成。")
# %%

# 4. 计算每日区域暴雨情况
def find_drought_clusters(drought_stations, adjacent_stations):
    """使用深度优先搜索找到所有连通的暴雨站点群组"""
    visited = set()
    clusters = []

    def dfs(station, current_cluster):
        """深度优先搜索，找到连通的暴雨站点"""
        if station in visited or station not in drought_stations:
            return
        visited.add(station)
        current_cluster.add(station)

        # 遍历所有相邻站点
        for neighbor in adjacent_stations.get(station, []):
            if neighbor in drought_stations and neighbor not in visited:
                dfs(neighbor, current_cluster)

    # 对每个未访问的暴雨站点进行DFS
    for station in drought_stations:
        if station not in visited:
            cluster = set()
            dfs(station, cluster)
            if len(cluster) >= 2:  # 至少2个相邻站点才算群组
                clusters.append(list(cluster))

    return clusters

print("开始计算每日区域暴雨情况...")
daily_stats = []
# 在循环外部初始化前一日群组
prev_group = []

for date in tqdm(data_regional['资料时间'].unique()):
    daily_data = data_regional[data_regional['资料时间'] == date]
    # 获取当日有暴雨的站点
    drought_stations = daily_data[daily_data['暴雨日']]['区站号(字符)'].unique().tolist()
    # 找到所有连通的暴雨站点群组
    drought_clusters = find_drought_clusters(drought_stations, adjacent_stations)
    # 选择最大的群组作为代表
    if drought_clusters:
        best_group = max(drought_clusters, key=len)
        cluster_count = len(drought_clusters)
    else:
        best_group = []
        cluster_count = 0

    # 判断是否达到10%阈值,15个
    is_regional_day = len(best_group)  >= 15

    # 计算与前一日群组的重合率
    current_group = set(best_group)
    prev_group_set = set(prev_group)
    
    if prev_group and current_group:
        overlap = len(current_group & prev_group_set)
        overlap_rate = overlap / len(prev_group_set)
    else:
        overlap_rate = 0.0
    
    daily_stats.append({
        '日期': date,
        '暴雨站点数': len(drought_stations),
        '有效群组站点数': len(best_group),
        '重合率': round(overlap_rate, 2),
        '群组总数': cluster_count,
        '是否区域暴雨日': is_regional_day,
        '暴雨站点列表': best_group,
        '所有群组': drought_clusters
    })
    
    # 更新前一日群组
    prev_group = best_group.copy()

daily_df = pd.DataFrame(daily_stats)
print("每日区域暴雨情况计算完成。")
print(f"总共处理了 {len(daily_df)} 天的数据")
print(f"其中区域暴雨日有 {daily_df['是否区域暴雨日'].sum()} 天")

#%%
#中间过程数据输出
daily_df.to_csv(r'D:\python\四川\monitoring\Climate events\rainstorm\data\during_data.csv', index=False)

#%% 5. 区域性暴雨过程识别

print("开始进行区域性暴雨过程识别...")

# 5.1 重新处理每日数据，识别区域性组群
def identify_regional_clusters(clusters):
    """识别区域性组群（站点数占总站点数≥10%）"""
    regional_clusters = []
    for cluster in clusters:
        if len(cluster)  >= 15:
            regional_clusters.append(cluster)
    return regional_clusters

# 5.2 计算组群重合度
def calculate_overlap_rate(prev_cluster, current_cluster):
    """计算当前组群与前一日组群的重合度"""
    if not prev_cluster or not current_cluster:
        return 0.0

    prev_set = set(prev_cluster)
    current_set = set(current_cluster)
    overlap = len(prev_set & current_set)
    return overlap / len(prev_set)

# 初始化新列
print("初始化新列...")
daily_df['区域性组群'] = [[] for _ in range(len(daily_df))]
daily_df['前一日区域性组群'] = [[] for _ in range(len(daily_df))]
daily_df['是否持续'] = False
daily_df['过程ID'] = ''

# 重新处理每日数据，添加区域性组群信息
print("重新处理每日数据，识别区域性组群...")
for i in tqdm(range(len(daily_df))):
    row = daily_df.iloc[i]
    all_clusters = row['所有群组']

    # 识别区域性组群
    regional_clusters = identify_regional_clusters(all_clusters)
    daily_df.at[i, '区域性组群'] = regional_clusters

    # 计算与前一日的重合度
    if i > 0:
        prev_regional = daily_df.iloc[i-1]['区域性组群']
        daily_df.at[i, '前一日区域性组群'] = prev_regional

        # 如果当前和前一日都有区域性组群，计算重合度
        if regional_clusters and prev_regional:
            # 选择最大的区域性组群进行比较
            current_main = max(regional_clusters, key=len)
            prev_main = max(prev_regional, key=len)
            overlap_rate = calculate_overlap_rate(prev_main, current_main)

            # 判断是否持续（重合度≥50%）
            is_continuous = overlap_rate >= 0
            daily_df.at[i, '是否持续'] = is_continuous
        else:
            daily_df.at[i, '是否持续'] = False
    else:
        daily_df.at[i, '前一日区域性组群'] = []
        daily_df.at[i, '是否持续'] = False

print("区域性组群识别完成。")

# 5.3 区域性暴雨过程识别
print("开始识别区域性暴雨过程...")

processes = []
current_process = None
process_id = 1

for i in tqdm(range(len(daily_df))):
    row = daily_df.iloc[i]
    has_regional_cluster = len(row['区域性组群']) > 0

    if current_process is None:
        # 开始新过程：当日有区域性组群
        if has_regional_cluster:
            current_process = {
                'process_id': process_id,
                'start_date': row['日期'],
                'end_date': row['日期'],
                'duration': 1,
                'dates': [row['日期']],
                'total_rainstorm_stations': len(row['暴雨站点列表']),
                'regional_clusters': [row['区域性组群']]
            }
            daily_df.at[i, '过程ID'] = process_id
    else:
        # 过程持续中
        if has_regional_cluster and row['是否持续']:
            # 过程继续
            current_process['end_date'] = row['日期']
            current_process['duration'] += 1
            current_process['dates'].append(row['日期'])
            current_process['total_rainstorm_stations'] += len(row['暴雨站点列表'])
            current_process['regional_clusters'].append(row['区域性组群'])
            daily_df.at[i, '过程ID'] = current_process['process_id']
        else:
            # 过程结束
            processes.append(current_process)
            process_id += 1

            # 检查是否立即开始新过程
            if has_regional_cluster:
                current_process = {
                    'process_id': process_id,
                    'start_date': row['日期'],
                    'end_date': row['日期'],
                    'duration': 1,
                    'dates': [row['日期']],
                    'total_rainstorm_stations': len(row['暴雨站点列表']),
                    'regional_clusters': [row['区域性组群']]
                }
                daily_df.at[i, '过程ID'] = process_id
            else:
                current_process = None
                daily_df.at[i, '过程ID'] = ''

# 处理最后一个未结束的过程
if current_process:
    processes.append(current_process)

print(f"区域性暴雨过程识别完成，共识别出 {len(processes)} 个过程。")

# 5.4 计算过程统计指标
print("计算过程统计指标...")

def calculate_process_statistics(process, data_regional, all_stations):
    """计算单个过程的详细统计指标"""
    start_date = process['start_date']
    end_date = process['end_date']
    duration = process['duration']

    # 确保日期是 datetime 对象
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)

    # 确保 data_regional 中的时间列也是 datetime 类型
    if not pd.api.types.is_datetime64_any_dtype(data_regional['资料时间']):
        data_regional['资料时间'] = pd.to_datetime(data_regional['资料时间'])

    # 获取过程期间的数据
    mask = (data_regional['资料时间'] >= start_date) & (data_regional['资料时间'] <= end_date)
    process_data = data_regional[mask]

    # 计算影响范围（过程期间所有发生暴雨的站点）
    affected_stations = set()
    daily_stats = []

    for date in process['dates']:
        # 确保日期是 datetime 对象
        if isinstance(date, str):
            date = pd.to_datetime(date)
        daily_data = data_regional[data_regional['资料时间'] == date]

        # 统计各等级暴雨站数（根据站点类型使用不同标准）
        rainstorm_stations = daily_data[daily_data['暴雨等级'] == '暴雨']
        heavy_rainstorm_stations = daily_data[daily_data['暴雨等级'] == '大暴雨']
        severe_rainstorm_stations = daily_data[daily_data['暴雨等级'] == '特大暴雨']
        all_rainstorm_stations = daily_data[daily_data['暴雨日']]

        # 获取当日最大降水的站名
        if len(all_rainstorm_stations) > 0:
            max_idx = all_rainstorm_stations['20-20时降水量'].idxmax()
            max_station_id = all_rainstorm_stations.loc[max_idx, '区站号(字符)']
            # 从原始数据中查找对应的站名
            station_name_match = data[data['区站号(字符)'] == max_station_id]['站名'].iloc[0] if len(data[data['区站号(字符)'] == max_station_id]) > 0 else max_station_id
            max_daily_precip = all_rainstorm_stations['20-20时降水量'].max()
        else:
            station_name_match = ''
            max_daily_precip = 0

        daily_stat = {
            'date': date,
            'rainstorm_count': len(rainstorm_stations)+len(heavy_rainstorm_stations)+len(severe_rainstorm_stations),
            'heavy_rainstorm_count': len(heavy_rainstorm_stations)+len(severe_rainstorm_stations),
            'severe_rainstorm_count': len(severe_rainstorm_stations),
            'max_precipitation': max_daily_precip,
            'max_precip_station': station_name_match
        }
        daily_stats.append(daily_stat)

        # 更新影响站点
        daily_rainstorm_stations = all_rainstorm_stations['区站号(字符)'].unique()
        affected_stations.update(daily_rainstorm_stations)

    # 计算平均影响范围
    total_daily_regional_stations = 0

    for date in process['dates']:
        # 确保日期是 datetime 对象
        if isinstance(date, str):
            date = pd.to_datetime(date)
        daily_data = data_regional[data_regional['资料时间'] == date]

        # 获取当日暴雨站点
        daily_rainstorm_stations = daily_data[daily_data['暴雨日']]['区站号(字符)'].unique().tolist()

        if len(daily_rainstorm_stations)  >= 15: 
            daily_regional_count = len(daily_rainstorm_stations)
        else:
            daily_regional_count = 0

        total_daily_regional_stations += daily_regional_count

    # 平均影响范围 = 总的日区域性组群站点数 / 持续天数
    avg_influence_range = total_daily_regional_stations / duration if duration > 0 else 0
    
    # 计算平均强度（过程期间所有暴雨站点的平均降水量）
    rainstorm_data = process_data[process_data['暴雨日']]
    avg_intensity = rainstorm_data['20-20时降水量'].mean() if len(rainstorm_data) > 0 else 0

    # 计算综合强度 - 使用公式(4): Z = Ia × Aa^0.5 × T^0.5
    # Ia: 区域性暴雨过程平均强度
    # Aa: 区域性暴雨过程平均范围（站点数）
    # T: 区域性暴雨过程持续时间长度
    comprehensive_intensity = avg_intensity * (avg_influence_range ** 0.5) * (duration ** 0.5)

    # 确定等级（基于综合强度）
    if comprehensive_intensity >= 100:
        level = "特强"
    elif comprehensive_intensity >= 50:
        level = "强"
    elif comprehensive_intensity >= 20:
        level = "中等"
    else:
        level = "弱"

    # 找出整个过程中所有站点的最大和次大降水量
    # 获取过程期间所有暴雨站点的降水记录，并合并站名信息
    all_rainstorm_records = process_data[process_data['暴雨日']].copy()

    # 从原始数据中获取站名信息
    station_names = data[['区站号(字符)', '站名']].drop_duplicates()
    all_rainstorm_records = all_rainstorm_records.merge(station_names, on='区站号(字符)', how='left')

    if len(all_rainstorm_records) > 0:
        # 按降水量排序，找出最大和次大值
        sorted_records = all_rainstorm_records.sort_values('20-20时降水量', ascending=False)

        # 最大降水
        max_record = sorted_records.iloc[0]
        max_precip = {
            'max_precipitation': max_record['20-20时降水量'],
            'date': max_record['资料时间'],
            'max_precip_station': max_record['站名'] if pd.notna(max_record['站名']) else max_record['区站号(字符)']
        }

        # 次大降水
        if len(sorted_records) > 1:
            second_record = sorted_records.iloc[1]
            second_max_precip = {
                'max_precipitation': second_record['20-20时降水量'],
                'date': second_record['资料时间'],
                'max_precip_station': second_record['站名'] if pd.notna(second_record['站名']) else second_record['区站号(字符)']
            }
        else:
            second_max_precip = {'max_precipitation': 0, 'date': '', 'max_precip_station': ''}
    else:
        max_precip = {'max_precipitation': 0, 'date': '', 'max_precip_station': ''}
        second_max_precip = {'max_precipitation': 0, 'date': '', 'max_precip_station': ''}

    # 找出最多暴雨站数的日期
    max_rainstorm_day = max(daily_stats, key=lambda x: x['rainstorm_count']) if daily_stats else {'rainstorm_count': 0, 'date': ''}

    # 统计总站数
    total_rainstorm_stations = sum([d['rainstorm_count'] for d in daily_stats])
    total_heavy_rainstorm_stations = sum([d['heavy_rainstorm_count'] for d in daily_stats])
    total_severe_rainstorm_stations = sum([d['severe_rainstorm_count'] for d in daily_stats])

    return {
        '开始时间': start_date.strftime('%Y-%m-%d'),
        '结束时间': end_date.strftime('%Y-%m-%d'),
        '持续时间': duration,
        '平均强度': round(avg_intensity, 1),
        '平均影响范围': round(avg_influence_range, 1),
        '综合强度': round(comprehensive_intensity, 2),
        '最大日降水': round(max_precip['max_precipitation'], 1),
        '最大日降水出现日期': max_precip['date'].strftime('%Y-%m-%d') if hasattr(max_precip['date'], 'strftime') else str(max_precip['date']),
        '最大日降水出现站': max_precip['max_precip_station'],
        '次大日降水': round(second_max_precip['max_precipitation'], 1),
        '次大日降水出现日期': second_max_precip['date'].strftime('%Y-%m-%d') if hasattr(second_max_precip['date'], 'strftime') else str(second_max_precip['date']),
        '次大日降水出现站': second_max_precip['max_precip_station'],
        '最多暴雨站数': max_rainstorm_day['rainstorm_count'],
        '最多暴雨站数出现日期': max_rainstorm_day['date'].strftime('%Y-%m-%d') if hasattr(max_rainstorm_day['date'], 'strftime') else str(max_rainstorm_day['date']),
        '暴雨站数': total_rainstorm_stations,
        '大暴雨站数': total_heavy_rainstorm_stations,
        '特大暴雨站数': total_severe_rainstorm_stations
    }

# 生成过程统计表
print("生成过程统计表...")
process_stats = []
for process in processes:
    stats = calculate_process_statistics(process, data_regional, all_stations)
    process_stats.append(stats)

process_df = pd.DataFrame(process_stats)

# 保存过程统计表（严格按照指定格式）
process_output_path = r'D:\python\四川\monitoring\Climate events\rainstorm\data\区域性暴雨过程统计表.csv'
process_df.to_csv(process_output_path, index=False, encoding='utf-8-sig')
print(f"区域性暴雨过程统计表已保存至: {process_output_path}")


print("\n=== 站点类型统计 ===")
print(f"高原站点数: {plateau_stations}")
print(f"平地站点数: {plain_stations}")
print(f"总站点数: {plateau_stations + plain_stations}")

print("\n=== 暴雨等级标准 ===")
print("高原站（指定31个站点）：")
print("  暴雨: 25-50mm")
print("  大暴雨: 50-100mm")
print("  特大暴雨: ≥100mm")
print("平地站（其他站点）：")
print("  暴雨: 50-100mm")
print("  大暴雨: 100-250mm")
print("  特大暴雨: ≥250mm")

print("\n=== 高原站点列表 ===")
plateau_list = [
    '56257', '56183', '56184', '56178', '56173', '56185', '56180', '56079',
    '56038', '56357', '56147', '56182', '56158', '56247', '56441', '56152',
    '56251', '56267', '56168', '56462', '56144', '56443', '56263', '56167',
    '56371', '56172', '56171', '56374', '56146', '56164', '56097'
]
print(f"高原站点({len(plateau_list)}个): {', '.join(plateau_list)}")

print("\n区域性暴雨过程识别完成！")



# %%
