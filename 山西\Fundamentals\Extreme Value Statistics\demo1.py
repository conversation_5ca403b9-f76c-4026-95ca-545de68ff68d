#%%
import pandas as pd
import numpy as np

file_path = r'D:\python\山西\data\SURF_CHN_MUL_DAY(2020-2024).xlsx'
df = pd.read_excel(file_path).replace({
        '20-20时降水量': {999999: np.nan, 999990: np.nan, 999998: np.nan},
        '平均气温': {999999: np.nan, 999990: np.nan, 999998: np.nan}
    })
df = df.sort_values(by='资料时间', ascending=True).reset_index(drop=True)
df['资料时间'] = pd.to_datetime(df['资料时间'])

# 使用更高效的筛选方式
def filter_data(df, years, months):
    return df[df['资料时间'].dt.year.isin(years) & df['资料时间'].dt.month.isin(months)]

statistics_period = filter_data(df, [2024], [2,3,4,5,6,7,8,9,10,11])
comparison_period = filter_data(df, [2020,2021,2022,2023,2024], [2,3,4,5,6,7,8,9,10,11])

station_info = pd.read_excel(r'D:\python\山西\data\station_id.xlsx')
station_dict = dict(zip(station_info['station_id'], station_info['station_name']))

# 优化后的极值计算函数
def get_extreme_info(data, station, column):
    """获取指定站点和列的最大值及其出现时间"""
    station_data = data[data['区站号(字符)'] == station]
    if station_data.empty:
        return np.nan, pd.NaT
    max_value = station_data[column].max()
    max_dates = station_data.loc[station_data[column] == max_value, '资料时间']
    return max_value, max_dates.iloc[0] if not max_dates.empty else pd.NaT

# 重构数据处理循环
temp_results = []
pre_results = []

for station in station_info['station_id']:
    # 温度处理
    stat_temp, stat_temp_time = get_extreme_info(statistics_period, station, '平均气温')
    comp_temp, comp_temp_time = get_extreme_info(comparison_period, station, '平均气温')
    temp_results.append({
        '站点': station,
        '站名': station_dict.get(station, '未知站点'),
        '统计时段极值': stat_temp,
        '统计时段极值出现时间': stat_temp_time,
        '对比时段极值': comp_temp,
        '对比时段极值出现时间': comp_temp_time
    })
    
    # 降水处理
    stat_pre, stat_pre_time = get_extreme_info(statistics_period, station, '20-20时降水量')
    comp_pre, comp_pre_time = get_extreme_info(comparison_period, station, '20-20时降水量')
    pre_results.append({
        '站点': station,
        '站名': station_dict.get(station, '未知站点'),
        '统计时段极值': stat_pre,
        '统计时段极值出现时间': stat_pre_time,
        '对比时段极值': comp_pre,
        '对比时段极值出现时间': comp_pre_time
    })

# 结果保存
pd.DataFrame(temp_results).to_excel(r'D:\python\山西\Extreme Value Statistics\data\temp_results_df.xlsx', index=False)
pd.DataFrame(pre_results).to_excel(r'D:\python\山西\Extreme Value Statistics\data\pre_results_df.xlsx', index=False)