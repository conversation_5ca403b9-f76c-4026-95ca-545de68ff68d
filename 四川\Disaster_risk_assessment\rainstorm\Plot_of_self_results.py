from matplotlib.colors import ListedColormap, BoundaryNorm
import rasterio
from rasterio.mask import mask
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd


# 添加中文字体配置（放在所有绘图代码之前）
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文显示字体为黑体（SimHei）
plt.rcParams['axes.unicode_minus'] = False     # 正确显示负号


# 读取成都市shp边界文件
shp_path = r'D:\python\Disaster_risk_assessment\High_Temp\day\data\cd\shp\成都.shp'  
gdf = gpd.read_file(shp_path)

file_path_data = r'D:\python\Disaster_risk_assessment\rainstorm\data\rainstorm_metrics.xlsx'
df = pd.read_excel(file_path_data)
data_hazard = df['暴雨过程强度指数'].values
# 保留三位小数
data_hazard = np.round(data_hazard, 3)
lon = df['经度'].values
lat = df['纬度'].values

# 读取栅格数据
with rasterio.open(r'D:\python\Disaster_risk_assessment\rainstorm\data\tif\assess_level_self.tif') as src:
    # 裁剪栅格数据到shp边界内
    out_image, out_transform = mask(src, gdf.geometry, crop=True)
    out_meta = src.meta.copy()
    out_meta.update({"driver": "GTiff",
                     "height": out_image.shape[1],
                     "width": out_image.shape[2],
                     "transform": out_transform})

    data = out_image[0]

    # 创建一个与原始数据相同大小的新数组，初始值为np.nan
    new_data = np.full((src.height, src.width), np.nan)
    # 将裁剪后的数据放回新数组中
    rows, cols = np.where(~np.isnan(data))
    new_data[rows, cols] = data[rows, cols]
    new_data[new_data == 0] = np.nan  

    #print(out_meta)

    # 定义风险等级对应的颜色
    colors = ['#FCDAD5', '#F5A89A', '#EE7C6B', '#DF0029', '#B2001F']
    cmap = ListedColormap(colors)

    # 定义边界
    boundaries = [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]
    norm = BoundaryNorm(boundaries, cmap.N)

    plt.imshow(new_data, cmap=cmap, norm=norm)
    plt.colorbar(ticks=[1, 2, 3, 4, 5],
                 label='Risk Level')
    plt.title('Risk Level')
    plt.savefig(r'D:\python\Disaster_risk_assessment\rainstorm\data\photo\Risk Level.png', dpi=400)
    plt.show()




with rasterio.open(r'D:\python\Disaster_risk_assessment\rainstorm\data\tif\hazard_level_self.tif') as src:
    # 裁剪栅格数据到shp边界内
    out_image, out_transform = mask(src, gdf.geometry, crop=True)
    out_meta = src.meta.copy()
    out_meta.update({"driver": "GTiff",
                     "height": out_image.shape[1],
                     "width": out_image.shape[2],
                     "transform": out_transform})

    data = out_image[0]

    # 创建一个与原始数据相同大小的新数组，初始值为np.nan
    new_data = np.full((src.height, src.width), np.nan)
    # 将裁剪后的数据放回新数组中
    rows, cols = np.where(~np.isnan(data))
    new_data[rows, cols] = data[rows, cols]
    new_data[new_data == 0] = np.nan 

    # 绘图
    # 定义风险等级对应的颜色
    colors = ['#FFFF00', '#FFB226', '#FF7333', '#CC2600', '#A31447']
    cmap = ListedColormap(colors)

    # 定义边界
    boundaries = [0.5, 1.5, 2.5, 3.5, 4.5, 5.5]
    norm = BoundaryNorm(boundaries, cmap.N)

        # 将经纬度转换为格点位置
    row_indices = []
    col_indices = []
    for i in range(len(lon)):
        col, row = ~src.transform * (lon[i], lat[i])
        row_indices.append(int(row))
        col_indices.append(int(col))

    # 添加散点图
    for i in range(len(row_indices)):
        plt.scatter(col_indices[i], row_indices[i], c='black', s=10, marker='o', label='站点' if i == 0 else "")

    # 添加监测站点的标注
    for i in range(len(row_indices)):
        plt.annotate(df['站名'][i], (col_indices[i], row_indices[i]), textcoords="offset points", xytext=(0, 10), ha='center', fontsize=8, color='black')
        plt.annotate(data_hazard[i], (col_indices[i], row_indices[i]), textcoords="offset points", xytext=(20, 10), ha='center', fontsize=8, color='black')


    plt.imshow(new_data, cmap=cmap, norm=norm)
    plt.colorbar(ticks=[1, 2, 3, 4, 5],
                 label='Hazard Level')
    plt.title('Hazard Level')
    plt.savefig(r'D:\python\Disaster_risk_assessment\rainstorm\data\photo\Hazard Level.png', dpi=400)
    plt.show()