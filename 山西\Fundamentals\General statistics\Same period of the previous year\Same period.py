#%%
import pandas as pd
import numpy as np

file_path = r'D:\python\山西\data\SURF_CHN_MUL_DAY(2020-2024).xlsx'
df = pd.read_excel(file_path)
df = df.sort_values(by='资料时间', ascending=True).reset_index(drop=True)
df['资料时间'] = pd.to_datetime(df['资料时间'])

data = df[
    (df['资料时间'].dt.year.isin([2020, 2021, 2022, 2023, 2024])) & 
    (df['资料时间'].dt.month.isin([3, 4, 5, 6, 7, 8, 9]))
].replace({
        '20-20时降水量': {999999: np.nan, 999990: np.nan, 999998: np.nan},
        '平均气温': {999999: np.nan, 999990: np.nan, 999998: np.nan}
    })


#%%
years = [2020, 2021, 2022, 2023, 2024]
stations = df['区站号(字符)'].unique()
ave_temp = []
total_pre = []
for station in stations:
    station_data = data[data['区站号(字符)'] == station]
    for year in years:
        ave_t = station_data[station_data['资料时间'].dt.year == year]['平均气温'].mean()
        total_p = station_data[station_data['资料时间'].dt.year == year]['20-20时降水量'].sum()
        ave_temp.append(
            {'station': station,
             'year': year, 
             '原值': ave_t})
        total_pre.append(
            {'station': station, 
             'year': year,
             '原值': total_p}
        )

ave_temp_df = pd.DataFrame(ave_temp)
total_pre_df = pd.DataFrame(total_pre)
print(ave_temp_df)
print(total_pre_df)
ave_temp_df.to_excel(r'D:\python\山西\General statistics\Same period of the previous year\data\ave_temp_df.xlsx', index=False)
total_pre_df.to_excel(r'D:\python\山西\General statistics\Same period of the previous year\data\total_pre_df.xlsx', index=False)

# %%
