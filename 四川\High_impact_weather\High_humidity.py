import numpy as np
import pandas as pd


def read_data():
    """读取并预处理数据"""
    # 从Excel文件读取数据
    data = pd.read_excel(r'D:\python\High_impact_weather\data\中国地面日值数据(SURF_CHN_MUL_DAY).xlsx')
    
    # 获取年月日列
    year = data['年']
    month = data['月']
    day = data['日']
    
    data = data[data['年'] == 2024]
    # 将年月日组合成datetime格式
    date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d')
    
    # 删除原始的年月日列
    data.drop(['年', '月', '日'], axis=1, inplace=True)
    
    # 添加新的日期列并设置为索引
    data['date'] = date_time
    data.set_index('date', inplace=True)
    
    # 按日期排序
    data.sort_index(inplace=True)
    # 取2024年的所有数据


    
    return data

# 统计高湿日数
def calculate_humidity_difference(data):
    """
    计算每个月的高湿日数
    湿度超过85%
    """
    # 提取平均相对湿度列
    humidity = data['平均相对湿度']
    humidity = humidity.replace(999999, 0)
    # 计算每个月的高湿日数,并除以5
    high_humidity_days = humidity.groupby(humidity.index.month).apply((lambda x: (x >= 85).sum()))
    return high_humidity_days




if __name__ == "__main__":
    # 读取数据
    data = read_data()
    # 计算高湿日数
    high_humidity_days = calculate_humidity_difference(data)
    # 输出结果
    #print(data)
    print(high_humidity_days)
