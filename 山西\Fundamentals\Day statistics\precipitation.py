#%%
import pandas as pd

file_path = r'D:\python\山西\data\SURF_CHN_MUL_DAY(2020-2024).xlsx'
df = pd.read_excel(file_path).replace({
        '20-20时降水量': {999999: pd.NA, 999990: pd.NA, 999998: pd.NA},
        '平均气温': {999999: pd.NA, 999990: pd.NA, 999998: pd.NA}
})
df = df.sort_values(by='资料时间', ascending=True).reset_index(drop=True)
df['资料时间'] = pd.to_datetime(df['资料时间'])

print("数据预处理完成")
print(df.head())

#%%
station_info = pd.read_excel(r'D:\python\山西\data\station_id.xlsx')
station_dict = dict(zip(station_info['station_id'], station_info['station_name']))


# %%
years = [2020,2021,2022,2023,2024]
months = [3,4,5,6,7,8]
results1 = []
for station in station_info['station_id']:
    station_data = df[df['区站号(字符)'] == station].copy()
    station_data = station_data[station_data['资料时间'].dt.year.isin(years) & station_data['资料时间'].dt.month.isin(months)]
    station_data['大于10mm'] = station_data['20-20时降水量'] > 10
    station_data['大于10mm'] = station_data['大于10mm'].astype(int)
    for year in years:
        days = station_data[(station_data['资料时间'].dt.year == year) & (station_data['大于10mm'] == 1)]['20-20时降水量'].count()
        results1.append({
            '站点': station,
            '站名': station_dict[station],
            '年份': year,
            '时段总日数': days
        })

results_df1 = pd.DataFrame(results1)
results_df1.to_excel(r'D:\python\山西\Fundamentals\Day statistics\data\降水日数统计表.xlsx', index=False)

result2 = []
for year in years:
    year_data = df[df['资料时间'].dt.year == year].copy()
    year_data['大于10mm'] = year_data['20-20时降水量'] > 10
    stations = year_data[year_data['大于10mm'] == 1]['区站号(字符)'].nunique()
    result2.append({
        '区域': '山西省',
        '年份': year,
        '时段总站点数': stations 
    })

results_df2 = pd.DataFrame(result2)
results_df2.to_excel(r'D:\python\山西\Fundamentals\Day statistics\data\降水站点统计表.xlsx', index=False)


print("数据处理完成")

# %%
