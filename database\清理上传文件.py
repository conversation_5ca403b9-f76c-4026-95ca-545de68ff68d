#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理Web应用uploads文件夹中的临时文件
"""

import os
import shutil
from datetime import datetime

def clean_uploads_folder():
    """清理uploads文件夹"""
    uploads_path = os.path.join('web_app', 'uploads')
    
    if not os.path.exists(uploads_path):
        print("uploads文件夹不存在")
        return
    
    print(f"正在清理文件夹: {uploads_path}")
    
    # 统计文件
    file_count = 0
    total_size = 0
    
    for root, dirs, files in os.walk(uploads_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            total_size += file_size
            file_count += 1
            print(f"  - {file} ({file_size} bytes)")
    
    if file_count == 0:
        print("uploads文件夹已经是空的")
        return
    
    print(f"\n找到 {file_count} 个文件，总大小: {total_size} bytes")
    
    # 询问是否删除
    response = input("\n是否删除这些文件? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        # 删除所有文件但保留文件夹结构
        for root, dirs, files in os.walk(uploads_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    print(f"已删除: {file}")
                except Exception as e:
                    print(f"删除失败 {file}: {e}")
        
        print(f"\n清理完成! 已删除 {file_count} 个文件")
    else:
        print("取消清理")

if __name__ == "__main__":
    clean_uploads_folder()
