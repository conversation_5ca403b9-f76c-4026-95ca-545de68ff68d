import pandas as pd

# 读取 Excel 文件
excel_file = r'D:\python\Disaster_risk_assessment\drought\data\2022年成都参证站列表逐日MCI指数统计图.xlsx'
df = pd.read_excel(excel_file, sheet_name='sheet1')

# 将数据转换为 DataFrame 格式
data = pd.DataFrame({
    'time': df['时间'],
    '56181': df['崇州'],
    '56188': df['都江堰'],
    '56189': df['彭州'],
    '56272': df['郫都'],
    '56276': df['新津'],
    '56281': df['蒲江'],
    '56284': df['邛崃'],
})

# 判断无旱、轻旱、中旱、重旱、特旱(返回整数分类)
def classify_drought_intensity(mci_value):
    if -0.5 < mci_value:
        return 0
    elif -1.0 < mci_value <= -0.5:
        return 1
    elif -1.5 < mci_value <= -1.0:
        return 2
    elif -2.0 < mci_value <= -1.5:
        return 3
    else:
        return 4

# 应用分类函数到每个站点，追加在每一列站点数据后面(转换为整数分类)
for station in data.columns[1:]:  # 跳过时间列
    data[f'{station}_分类'] = data[station].apply(classify_drought_intensity).astype(int)

# 累计干旱强度
def calculate_cumulative_drought_intensity(data):
    n = len(data)
    a = 0.5
    data_Sn = (n ** (a - 1)) * abs(data.sum())
    return data_Sn

# 干旱过程强度
def calculate_drought_process_intensity(data):
    max_zs = -float('inf')
    m = len(data)
    for i in range(m):  # 遍历所有起始点
        for n in range(1, m - i + 1):  # 遍历所有可能的持续天数
            subset = data.iloc[i:i + n]
            current_Sn = calculate_cumulative_drought_intensity(subset)
            if current_Sn > max_zs:
                max_zs = current_Sn
    return max_zs

# 判断干旱过程
def classify_drought_process():
    drought_processes = []
    total_process_count = 0
    total_process_duration = 0
    for station in data.columns[1:]:
        if '_分类' not in station:
            continue
        classification_col = data[station]  # 已经是整数类型
        time_col = data['time']
        original_station_col = data[station.replace('_分类', '')]

        in_drought = False  # 是否处于干旱过程
        start_index = None  # 干旱过程开始索引(第一次轻旱及以上的位置)
        last_non_zero_index = None  # 最后一次轻旱及以上的位置
        has_moderate = False  # 过程中是否有中旱及以上(分类>=2)
        current_consecutive_zero = 0  # 连续无旱天数

        i = 0
        while i < len(classification_col):
            c = classification_col[i]

            if c >= 1:  # 轻旱及以上
                if not in_drought:
                    # 新干旱过程开始
                    start_index = i
                    last_non_zero_index = i
                    has_moderate = c >= 2  # 检查当前是否中旱及以上
                    in_drought = True
                    current_consecutive_zero = 0
                else:
                    # 更新最后轻旱位置，并检查是否有中旱
                    last_non_zero_index = i
                    if c >= 2:
                        has_moderate = True
                i += 1
            else:  # 无旱(c=0)
                if in_drought:
                    # 开始计算连续无旱天数
                    current_consecutive_zero = 1
                    j = i + 1
                    while j < len(classification_col) and current_consecutive_zero < 5:
                        if classification_col[j] == 0:
                            current_consecutive_zero += 1
                        else:
                            # 遇到轻旱及以上，中断无旱计数，更新最后位置
                            last_non_zero_index = j
                            if classification_col[j] >= 2:
                                has_moderate = True
                            current_consecutive_zero = 0  # 重置无旱计数
                            break
                        j += 1

                    # 检查是否达到5天连续无旱，结束过程
                    if current_consecutive_zero == 5 or j == len(classification_col):
                        duration = last_non_zero_index - start_index + 1  # 包含开始和结束日
                        if duration >= 15 and has_moderate:
                            start_time = time_col[start_index]
                            end_time = time_col[last_non_zero_index]
                            drought_data = original_station_col[start_index:last_non_zero_index+1]
                            cumulative_intensity = calculate_cumulative_drought_intensity(drought_data)
                            process_intensity = calculate_drought_process_intensity(drought_data)

                            drought_processes.append({
                                'station': station.replace('_分类', ''),
                                'start_time': start_time,
                                'end_time': end_time,
                                'duration': duration,
                                'cumulative_intensity': cumulative_intensity,
                                'process_intensity': process_intensity
                            })
                            total_process_count += 1
                            total_process_duration += duration

                        # 退出干旱状态
                        in_drought = False
                        start_index = None
                        last_non_zero_index = None
                        has_moderate = False
                        current_consecutive_zero = 0
                        i = j  # 移动到下一个处理位置
                    else:
                        i = j  # 继续处理后续可能的轻旱
                else:
                    i += 1  # 不在干旱过程中，继续寻找开始点

        # 处理数据末尾未结束的干旱过程(没有遇到5天无旱)
        if in_drought:
            duration = last_non_zero_index - start_index + 1
            if duration >= 15 and has_moderate:
                start_time = time_col[start_index]
                end_time = time_col[last_non_zero_index]
                drought_data = original_station_col[start_index:last_non_zero_index+1]
                cumulative_intensity = calculate_cumulative_drought_intensity(drought_data)
                process_intensity = calculate_drought_process_intensity(drought_data)

                drought_processes.append({
                    'station': station.replace('_分类', ''),
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'cumulative_intensity': cumulative_intensity,
                    'process_intensity': process_intensity
                })
                total_process_count += 1
                total_process_duration += duration

    return drought_processes, total_process_count, total_process_duration

# 调用函数进行干旱过程判断
processes, count, duration = classify_drought_process()

# 按站点分组输出信息
station_info = {}
for process in processes:
    station = process['station']
    if station not in station_info:
        station_info[station] = []
    station_info[station].append(process)

# 准备保存到 Excel 的数据
all_processes_data = []
station_summary_data = []
for station, process_list in station_info.items():
    total_station_duration = sum([p['duration'] for p in process_list])
    for process in process_list:
        all_processes_data.append({
            '站点': station,
            '开始时间': process['start_time'],
            '结束时间': process['end_time'],
            '持续天数': process['duration'],
            '累计干旱强度': process['cumulative_intensity'],
            '干旱过程强度': process['process_intensity']
        })
    station_summary_data.append({
        '站点': station,
        '总干旱过程数量': len(process_list),
        '总持续天数': total_station_duration
    })

# 创建 DataFrame
all_processes_df = pd.DataFrame(all_processes_data)
station_summary_df = pd.DataFrame(station_summary_data)

# 保存到 Excel 文件
with pd.ExcelWriter(r'D:\python\Disaster_risk_assessment\drought\data\drought_process_results.xlsx') as writer:
    all_processes_df.to_excel(writer, sheet_name='所有干旱过程', index=False)
    station_summary_df.to_excel(writer, sheet_name='站点汇总信息', index=False)


# 计算每个站点的累计干旱强度和干旱过程强度的平均值
station_metrics = {}
for station, process_list in station_info.items():
    if len(process_list) > 0:
        avg_cumulative_intensity = sum([p['cumulative_intensity'] for p in process_list]) / len(process_list)
        avg_process_intensity = sum([p['process_intensity'] for p in process_list]) / len(process_list)
        station_metrics[station] = {
            'avg_cumulative_intensity': avg_cumulative_intensity,
            'avg_process_intensity': avg_process_intensity
        }

# 归一化处理
cumulative_intensities = [metrics['avg_cumulative_intensity'] for metrics in station_metrics.values()]
process_intensities = [metrics['avg_process_intensity'] for metrics in station_metrics.values()]

min_cumulative = min(cumulative_intensities)
max_cumulative = max(cumulative_intensities)
min_process = min(process_intensities)
max_process = max(process_intensities)

for station, metrics in station_metrics.items():
    normalized_cumulative = 0.5 + 0.5 * (metrics['avg_cumulative_intensity'] - min_cumulative) / (max_cumulative - min_cumulative)
    normalized_process = 0.5 + 0.5 * (metrics['avg_process_intensity'] - min_process) / (max_process - min_process)
    metrics['normalized_cumulative'] = normalized_cumulative
    metrics['normalized_process'] = normalized_process

# 计算危险性指数（等权法）
risk_indexes = {}
for station, metrics in station_metrics.items():
    # 等权法，每个因子权重为 0.5
    risk_index = 0.5 * metrics['normalized_cumulative'] + 0.5 * metrics['normalized_process']
    risk_indexes[station] = risk_index

# 输出每个站点的危险性指数
for station, risk_index in risk_indexes.items():
    print(f"站点 {station} 的危险性指数: {risk_index}")

# 保存危险性指数到 Excel
risk_index_data = [{'站点': station, '危险性指数': risk_index} for station, risk_index in risk_indexes.items()]
risk_index_df = pd.DataFrame(risk_index_data)
with pd.ExcelWriter(r'D:\python\Disaster_risk_assessment\drought\data\drought_process_results.xlsx', mode='a', engine='openpyxl') as writer:
    risk_index_df.to_excel(writer, sheet_name='危险性指数', index=False)

