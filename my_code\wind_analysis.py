import pandas as pd
import numpy as np

def normalize_data():
    """第一步：数据标准化"""
    # 读取原始数据
    df = pd.read_csv(r'D:\python\data\区域大风\wind.txt', sep='\t', encoding='utf-8')
    
    # 标准化处理
    normalized_df = df.apply(lambda series: (series - series.min()) / (series.max() - series.min()))
    
    # 保存标准化结果
    normalized_df.to_csv(r'D:\python\data\区域大风\wind_normalized.txt', sep='\t', index=False, encoding='utf-8')
    return normalized_df

def calculate_intensity(normalized_df):
    """第二步：计算综合强度"""
    # 权重系数
    A, B, C = 0.32, 0.38, 0.30
    
    # 计算综合强度
    normalized_df['综合强度'] = (A * normalized_df['最大值m/s'] + 
                              B * normalized_df['平均值m/s'] + 
                              C * normalized_df['站数'])
    
    # 按综合强度降序排序
    result = normalized_df.sort_values(by='综合强度', ascending=False)
    
    # 保存结果
    result.to_csv(r'D:\python\data\区域大风\wind_intensity.txt', sep='\t', index=False, encoding='utf-8')
    return result

def calculate_percentiles(df_with_intensity):
    """第三步：计算百分位数，展示对应的原始值"""
    # 读取原始数据
    original_df = pd.read_csv(r'D:\python\data\区域大风\wind.txt', sep='\t', encoding='utf-8')
    
    # 创建包含原始值和标准化后综合强度的数据框
    combined_df = pd.DataFrame({
        '最大值m/s': original_df['最大值m/s'],
        '平均值m/s': original_df['平均值m/s'],
        '站数': original_df['站数'],
        '综合强度': df_with_intensity['综合强度']
    })
    
    # 按综合强度降序排序
    combined_df = combined_df.sort_values(by='综合强度', ascending=False)
    
    percentiles = [90, 70, 50, 30]
    intensity_percentiles = np.percentile(combined_df['综合强度'], percentiles)
    
    # 保存结果到文件
    with open(r'D:\python\data\区域大风\percentile_results.txt', 'w', encoding='utf-8') as f:
        f.write("百分位数分析结果\n")
        for p, threshold in zip(percentiles, intensity_percentiles):
            # 找出最接近百分位数的记录
            closest_record = combined_df.iloc[
                (combined_df['综合强度'] - threshold).abs().argsort()[:1]
            ]
            
            f.write(f"\n{p}% 分位数指标值：\n")
            f.write(f"最大值: {closest_record['最大值m/s'].values[0]:.1f}\n")
            f.write(f"平均值: {closest_record['平均值m/s'].values[0]:.1f}\n")
            f.write(f"站数: {closest_record['站数'].values[0]:.1f}\n")
            f.write(f"综合强度: {closest_record['综合强度'].values[0]:.4f}\n")
    
    return intensity_percentiles

def main():
    """主程序"""
    print("1. 开始数据标准化...")
    normalized_data = normalize_data()
    print("   标准化完成！")
    
    print("\n2. 计算综合强度...")
    intensity_data = calculate_intensity(normalized_data)
    print("   综合强度计算完成！")
    
    print("\n3. 计算百分位数...")
    percentile_results = calculate_percentiles(intensity_data)
    print("   百分位数计算完成！")
    
    print("\n所有计算已完成，结果已保存到相应文件中。")

if __name__ == "__main__":
    main()
