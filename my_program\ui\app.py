import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from core.data_processor import DataProcessor
from ui.components.missing_values_window import MissingValuesWindow
from ui.components.filter_window import FilterWindow
from ui.components.statistics_window import StatisticsWindow

class ExcelProcessor:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel数据处理系统")
        self.root.geometry("900x650")
        self.root.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        
        self.data_processor = DataProcessor()
        self.setup_ui()

    def setup_ui(self):
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.N, tk.S, tk.E, tk.W))
        self.main_frame.rowconfigure(1, weight=1)
        self.main_frame.columnconfigure(1, weight=1)

        self.setup_file_frame()
        self.setup_options_frame()
        self.setup_result_frame()

    def setup_file_frame(self):
        # 文件选择区域
        self.file_frame = ttk.LabelFrame(self.main_frame, text="文件操作", padding="8")
        self.file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        self.file_frame.columnconfigure(0, weight=1)
        
        self.file_path = tk.StringVar()
        ttk.Entry(self.file_frame, textvariable=self.file_path, width=60).grid(
            row=0, column=0, padx=(0, 8), sticky=(tk.W, tk.E)
        )
        ttk.Button(self.file_frame, text="选择文件", command=self.select_file).grid(
            row=0, column=1, padx=(0, 8)
        )

    def setup_options_frame(self):
        # 数据处理选项区域
        self.options_frame = ttk.LabelFrame(self.main_frame, text="数据处理选项", padding="8")
        self.options_frame.grid(row=1, column=0, sticky=(tk.N, tk.S, tk.E, tk.W), padx=(0, 8))
        
        buttons = [
            ("数据预览", self.preview_data),
            ("基础统计", self.basic_statistics),
            ("数据筛选", self.filter_data),
            ("处理缺失值", self.handle_missing_values),
            ("常规统计", self.show_general_statistics_menu)
        ]

        for i, (text, command) in enumerate(buttons):
            ttk.Button(self.options_frame, text=text, command=command).grid(
                row=i, column=0, pady=(0, 8), padx=5, sticky=(tk.W, tk.E)
            )

    def setup_result_frame(self):
        # 结果显示区域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="结果显示", padding="8")
        self.result_frame.grid(row=1, column=1, sticky=(tk.N, tk.S, tk.E, tk.W))
        self.result_frame.rowconfigure(0, weight=1)
        self.result_frame.columnconfigure(0, weight=1)

        # 添加文本框和滚动条
        self.result_text = tk.Text(self.result_frame, width=60, height=25, wrap=tk.NONE)
        self.result_text.grid(row=0, column=0, sticky=(tk.N, tk.S, tk.E, tk.W))
        
        yscroll = ttk.Scrollbar(self.result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        yscroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        xscroll = ttk.Scrollbar(self.result_frame, orient=tk.HORIZONTAL, command=self.result_text.xview)
        xscroll.grid(row=1, column=0, sticky=(tk.E, tk.W))
        
        self.result_text.configure(yscrollcommand=yscroll.set, xscrollcommand=xscroll.set)

    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx;*.xls")])
        if file_path:
            self.file_path.set(file_path)
            self.load_file_with_progress(file_path)

    def load_file_with_progress(self, file_path):
        # 创建进度条窗口
        progress_window = tk.Toplevel(self.root)
        progress_window.title("加载文件")
        progress_window.geometry("300x100")
        progress_window.transient(self.root)
        # 居中显示
        progress_window.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - progress_window.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - progress_window.winfo_height()) // 2
        progress_window.geometry(f"+{x}+{y}")
        progress_window.grab_set()
        

        ttk.Label(progress_window, text="正在加载文件，请稍候...").pack(pady=10)
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate', length=200)
        progress_bar.pack(pady=10)
        progress_bar.start()

        def load_file():
            success, result = self.data_processor.load_data(file_path)
            if success:
                columns_info = "\n列名：" + "、".join(result)
                messagebox.showinfo("成功", f"文件加载成功！{columns_info}")
                self.preview_data()
            else:
                messagebox.showerror("错误", f"文件加载失败：{result}")
            progress_window.destroy()

        thread = threading.Thread(target=load_file)
        thread.daemon = True
        thread.start()

    def preview_data(self):
        if self.data_processor.df is None:
            messagebox.showwarning("警告", "请先选择Excel文件！")
            return

        preview_df = self.data_processor.get_preview()
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, preview_df.to_string(index=False, justify='center'))

    def basic_statistics(self):
        if self.data_processor.df is None:
            messagebox.showwarning("警告", "请先选择Excel文件！")
            return

        stats = self.data_processor.get_basic_statistics()
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, str(stats))

    def filter_data(self):
        if self.data_processor.df is None:
            messagebox.showwarning("警告", "请先选择Excel文件！")
            return

        FilterWindow(self.root, self.data_processor, self.result_text)

    def handle_missing_values(self):
        if self.data_processor.df is None:
            messagebox.showwarning("警告", "请先选择Excel文件！")
            return

        MissingValuesWindow(self.root, self.data_processor, self.result_text)

    def show_general_statistics_menu(self):
        if self.data_processor.df is None:
            messagebox.showwarning("警告", "请先选择Excel文件！")
            return

        StatisticsWindow(self.root, self.data_processor, self.result_text)