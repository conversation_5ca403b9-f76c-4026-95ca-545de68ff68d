#%%
import numpy as np
import pandas as pd
from scipy import stats
from matplotlib import pyplot as plt 


def mt_mk_inspection(v, a):
    """
    曼-肯德尔（Mann-Kebdall）检验
    依赖库：numpy、scipy
    UFk, UBkT, sig = MT_mktb(v, a)

    输入
    v			检验序列，一维，v(n)，n为序列长度
    a			显著性检验水平，数值，可取0.01，0.05，0.1

    输出
    UFk			UFk序列，一维，UFk(n)，n为序列长度
    UBk			UBk序列，一维，UBk(n)，n为序列长度
    sig			显著性水平a对应的上下置信区间，两个元素的数组，sig(2)
    """
    v = np.array(v)
    n = v.shape[0]
    Sk = [0]
    UFk = [0]
    s = 0
    Exp_value = [0]
    Var_value = [0]
    for i in range(1, n):
        for j in range(i):
            if v[i] > v[j]:
                s = s + 1
            else:
                s = s + 0
        Sk.append(s)
        Exp_value.append((i + 1) * (i + 2) / 4)
        Var_value.append((i + 1) * i * (2 * (i + 1) + 5) / 72)
        UFk.append((Sk[i] - Exp_value[i]) / np.sqrt(Var_value[i]))
    Sk2 = [0]
    UBk = [0]
    UBk2 = [0]
    s2 = 0
    Exp_value2 = [0]
    Var_value2 = [0]
    vT = list(reversed(v))
    for i in range(1, n):
        for j in range(i):
            if vT[i] > vT[j]:
                s2 = s2 + 1
            else:
                s2 = s2 + 0
        Sk2.append(s2)
        Exp_value2.append((i + 1) * (i + 2) / 4)
        Var_value2.append((i + 1) * i * (2 * (i + 1) + 5) / 72)
        UBk.append((Sk2[i] - Exp_value2[i]) / np.sqrt(Var_value2[i]))
        UBk2.append(-UBk[i])
    UBkT = list(reversed(UBk2))

    sig = stats.norm.interval(1 - a, loc=0, scale=1)  # sig为上下置信区间值

    return UFk, UBkT, sig


#%%主函数1
if __name__ == "__main__":

    v = [15.4,14.6 ,15.8 ,14.8,15.0 ,15.1,15.1,15.0, 15.2,15.4,
     14.8, 15.0,15.1, 14.7, 16.0,15.7, 15.4, 14.5, 15.1,15.3,
     15.5, 15.1, 15.6 ,15.1, 15.1, 14.9, 15.5, 15.3, 15.3 ,15.4,
     15.7, 15.2, 15.5, 15.5, 15.6, 16.1, 15.1, 16.0, 15.0, 15.8,
     16.2, 16.2, 16.0, 15.6, 15.9, 15.2, 16.7, 15.8, 16.2, 15.9,
     15.8, 15.5, 15.9, 16.8 ,15.5, 15.8,15.0,14.9, 15.3,16.0,
     16.1, 16.5,15.5,15.6,16.1, 15.6,16.0,15.4, 15.5,15.2,
     15.4, 15.5, 15.1, 15.8, 15.3, 16.0,15.2,15.8, 16.2, 16.2,
     15.2,15.7, 16.0 ,16.0, 15.7, 15.9,15.7, 16.7, 15.3,16.1,
    16.2]

    a = 0.05
    UFk, UBkT, sig = mt_mk_inspection(v, a)


    n = len(v)
    
    plt.figure(figsize=(12, 6))
    
    # 绘制UF和UB曲线
    plt.plot(range(n), UFk, 'r-', label='UF')
    plt.plot(range(n), UBkT, 'b--', label='UB')
    z_critical = sig[1]
    
    # 绘制显著性水平临界线
    plt.axhline(z_critical, color='gray', linestyle='--', label=f'{100*(1-a)}% Sig Level')
    plt.axhline(-z_critical, color='gray', linestyle='--')
    plt.axhline(0, color='black', lw=0.5)
    
    # 标记交点
    for i in range(n):
        if (UFk[i] > UBkT[i] and UFk[i-1] < UBkT[i-1]) or (UFk[i] < UBkT[i] and UFk[i-1] > UBkT[i-1]):
            plt.scatter(i, UFk[i], color='purple', s=50, zorder=5)
    
    plt.xlabel('Time Index')
    plt.ylabel('Statistical Value')
    plt.legend()
    plt.grid(alpha=0.3)
    plt.tight_layout()
    plt.show()

#%%主函数2
if __name__ == "__main__":
    a = 0.05
    filepath = r'D:\python\四川\Reference_station_analysis\data\龙泉驿56286.xlsx'
    data1 = pd.read_excel(filepath)
    data1.sort_values(by='年', inplace=True)
    data = data1['平均气压'].values


    UFk, UBkT, sig = mt_mk_inspection(data, a)




    n = len(data)
    
    plt.figure(figsize=(12, 6))
    
    # 绘制UF和UB曲线
    plt.plot(range(n), UFk, 'r-', label='UF')
    plt.plot(range(n), UBkT, 'b--', label='UB')
    z_critical = sig[1]
    
    # 绘制显著性水平临界线
    plt.axhline(z_critical, color='gray', linestyle='--', label=f'{100*(1-a)}% Sig Level')
    plt.axhline(-z_critical, color='gray', linestyle='--')
    plt.axhline(0, color='black', lw=0.5)
    
    # 标记交点
    for i in range(n):
        if (UFk[i] > UBkT[i] and UFk[i-1] < UBkT[i-1]) or (UFk[i] < UBkT[i] and UFk[i-1] > UBkT[i-1]):
            plt.scatter(i, UFk[i], color='purple', s=50, zorder=5)
    
    plt.xlabel('Time Index')
    plt.ylabel('Statistical Value')
    plt.legend()
    plt.grid(alpha=0.3)
    plt.tight_layout()
    plt.show()
# %%
