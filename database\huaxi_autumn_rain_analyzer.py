"""
华西秋雨分析模块
基于多站点降水数据的华西秋雨现象分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from weather_database_fixed import WeatherDatabase
import logging

class HuaxiAutumnRainAnalyzer:
    """华西秋雨分析器"""
    
    def __init__(self):
        self.db = WeatherDatabase()
        
        # 华西地区站点ID列表
        self.station_ids = [
            56181, 56183, 56186, 56187, 56188, 56189, 56190, 56193, 57415, 57416, 57417,
            57420, 57503, 57507, 57600, 57603, 56194, 56195, 56196, 56197, 56198, 56199,
            56272, 56273, 56276, 56278, 56279, 56280, 56281, 56284, 56285, 56287, 56288,
            56289, 56290, 57604, 57605, 57608, 56295, 56296, 56297, 56298, 56371, 56373,
            56374, 56376, 56378, 56380, 56381, 56382, 56383, 56384, 56386, 56387, 56291,
            57217, 57237, 57303, 57304, 57306, 57307, 57308, 57309, 57313, 57314, 57315,
            57317, 57318, 57320, 57324, 57326, 57328, 56389, 56390, 56391, 56393, 56394,
            56395, 56396, 56399, 56474, 56475, 56480, 56485, 56487, 56490, 56492, 56493,
            56494, 56496, 57329, 57401, 57402, 57405, 57407, 57408, 57411, 57413, 57414,
            56498, 56499, 56592, 56593, 57204, 57206, 57208, 57216
        ]
        
        self.total_stations = 107  # 华西地区总站点数
        
    def get_precipitation_data(self, year, start_month=8, end_month=11):
        """
        从数据库获取指定年份的降水数据
        
        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份
            
        Returns:
            DataFrame: 降水数据
        """
        try:
            # 构建查询条件
            start_date = f"{year}-{start_month:02d}-01"
            end_date = f"{year}-{end_month:02d}-30"
            
            # 从数据库获取数据
            data_list = []
            
            for station_id in self.station_ids:
                # 检查站点是否存在
                station = self.db.get_station_by_id(str(station_id))
                if not station:
                    continue
                    
                # 获取观测数据
                observations = self.db.get_observations_by_station_id_and_date_range(
                    str(station_id), start_date, end_date
                )
                
                for obs in observations:
                    if obs.get('precipitation') is not None:
                        data_list.append({
                            '区站号(字符)': station_id,
                            '资料时间': obs['timestamp'],
                            '20-20时降水量': obs['precipitation']
                        })
            
            if not data_list:
                raise ValueError("未找到符合条件的降水数据")
                
            # 转换为DataFrame
            data_rain = pd.DataFrame(data_list)
            data_rain['资料时间'] = pd.to_datetime(data_rain['资料时间'])
            data_rain.sort_values(by='资料时间', inplace=True)
            
            # 处理异常值
            data_rain['20-20时降水量'] = data_rain['20-20时降水量'].replace([999990, 999999, 999998], np.nan)
            
            return data_rain
            
        except Exception as e:
            logging.error(f"获取降水数据失败: {e}")
            raise
    
    def is_autumn_rain_day(self, data):
        """
        判断是否为秋雨日
        
        Args:
            data: 某一天的所有站点数据
            
        Returns:
            bool: 是否为秋雨日
        """
        valid_stations = (data['20-20时降水量'] >= 0.1).sum()
        ratio = valid_stations / self.total_stations
        return ratio >= 0.5
    
    def detect_autumn_rain_period(self, data_rain, year):
        """
        检测华西秋雨期间
        
        Args:
            data_rain: 降水数据DataFrame
            year: 年份
            
        Returns:
            dict: 包含开始日期、结束日期、持续天数等信息
        """
        try:
            # 初始化秋雨日标记列
            data_rain['是否为秋雨日'] = False
            
            # 标记秋雨日
            for date in data_rain['资料时间'].unique():
                mask = data_rain['资料时间'] == date
                data_rain.loc[mask, '是否为秋雨日'] = self.is_autumn_rain_day(data_rain[mask])
            
            # 设置分析起始日期
            start_date = f'{year}-08-21'
            
            # 获取分析期秋雨日标记
            autumn_rain_days = data_rain[data_rain['资料时间'] >= pd.to_datetime(start_date)]
            autumn_rain_series = autumn_rain_days.groupby('资料时间')['是否为秋雨日'].first()
            
            # 秋雨开始日期检测
            start_date_detected = self._detect_start_date(autumn_rain_series)
            
            # 秋雨结束日期检测
            end_date_detected = None
            duration_days = 0
            
            if start_date_detected:
                end_date_detected = self._detect_end_date(autumn_rain_series, start_date_detected, year)
                if end_date_detected:
                    duration_days = (end_date_detected - start_date_detected).days
            
            return {
                'start_date': start_date_detected,
                'end_date': end_date_detected,
                'duration_days': duration_days,
                'autumn_rain_series': autumn_rain_series
            }
            
        except Exception as e:
            logging.error(f"检测秋雨期间失败: {e}")
            raise
    
    def _detect_start_date(self, autumn_rain_series):
        """检测秋雨开始日期"""
        window_size = 5
        
        for i in range(len(autumn_rain_series) - window_size + 1):
            current_window = autumn_rain_series.iloc[i:i+window_size]
            
            # 条件1：连续5个秋雨日
            if all(current_window):
                return current_window.index[0]
            
            # 条件2：首尾为秋雨日，5天中最多1个非秋雨日（至少4个秋雨日）
            if (current_window.iloc[0] and current_window.iloc[-1] and 
                current_window.sum() >= 4 and 
                current_window.iloc[1:-1].sum() >= 2):
                return current_window.index[0]
        
        return None
    
    def _detect_end_date(self, autumn_rain_series, start_date_detected, year):
        """检测秋雨结束日期"""
        window_size = 5
        
        # 设置结束日期上限
        end_date_limit = pd.to_datetime(f'{year}-11-30')
        end_period = autumn_rain_series[start_date_detected:end_date_limit]
        
        # 逆序滑动窗口检测
        for i in range(len(end_period) - window_size, -1, -1):
            current_window = end_period.iloc[i:i+window_size]
            
            # 条件1: 连续5个秋雨日
            condition1 = all(current_window)
            
            # 条件2: 5天中第2-4天出现1个非秋雨日
            condition2 = (current_window.iloc[0] and current_window.iloc[-1] and 
                          current_window.sum() == 4 and 
                          current_window.iloc[1:-1].sum() >= 2)
            
            if condition1 or condition2:
                # 结束日期为窗口最后一天的下一天
                return current_window.index[-1] + pd.DateOffset(days=1)
        
        return None
    
    def calculate_rainfall_index(self, data_rain, start_date, end_date):
        """
        计算华西秋雨量指数
        
        Args:
            data_rain: 降水数据
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            float: 秋雨量指数R
        """
        try:
            # 筛选秋雨期数据
            rain_period = data_rain[
                (data_rain['资料时间'] >= start_date) &
                (data_rain['资料时间'] <= end_date)
            ]
            
            # 计算每日平均降水量
            station_rainfall = rain_period.groupby('资料时间')['20-20时降水量'].mean()

            # 处理NaN值，将其替换为0
            station_rainfall = station_rainfall.fillna(0)

            # 计算区域平均降水量累积值（秋雨量指数R）
            R = station_rainfall.sum()

            # 确保返回值不是NaN
            if pd.isna(R) or R != R:
                R = 0.0

            return float(R)
            
        except Exception as e:
            logging.error(f"计算秋雨量指数失败: {e}")
            return 0.0
    
    def get_intensity_level(self, rainfall_index):
        """
        根据秋雨量指数判定强度等级
        
        Args:
            rainfall_index: 秋雨量指数
            
        Returns:
            dict: 强度等级信息
        """
        if rainfall_index >= 300:
            return {
                'level': 'extreme',
                'name': '极强',
                'description': '极强华西秋雨',
                'color': 'danger'
            }
        elif rainfall_index >= 200:
            return {
                'level': 'heavy',
                'name': '较强',
                'description': '较强华西秋雨',
                'color': 'warning'
            }
        elif rainfall_index >= 100:
            return {
                'level': 'moderate',
                'name': '中等',
                'description': '中等华西秋雨',
                'color': 'info'
            }
        else:
            return {
                'level': 'light',
                'name': '轻度',
                'description': '轻度华西秋雨',
                'color': 'success'
            }
    
    def analyze_year(self, year):
        """
        分析指定年份的华西秋雨
        
        Args:
            year: 年份
            
        Returns:
            dict: 分析结果
        """
        try:
            # 获取降水数据
            data_rain = self.get_precipitation_data(year)
            
            # 检测秋雨期间
            period_info = self.detect_autumn_rain_period(data_rain, year)
            
            result = {
                'year': year,
                'success': False,
                'message': '',
                'start_date': None,
                'end_date': None,
                'duration_days': 0,
                'rainfall_index': 0.0,
                'intensity': None,
                'daily_rainfall': []
            }
            
            if period_info['start_date'] and period_info['end_date']:
                # 计算秋雨量指数
                rainfall_index = self.calculate_rainfall_index(
                    data_rain, 
                    period_info['start_date'], 
                    period_info['end_date']
                )
                
                # 获取强度等级
                intensity = self.get_intensity_level(rainfall_index)
                
                # 计算每日降水量序列
                rain_period = data_rain[
                    (data_rain['资料时间'] >= period_info['start_date']) &
                    (data_rain['资料时间'] <= period_info['end_date'])
                ]
                daily_rainfall = rain_period.groupby('资料时间')['20-20时降水量'].mean().to_dict()

                # 处理NaN值，将其替换为0
                daily_rainfall_clean = {}
                for k, v in daily_rainfall.items():
                    if pd.isna(v) or v != v:  # 检查NaN值
                        daily_rainfall_clean[k.strftime('%Y-%m-%d')] = 0.0
                    else:
                        daily_rainfall_clean[k.strftime('%Y-%m-%d')] = round(float(v), 1)

                result.update({
                    'success': True,
                    'message': f'成功检测到{year}年华西秋雨',
                    'start_date': period_info['start_date'].strftime('%Y-%m-%d'),
                    'end_date': period_info['end_date'].strftime('%Y-%m-%d'),
                    'duration_days': period_info['duration_days'],
                    'rainfall_index': round(rainfall_index, 2),
                    'intensity': intensity,
                    'daily_rainfall': daily_rainfall_clean
                })
            else:
                result['message'] = f'{year}年未检测到符合条件的华西秋雨'
            
            return result
            
        except Exception as e:
            logging.error(f"分析{year}年华西秋雨失败: {e}")
            return {
                'year': year,
                'success': False,
                'message': f'分析失败: {str(e)}',
                'start_date': None,
                'end_date': None,
                'duration_days': 0,
                'rainfall_index': 0.0,
                'intensity': None,
                'daily_rainfall': []
            }
    
    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()
