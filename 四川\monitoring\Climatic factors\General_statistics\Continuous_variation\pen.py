# In[0]:

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy.spatial import cKDTree
import geopandas as gpd


# 添加中文字体配置
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文显示字体为黑体（SimHei）
plt.rcParams['axes.unicode_minus'] = False     # 正确显示负号


# 读取Excel文件
file_path = r'D:\python\monitoring\data\station\SURF_CHN_MUL_PEN.xlsx'

# 读取站点信息
station_info = pd.read_excel(r'D:\python\monitoring\data\station\station_info.xlsx')
station_ids = station_info['station_id'].astype(str)

# 读取数据并筛选特定站号的数据
def read_data(file_path):

    data = pd.read_excel(file_path)
    # 获取年月日列
    year = data['年']
    month = data['月']
    pen = data['侯']

    # 将年月旬组合成datetime格式
    data['datetime'] = pd.to_datetime(year.astype(str) + '-' + month.astype(str) + '-' + pen.astype(str))
    # 按照日期排序
    data = data.sort_values(by='datetime')
    # 添加新的日期列并设置为索引
    data['date'] = data['datetime'].dt.date
    data.set_index('date', inplace=True)

    return data

data = read_data(file_path)

# IDW插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values)-1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)

# In[1]:
# 降水

precipitation = []
for station_id in station_ids:
    # 使用原始数据的副本进行筛选
    station_data = data[data['区站号(字符)'].astype(str) == station_id].copy()
    station_data.replace([999990.0], np.nan, inplace=True)
    precipitation_value = station_data['20-20时降水量'].sum()
    precipitation.append(precipitation_value)
    #print(f"Station ID: {station_id}, Precipitation: {precipitation_value:.1f}")
precipitation = np.array(precipitation)

# 绘图
# 读取省界shp文件
shp_path = r'D:\python\monitoring\data\shp\省界_region.shp'
province = gpd.read_file(shp_path)

# 获取站点经纬度信息
lon = station_info['lon'].values
lat = station_info['lat'].values

# 创建网格点
x = np.linspace(96, 109, 100)
y = np.linspace(25.5, 35, 100)
xx, yy = np.meshgrid(x, y)

# 插值
zz = safe_idw_interpolation(np.column_stack((lon, lat)), precipitation, xx, yy, power=2, k=10)
print(zz.max())

# 绘图
plt.figure(figsize=(12, 8))

# 绘制插值结果
contour = plt.contourf(xx, yy, zz, levels=15,cmap = 'Blues')
# 绘制散点图
plt.scatter(lon, lat, c=precipitation, cmap='Blues',s=40, edgecolor='black', linewidth=0.5)

# 绘制省界
province.boundary.plot(ax=plt.gca(), color='black', linewidth=1)

# 添加颜色条
plt.colorbar(contour, label='降水(mm)')

# 设置标题和坐标轴标签
plt.title('四川省降水量空间分布')
plt.xlabel('经度')
plt.ylabel('纬度')

# 保存图片
plt.savefig(r'D:\python\monitoring\General_statistics\Continuous_variation\photo\pen\precipation_distribution.png', dpi=300, bbox_inches='tight')
plt.show()
