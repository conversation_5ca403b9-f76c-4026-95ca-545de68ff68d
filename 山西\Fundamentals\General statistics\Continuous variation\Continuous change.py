import pandas as pd
import numpy as np

def load_data(file_path):
    """加载并预处理数据"""
    df = pd.read_excel(file_path)
    df = df.sort_values(by='资料时间', ascending=True).reset_index(drop=True)
    df['资料时间'] = pd.to_datetime(df['资料时间'])
    
    # 过滤2024年数据并处理异常值
    df_2024 = df[df['资料时间'].dt.year == 2024].replace({
        '20-20时降水量': {999999: np.nan, 999990: np.nan, 999998: np.nan},
        '平均气温': {999999: np.nan, 999990: np.nan, 999998: np.nan}
    })

    stations = df_2024['区站号(字符)'].unique()
    return df_2024,stations

def load_data1(file_path):
    """加载并预处理数据"""
    df = pd.read_excel(file_path)
    df = df.sort_values(by='资料时间', ascending=True).reset_index(drop=True)
    df['资料时间'] = pd.to_datetime(df['资料时间'])
    df = df.replace({999999: np.nan, 999990: np.nan, 999998: np.nan})
    return df

def process_metric(df, metric_name, aggregation_func, output_path, round_digits=None):

    result = []
    
    for station in stations:
        station_data = df[df['区站号(字符)'] == station][metric_name]
        aggregated_value = aggregation_func(station_data)
        
        if round_digits is not None:
            aggregated_value = round(aggregated_value, round_digits)
        
        if metric_name == '平均气温':
            Perennial_value = round(temp_climent[temp_climent['区站号(字符)'] == station][metric_name].mean(),round_digits)
            result.append({
                'station': station,
                metric_name: aggregated_value,
                '时段常年值': Perennial_value,
                '时段距平': aggregated_value - Perennial_value,
            })

        if metric_name == '20-20时降水量':
            Perennial_value = rainfall_climent[rainfall_climent['区站号(字符)'] == station][metric_name].sum()
            Perennial_value = round(Perennial_value/30,round_digits)
            result.append({
            'station': station,
            metric_name: aggregated_value,
            '时段常年值': Perennial_value,
            '时段距平百分率': (aggregated_value - Perennial_value) / Perennial_value * 100 if Perennial_value != 0 else None,
            })

    result_df = pd.DataFrame(result).sort_values(by='station', ascending=True)
    result_df.to_excel(output_path, index=False)

if __name__ == "__main__":
    input_file = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\SURF_CHN_MUL_DAY(2024).xlsx'
    temp_climent_input = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\平均气温1991-2020.xlsx'
    rainfall_climent_input = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\降水1991-2020.xlsx'
    rainfall_output = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\20-20降水量.xlsx'
    temp_output = r'D:\python\山西\Fundamentals\General statistics\Continuous variation\data\平均气温.xlsx'


    # 加载数据
    df_2024,stations = load_data(input_file)
    temp_climent = load_data1(temp_climent_input)
    rainfall_climent = load_data1(rainfall_climent_input)

    # 处理降水量
    process_metric(
        df=df_2024,
        metric_name='20-20时降水量',
        aggregation_func=pd.Series.sum,
        output_path=rainfall_output,
        round_digits=1
    )

    # 处理平均气温
    process_metric(
        df=df_2024,
        metric_name='平均气温',
        aggregation_func=pd.Series.mean,
        output_path=temp_output,
        round_digits=1
    )


