from matplotlib import pyplot as plt
import numpy as np
import pandas as pd
from scipy.spatial import cKDTree
import geopandas as gpd

plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定中文显示字体为黑体（SimHei）
plt.rcParams['axes.unicode_minus'] = False     # 正确显示负号



# 读取excel文件
file_path = r'D:\python\monitoring\data\station\SURF_CHN_MUL_HOR_N.xlsx'
data = pd.read_excel(file_path)

station_ids = data['区站号(字符)'].astype(str).unique()
#print(station_ids)

# 读取站点信息文件
file_path1 = r'D:\python\monitoring\data\station\station_info.xlsx'
station_info = pd.read_excel(file_path1)
station_info = station_info[station_info['station_id'].astype(str).isin(station_ids)]
station_ids2 = station_info['station_id'].astype(str).values

#print(station_info)


# 获取年月日列
year = data['年']
month = data['月']
day = data['日']
hour = data['时']


# 将年月日和小时组合成datetime格式
date_time = pd.to_datetime(year*10000+month*100+day, format='%Y%m%d') + pd.to_timedelta(hour, unit='h')
# 删除原始的年月日和小时列
data.drop(['年', '月', '日', '时'], axis=1, inplace=True)
# 添加新的日期列并设置为索引
data['date'] = date_time
data.set_index('date', inplace=True)

# 按日期排序
data.sort_index(inplace=True)


# IDW插值函数
def safe_idw_interpolation(points, values, grid_x, grid_y, power=2, k=10):
    tree = cKDTree(points)
    target_points = np.column_stack((grid_x.ravel(), grid_y.ravel()))
    k = min(k, len(points))
    distances, indices = tree.query(target_points, k=k)
    indices = np.clip(indices, 0, len(values)-1)
    distances[distances == 0] = 1e-12
    weights = 1.0 / (distances ** power)
    weights_sum = weights.sum(axis=1)
    weights = weights / weights_sum[:, np.newaxis]
    return np.sum(values[indices] * weights, axis=1).reshape(grid_x.shape)

# 读取省界shp文件
shp_path = r'D:\python\monitoring\data\shp\省界_region.shp'
province = gpd.read_file(shp_path)


# --------------小时尺度---------------
def hourly_vars(station_ids, data, var_name, date_range, cmap, method='sum',file_path=None):
    # 初始化指定变量数据列表
    var_data_list = []

    for station_id in station_ids:
        # 使用原始数据的副本进行筛选
        station_data = data[data['区站号(字符)'].astype(str) == station_id].copy()
        station_data.replace([999990.0], np.nan, inplace=True)

        # 提取指定变量的数据
        var_data = station_data[var_name]

        # 提取指定时间的数据
        var_data = var_data[date_range]

        # 计算统计时段变量
        if method == 'sum':
            cumulative_var = var_data.sum()
        elif method == 'mean':
            cumulative_var = var_data.mean()

        # 获取站点经纬度信息
        station_row = station_info[station_info['station_id'].astype(str) == station_id]
        lon = station_row['lon'].values[0]
        lat = station_row['lat'].values[0]

        # 将统计时段累计降水量存到数组中，包含站点 ID、经纬度和累计降水量
        var_data_list.append({
            'station_id': station_id,
            'lon': lon,
            'lat': lat,
            'cumulative_var': cumulative_var
        })

    # 将列表转换为 DataFrame
    cumulative_df = pd.DataFrame(var_data_list)

    # -------------插值--------------------
    lon = cumulative_df['lon'].values
    lat = cumulative_df['lat'].values
    var_data_list_values = cumulative_df['cumulative_var'].values
    x = np.linspace(96, 109, 100)
    y = np.linspace(25.5, 35, 100)
    xx, yy = np.meshgrid(x, y)
    zz = safe_idw_interpolation(np.column_stack((lon, lat)), var_data_list_values, xx, yy, power=2, k=10)

    # ------------------绘图---------------------
    plt.figure(figsize=(12, 8))
    contour = plt.contourf(xx, yy, zz, levels=15, cmap=cmap)
    plt.scatter(lon, lat, c=var_data_list_values, cmap = cmap, s=40, edgecolor='black', linewidth=0.5)
    # 标记站点值，设置位置为每个点的下方
    for i, txt in enumerate(var_data_list_values):
        plt.text(lon[i], lat[i]-0.2, f'{txt:.2f}', fontsize=8, ha='center', va='center', color='black')
    province.boundary.plot(ax=plt.gca(), color='black', linewidth=1)
    plt.colorbar(contour, label=f'{var_name}')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.savefig(rf'{file_path}', dpi=300, bbox_inches='tight')
    plt.show()

    return cumulative_df

# 调用函数
date_range = pd.date_range(start='2024-05-01 16:00:00', end='2024-05-29 16:00:00', freq='h')
#print(date_range.shape)
cumulative_df1 = hourly_vars(station_ids, data, '过去1小时降水量', date_range,cmap='Blues', method='sum',
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\hour\preci_distribution.png')

cumulative_df1.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\hourly_preci_result.xlsx', index=False)
cumulative_df2 = hourly_vars(station_ids, data, '2分钟平均风速', date_range,cmap='viridis', method='mean',
                file_path=r'D:\python\monitoring\General_statistics\Continuous_variation\photo\hour\wind_distribution.png')
#print(cumulative_df)
cumulative_df2.to_excel(r'D:\python\monitoring\General_statistics\Continuous_variation\data\hourly_wind_result.xlsx', index=False)