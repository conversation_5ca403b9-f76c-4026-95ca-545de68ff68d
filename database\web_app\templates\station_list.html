{% extends "base.html" %}

{% block title %}站点列表 - 气象站点数据库管理系统{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面头部 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="fade-in">
                    <h1 class="h1 fw-bold mb-2">
                        <span class="gradient-primary px-3 py-2 text-white rounded-pill me-3 d-inline-flex align-items-center">
                            <i class="bi bi-geo-alt me-2"></i>
                        </span>
                        气象站点列表
                    </h1>
                    <p class="text-muted">浏览和管理全国气象站点信息</p>
                </div>
                <div class="fade-in animate-delay-1">
                    <button class="btn btn-primary hover-lift" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="stations">
                        <i class="bi bi-plus-circle me-2"></i>添加站点
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-soft fade-in animate-delay-2">
                <div class="card-body">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">省份筛选</label>
                            <select class="form-select" id="provinceFilter">
                                <option value="">🌍 所有省份</option>
                                <!-- 省份选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">搜索站点</label>
                            <div class="search-box">
                                <i class="bi bi-search search-icon"></i>
                                <input type="text" class="form-control" id="stationSearch" placeholder="输入站点名称、ID或地区...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button id="resetFilters" class="btn btn-outline-secondary w-100 hover-lift">
                                <i class="bi bi-arrow-clockwise me-2"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据可视化图表 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-soft fade-in animate-delay-3">
                <div class="card-header bg-transparent border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-bar-chart text-primary me-2"></i>
                            省份分布统计
                        </h5>
                        <div class="d-flex gap-2">
                            <span class="badge badge-light">
                                <i class="bi bi-graph-up me-1"></i>实时数据
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div style="height: 350px; position: relative;">
                        <canvas id="provinceDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 站点数据表格 -->
    <div class="card border-0 shadow-soft fade-in animate-delay-4">
        <div class="card-header bg-transparent border-0 py-3">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div>
                    <h5 class="mb-1 fw-bold">
                        <i class="bi bi-table text-primary me-2"></i>
                        站点数据表
                    </h5>
                    <div id="stationStats" class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        <span>加载站点数据...</span>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary hover-lift" id="exportData">
                        <i class="bi bi-download me-1"></i>导出数据
                    </button>
                    <button class="btn btn-sm btn-outline-success hover-lift" id="refreshData">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="stationTable">
                    <thead>
                        <tr>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="id">
                                    <i class="bi bi-hash me-1"></i>站点ID
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="name">
                                    <i class="bi bi-geo-alt me-1"></i>站点名称
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="country">
                                    <i class="bi bi-map me-1"></i>省份
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="latitude">
                                    <i class="bi bi-compass me-1"></i>纬度
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="longitude">
                                    <i class="bi bi-compass me-1"></i>经度
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="elevation">
                                    <i class="bi bi-arrow-up me-1"></i>海拔(米)
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold">
                                <a href="#" class="sort-header text-decoration-none" data-sort="observations">
                                    <i class="bi bi-clipboard-data me-1"></i>观测数量
                                    <i class="bi bi-arrow-down-up ms-1"></i>
                                </a>
                            </th>
                            <th class="fw-bold text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if stations %}
                            {% for station in stations %}
                            <tr class="hover-lift">
                                <td>
                                    <span class="badge badge-light">{{ station.station_id }}</span>
                                </td>
                                <td class="fw-bold">{{ station.name or station.station_id }}</td>
                                <td>
                                    <span class="badge badge-primary">{{ station.country }}</span>
                                </td>
                                <td>{{ "%.4f"|format(station.latitude) if station.latitude else '--' }}</td>
                                <td>{{ "%.4f"|format(station.longitude) if station.longitude else '--' }}</td>
                                <td>{{ station.elevation if station.elevation else '--' }}</td>
                                <td>
                                    <span class="badge badge-success">{{ station.observation_count or 0 }}</span>
                                </td>
                                <td class="text-center">
                                    <a href="/datacenter/station/{{ station.station_id }}" class="btn btn-sm btn-primary hover-lift">
                                        <i class="bi bi-eye me-1"></i>查看详情
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="bi bi-inbox" style="font-size: 3rem; color: var(--text-muted); opacity: 0.5;"></i>
                                        <h6 class="mt-3 text-muted">暂无站点数据</h6>
                                        <p class="text-muted mb-3">请导入站点数据或检查筛选条件</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal" data-file-type="stations">
                                            <i class="bi bi-plus-circle me-2"></i>导入站点数据
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- 分页导航 -->
            <div class="card-footer bg-transparent border-0 py-3">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mb-0" id="pagination">
                        <!-- 分页将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入Chart.js库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/province_filter.js') }}"></script>
<script src="{{ url_for('static', filename='js/province_chart.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 排序功能
        const sortHeaders = document.querySelectorAll('.sort-header');
        sortHeaders.forEach(header => {
            header.addEventListener('click', function(e) {
                e.preventDefault();
                const sortField = this.getAttribute('data-sort');
                const table = document.getElementById('stationTable');
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));
                
                // 获取当前排序方向
                const currentDir = this.classList.contains('sort-asc') ? 'desc' : 'asc';
                
                // 重置所有排序图标
                sortHeaders.forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                    h.querySelector('i').className = 'bi bi-arrow-down-up';
                });
                
                // 设置当前排序图标
                this.classList.add(`sort-${currentDir}`);
                this.querySelector('i').className = currentDir === 'asc' ? 
                    'bi bi-arrow-up' : 'bi bi-arrow-down';
                
                // 排序行
                rows.sort((a, b) => {
                    let aValue = a.cells[Array.from(table.querySelectorAll('th')).findIndex(th => 
                        th.querySelector(`[data-sort="${sortField}"]`))].textContent.trim();
                    let bValue = b.cells[Array.from(table.querySelectorAll('th')).findIndex(th => 
                        th.querySelector(`[data-sort="${sortField}"]`))].textContent.trim();
                    
                    // 检查是否为数字
                    if (!isNaN(aValue) && !isNaN(bValue)) {
                        aValue = parseFloat(aValue);
                        bValue = parseFloat(bValue);
                    }
                    
                    if (currentDir === 'asc') {
                        return aValue > bValue ? 1 : -1;
                    } else {
                        return aValue < bValue ? 1 : -1;
                    }
                });
                
                // 清空表格并重新填充排序后的行
                tbody.innerHTML = '';
                rows.forEach(row => tbody.appendChild(row));
                
                // 更新排序后的统计信息
                updateStationStats();
            });
        });
    });
</script>
{% endblock %}
