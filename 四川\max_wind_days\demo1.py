#%%
import pandas as pd

file_path_day = r'D:\python\四川\max_wind_days\data\SURF_CHN_MUL_DAY.xlsx'
file_path_mon = r'D:\python\四川\max_wind_days\data\SURF_CHN_MUL_MON.xlsx'

data_day = pd.read_excel(file_path_day)
data_mon = pd.read_excel(file_path_mon)

data_day['资料时间'] = pd.to_datetime(data_day['资料时间'])
data_mon['资料时间'] = pd.to_datetime(data_mon['资料时间'])


data_day['最大风速日'] = data_day['最大风速'] >= 17.2

data_56038 = data_day[data_day['区站号(字符)'] == 56038]
days_56038 = data_56038['最大风速日'].sum()
print('使用逐日资料计算的大风日数（≥17.2m/s）气候态：', days_56038/30,'天/年')

days1 = data_mon[data_mon['区站号(字符)'] == 56038]['最大风速≥17m/s日数'].sum()
print('使用月资料计算的大风日数（最大风速≥17m/s日数）气候态：',round(days1/30,1),'天/年')

days2 = data_mon[data_mon['区站号(字符)'] == 56038]['大风日数'].sum()
print('使用月资料计算的大风日数（大风日数）气候态：',round(days2/30,1),'天/年')
# %%
data_56038_mon = data_mon[data_mon['区站号(字符)'] == 56038]
print(data_56038_mon)
# %%
