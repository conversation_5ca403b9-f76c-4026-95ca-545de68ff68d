# In[0]:前处理
import pandas as pd
import numpy as np

day_path = r'D:\python\四川\Heating_and_ventilation\data\绵阳56196.xlsx'
day_data = pd.read_excel(day_path)
day_data.sort_values(by='资料时间', inplace=True)
day_data['资料时间'] = pd.to_datetime(day_data['资料时间'])
day_data['平均气温'] =  pd.to_numeric(day_data['平均气温'], errors='coerce').replace(999999, 0)

# In[1]:新方法

data_678 = day_data[(day_data['资料时间'].dt.month >= 6) & (day_data['资料时间'].dt.month <= 8)].copy()
# 按年份分组处理并剔除最高5天
data_678['年份'] = data_678['资料时间'].dt.year
filtered_data = data_678.groupby('年份', group_keys=False, as_index=False).apply(
    lambda g: g.sort_values('平均气温', ascending=False).iloc[5:],include_groups=False
)

# 计算剩余日期的平均值
average_temp = filtered_data['平均气温'].mean()
print(f"夏季空调室外计算日平均温度(新方法): {average_temp:.2f}℃")


# In[2]:旧方法
data_new = day_data.copy()
data_new['年份'] = data_new['资料时间'].dt.year
years = data_new['年份'].nunique()

sorted_data = data_new['平均气温'].sort_values(ascending=False)
average_temp = sorted_data.iloc[years*5]
print(f"夏季空调室外计算日平均温度(旧方法): {average_temp:.2f}℃")

# %%
