/* 导入现代字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS变量定义 */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #f1f5f9;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 全局样式重置与优化 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Microsoft YaHei', 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 现代化导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color) !important;
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--primary-dark) !important;
    transform: translateY(-1px);
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-secondary) !important;
    transition: var(--transition-fast);
    position: relative;
    padding: 8px 16px !important;
    border-radius: 8px;
    margin: 0 4px;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(0, 123, 255, 0.1);
}

/* 导航链接激活状态 */
.navbar-nav .nav-link.active {
    color: white !important;
    font-weight: 600;
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.navbar-nav .nav-link.active:hover {
    color: white !important;
    background-color: var(--primary-color);
}

/* 下拉菜单按钮特殊处理 */
.navbar-nav .nav-item.dropdown .nav-link {
    display: flex;
    align-items: center;
}

.navbar-nav .nav-item.dropdown .nav-link.active {
    color: white !important;
    background-color: var(--primary-color);
}

/* 下拉菜单样式优化 */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 8px 0;
    margin-top: 8px;
}

.dropdown-item {
    padding: 8px 16px;
    transition: var(--transition-fast);
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

/* 现代化卡片设计 */
.card {
    border: none;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    margin-bottom: 1.5rem;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: var(--transition-normal);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    padding: 1.25rem 1.5rem;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 特殊卡片样式 */
.card-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
}

.card-gradient .card-title,
.card-gradient .card-text {
    color: white;
}

/* 悬浮卡片效果 */
.hover-card {
    cursor: pointer;
    transition: all var(--transition-normal);
}

.hover-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

/* 玻璃态效果卡片 */
.card-glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 现代化按钮设计 */
.btn {
    border-radius: var(--border-radius-md);
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.625rem 1.25rem;
    transition: all var(--transition-fast);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    color: white;
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
    font-weight: 600;
}

/* 浮动操作按钮 */
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.btn-fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* 现代化表格设计 */
.table {
    border-collapse: separate;
    border-spacing: 0;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: var(--bg-secondary);
    border-top: none;
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem 1.25rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
}

.table thead th:first-child {
    border-top-left-radius: var(--border-radius-lg);
}

.table thead th:last-child {
    border-top-right-radius: var(--border-radius-lg);
}

.table tbody td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-weight: 400;
    vertical-align: middle;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--bg-secondary);
    transform: scale(1.01);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr:last-child td:first-child {
    border-bottom-left-radius: var(--border-radius-lg);
}

.table tbody tr:last-child td:last-child {
    border-bottom-right-radius: var(--border-radius-lg);
}

/* 表格条纹效果 */
.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(99, 102, 241, 0.02);
}

/* 表格边框效果 */
.table-bordered {
    border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--border-color);
}

/* 现代化观测数据表格 - 固定高度限制 */
.observation-table-container {
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
    position: relative;
    height: 400px;
    display: flex;
    flex-direction: column;
}

.observation-table-wrapper {
    flex: 1;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.observation-table-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.observation-table-wrapper::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.observation-table-wrapper::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    border: 1px solid var(--bg-secondary);
}

.observation-table-wrapper::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

.observation-table-wrapper::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

#observationTable {
    margin-bottom: 0;
    border: none;
    width: 100%;
    table-layout: fixed;
}

#observationTable thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#observationTable thead th {
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem 0.75rem;
    vertical-align: middle;
    white-space: nowrap;
    border-right: 1px solid var(--border-color);
}

#observationTable thead th:last-child {
    border-right: none;
}

#observationTable tbody td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
}

#observationTable tbody td:last-child {
    border-right: none;
}

#observationTable tbody tr:last-child td {
    border-bottom: none;
}

/* 表格列宽度控制 */
.col-checkbox {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

.col-date {
    width: 180px;
    min-width: 180px;
    max-width: 180px;
}

.col-data {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}

.col-actions {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
}

/* 表格布局辅助样式 */
.observation-table-wrapper .table {
    height: 100%;
}

/* 确保表格内容不会溢出 */
.observation-table-wrapper tbody {
    display: table-row-group;
    vertical-align: middle;
}

.observation-table-wrapper thead {
    display: table-header-group;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 排序表头 */
.sort-header {
    text-decoration: none;
    color: #343a40;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sort-header i {
    font-size: 0.8rem;
    margin-left: 5px;
}

.sort-asc, .sort-desc {
    font-weight: 700;
    color: #0d6efd;
}

/* 进度条样式 */
.progress {
    height: 20px;
    border-radius: 10px;
}

.progress-bar {
    background-color: #0d6efd;
}

/* 首页卡片数据统计 */
.card .display-4 {
    color: #0d6efd;
}

.station-count, .observation-count, .latest-date {
    font-size: 2rem;
    font-weight: 700;
    color: #343a40;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0.5rem 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

/* 页脚样式 */
.footer {
    margin-top: 3rem;
    padding: 1.5rem 0;
    color: #6c757d;
    border-top: 1px solid #e9ecef;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .station-count, .observation-count, .latest-date {
        font-size: 1.5rem;
    }
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: none;
    padding: 1.5rem 1.5rem 0.5rem;
}

.modal-footer {
    border-top: none;
    padding: 0.5rem 1.5rem 1.5rem;
}

/* 图表容器 */
.chart-container {
    position: relative;
    margin: auto;
    height: 250px;
}

/* 自定义阴影效果 */
.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important;
}

/* 高级动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* 动画类 */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* 加载动画 */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 交互动画 */
.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* 延迟动画 */
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

/* 圆形图标 */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    margin-bottom: 10px;
}

/* 提示信息 */
.alert {
    border: none;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 现代化表单控件 */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: var(--bg-primary);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* 现代化输入组 */
.input-group {
    position: relative;
}

.input-group-text {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    font-weight: 500;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    z-index: 5;
}

/* 现代化徽章 */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-md);
}

.badge-primary {
    background: var(--primary-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-light {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* 现代化进度条 */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius-md);
    background: var(--bg-secondary);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-md);
    transition: width var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* 现代化警告框 */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
    box-shadow: var(--shadow-sm);
}

.alert-primary {
    background: rgba(99, 102, 241, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-dark);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: var(--success-color);
    color: #065f46;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: var(--warning-color);
    color: #92400e;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: var(--danger-color);
    color: #991b1b;
}

/* 表格响应式优化 */
.table-responsive {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

/* 现代化模态框 */
.modal-content {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 1.5rem 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.modal-title {
    font-weight: 600;
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem 1.5rem;
    background: var(--bg-secondary);
    border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

/* 现代化图标样式 */
.icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.icon-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition-normal);
}

.icon-wrapper:hover::before {
    opacity: 1;
}

/* 渐变背景 */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.gradient-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.gradient-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

/* 玻璃态效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 阴影变体 */
.shadow-soft {
    box-shadow: 0 2px 12px rgba(99, 102, 241, 0.15);
}

.shadow-colored {
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

/* 现代化分页 */
.pagination .page-link {
    border: none;
    color: var(--text-secondary);
    background: var(--bg-primary);
    margin: 0 0.125rem;
    border-radius: var(--border-radius-md);
    padding: 0.5rem 0.75rem;
    transition: var(--transition-fast);
}

.pagination .page-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
}

/* 现代化面包屑 */
.breadcrumb {
    background: transparent;
    padding: 0.75rem 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item {
    font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--text-muted);
    font-weight: 600;
}

.breadcrumb-item.active {
    color: var(--text-primary);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

/* 现代化页脚 */
.footer {
    margin-top: 4rem;
    padding: 2rem 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background: var(--text-primary);
    border-radius: var(--border-radius-md);
    padding: 0.5rem 0.75rem;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: inherit;
}

.spinner-modern {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 992px) {
    .card {
        margin-bottom: 1rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .hero-section h1 {
        font-size: 2.5rem !important;
    }

    .hero-section p {
        font-size: 1.1rem !important;
    }
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .hero-section {
        min-height: 400px !important;
    }

    .hero-section h1 {
        font-size: 2rem !important;
    }

    .hero-section p {
        font-size: 1rem !important;
    }

    .icon-wrapper {
        width: 60px !important;
        height: 60px !important;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 0.75rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    .btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }

    .hero-section {
        min-height: 350px !important;
    }

    .hero-section h1 {
        font-size: 1.75rem !important;
    }

    .hero-section .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.75rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
        --bg-primary: #1e293b;
        --bg-secondary: #334155;
        --bg-tertiary: #475569;
        --border-color: #475569;
    }

    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    }

    .navbar {
        background: rgba(30, 41, 59, 0.95) !important;
    }

    .card {
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
    }

    .table {
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .table thead th {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }

    .table tbody tr:hover {
        background: var(--bg-secondary);
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .modal,
    .footer {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .card {
        border: 2px solid var(--border-color);
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* 选择文本样式 */
::selection {
    background: rgba(99, 102, 241, 0.2);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(99, 102, 241, 0.2);
    color: var(--text-primary);
}

/* 上传模态框样式 */
.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    background: var(--bg-secondary);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-zone:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.upload-zone.drag-over {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.02);
}

.upload-zone-content {
    transition: var(--transition-normal);
}

.upload-zone:hover .upload-zone-content {
    transform: translateY(-2px);
}

.upload-icon {
    transition: var(--transition-normal);
}

.upload-zone:hover .upload-icon {
    transform: scale(1.1);
    color: var(--primary-color) !important;
}

/* 文件信息卡片 */
.file-info .card {
    transition: var(--transition-normal);
}

.file-info .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 进度条增强 */
.upload-progress .progress {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    position: relative;
}

.upload-progress .progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    position: relative;
    overflow: hidden;
}

.upload-progress .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent);
    background-size: 1rem 1rem;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* 格式信息卡片 */
.format-info .card {
    transition: var(--transition-normal);
    border-left: 3px solid transparent;
}

.format-info .card:hover {
    border-left-color: var(--primary-color);
    transform: translateX(5px);
}

/* 表单控件增强 */
.form-check {
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    cursor: pointer;
}

.form-check:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.form-check-input:checked + .form-check-label {
    color: var(--primary-color);
}

.form-check-input:checked ~ * {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

/* 模态框动画增强 */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* 按钮加载状态 */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 文件类型图标 */
.file-type-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

.file-type-csv {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

.file-type-excel {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

/* 拖拽提示动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.upload-zone.drag-over .upload-icon {
    animation: bounce 1s infinite;
}

/* 成功状态样式 */
.upload-success {
    border-color: var(--success-color) !important;
    background: rgba(16, 185, 129, 0.1) !important;
}

.upload-success .upload-icon {
    color: var(--success-color) !important;
}

/* 错误状态样式 */
.upload-error {
    border-color: var(--danger-color) !important;
    background: rgba(239, 68, 68, 0.1) !important;
}

.upload-error .upload-icon {
    color: var(--danger-color) !important;
}
