<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华西秋雨分析 - 计算中心</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        :root {
            --huaxi-primary: #2980b9;
            --huaxi-secondary: #3498db;
            --huaxi-accent: #e74c3c;
            --huaxi-success: #27ae60;
            --huaxi-warning: #f39c12;
            --rain-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        body {
            background: var(--rain-gradient);
            min-height: 100vh;
        }

        .analysis-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            margin-bottom: 30px;
        }

        .analysis-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 20px;
        }

        .card-header-custom {
            background: var(--rain-gradient);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
            border: none;
        }

        .parameter-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            background: white;
        }

        .parameter-card:hover {
            border-color: var(--huaxi-primary);
            box-shadow: 0 3px 10px rgba(41, 128, 185, 0.1);
        }

        .result-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .intensity-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .intensity-light { background: #d4edda; color: #155724; }
        .intensity-moderate { background: #fff3cd; color: #856404; }
        .intensity-heavy { background: #f8d7da; color: #721c24; }
        .intensity-extreme { background: #d1ecf1; color: #0c5460; }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .breadcrumb-custom {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 8px 20px;
            margin-bottom: 20px;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: white;
        }

        .btn-analyze {
            background: var(--rain-gradient);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
        }

        .btn-analyze:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
            color: white;
        }

        .btn-analyze:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
        }



        .alert-info-custom {
            background: rgba(116, 185, 255, 0.1);
            border: 1px solid rgba(116, 185, 255, 0.3);
            color: #0984e3;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="analysis-header">
        <div class="container">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-custom">
                    <li class="breadcrumb-item"><a href="/">🦕 小恐龙的超算单元</a></li>
                    <li class="breadcrumb-item"><a href="/compute">💻 计算中心</a></li>
                    <li class="breadcrumb-item active">🌧️ 华西秋雨分析</li>
                </ol>
            </nav>
            
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="text-white mb-2">
                        <i class="bi bi-cloud-rain-heavy-fill me-3"></i>华西秋雨分析
                    </h1>
                    <p class="text-white-50 mb-0">基于多站点数据的华西秋雨现象综合分析</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="/compute" class="btn btn-outline-light me-2">
                        <i class="bi bi-arrow-left me-2"></i>返回计算中心
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 左侧参数设置 -->
            <div class="col-lg-3">
                <div class="analysis-card">
                    <div class="card-header-custom">
                        <h5 class="mb-0">
                            <i class="bi bi-gear-fill me-2"></i>分析设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 分析年份选择 -->
                        <div class="parameter-card">
                            <h6 class="fw-bold mb-3">
                                <i class="bi bi-calendar-range text-primary me-2"></i>分析年份
                            </h6>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">开始年份</label>
                                    <select class="form-select" id="startYear">
                                        <!-- 动态加载可用年份 -->
                                    </select>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">结束年份</label>
                                    <select class="form-select" id="endYear">
                                        <!-- 动态加载可用年份 -->
                                    </select>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="alert alert-info-custom">
                                    <small>
                                        <i class="bi bi-info-circle me-1"></i>
                                        分析期间：8月21日 - 11月30日（华西秋雨典型时段）
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- 华西地区说明 -->
                        <div class="parameter-card">
                            <h6 class="fw-bold mb-2">
                                <i class="bi bi-geo-alt-fill text-success me-2"></i>分析区域
                            </h6>
                            <p class="text-muted small mb-0">
                                此华西秋雨只监测<span class="text-primary fw-bold">四川省107个华西秋雨站点</span>
                            </p>
                        </div>

                        <!-- 算法说明 -->
                        <div class="parameter-card">
                            <h6 class="fw-bold mb-3">
                                <i class="bi bi-cpu text-warning me-2"></i>算法说明
                            </h6>
                            <div class="small text-muted">
                                <p class="mb-2">
                                    <strong>秋雨日判定：</strong>≥50%站点日降水量≥0.1mm
                                </p>
                                <p class="mb-2">
                                    <strong>期间检测：</strong>5天滑动窗口，连续或准连续秋雨日
                                </p>
                            </div>
                        </div>

                        <!-- 开始分析按钮 -->
                        <div class="d-grid mt-4">
                            <button class="btn btn-analyze btn-lg" id="startAnalysis">
                                <i class="bi bi-play-fill me-2"></i>开始华西秋雨分析
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示 -->
            <div class="col-lg-9">
                <!-- 分析状态 -->
                <div class="analysis-card">
                    <div class="card-body">
                        <div class="alert alert-info-custom" id="analysisStatus">
                            <i class="bi bi-info-circle me-2"></i>
                            请设置分析参数并点击"开始分析"按钮
                        </div>
                    </div>
                </div>

                <!-- 加载动画 -->
                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">分析中...</span>
                    </div>
                    <p class="mt-3 text-white">正在分析华西秋雨数据，请稍候...</p>
                </div>

                <!-- 分析结果区域 -->
                <div id="analysisResults" style="display: none;">
                    <!-- 秋雨强度等级 -->
                    <div class="result-section">
                        <h5 class="fw-bold mb-3">
                            <i class="bi bi-table text-primary me-2"></i>华西秋雨分析结果
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="intensityTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>年份</th>
                                        <th>累计降水量 (mm)</th>
                                        <th>秋雨期长度</th>
                                        <th>开始日期</th>
                                        <th>结束日期</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="intensityResults">
                                    <!-- 结果将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 时间序列图表 -->
                    <div class="result-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0">
                                <i class="bi bi-graph-up text-success me-2"></i>华西秋雨期间逐日区域累计降水量
                            </h5>
                            <div class="d-flex gap-2">
                                <!-- 年份切换按钮 -->
                                <div class="btn-group" role="group" id="yearToggle">
                                    <!-- 年份按钮将通过JavaScript动态生成 -->
                                </div>
                                <!-- 图表类型切换按钮 -->
                                <div class="btn-group" role="group" id="chartTypeToggle">
                                    <input type="radio" class="btn-check" name="chartType" id="lineChart" value="line" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="lineChart">
                                        <i class="bi bi-graph-up me-1"></i>折线图
                                    </label>
                                    <input type="radio" class="btn-check" name="chartType" id="barChart" value="bar">
                                    <label class="btn btn-outline-primary btn-sm" for="barChart">
                                        <i class="bi bi-bar-chart me-1"></i>柱状图
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="timeSeriesChart"></canvas>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>

        // 华西秋雨分析功能
        document.addEventListener('DOMContentLoaded', function() {
            const startAnalysisBtn = document.getElementById('startAnalysis');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const analysisResults = document.getElementById('analysisResults');
            const analysisStatus = document.getElementById('analysisStatus');

            // 检查元素是否存在
            if (!startAnalysisBtn) {
                console.error('找不到开始分析按钮');
                return;
            }

            console.log('页面加载完成，绑定事件监听器');

            // 加载可用年份
            loadAvailableYears();

            // 加载可用年份
            async function loadAvailableYears() {
                try {
                    const response = await fetch('/api/compute/huaxi-autumn-rain/available-years');
                    const result = await response.json();

                    if (result.success && result.years.length > 0) {
                        const startYearSelect = document.getElementById('startYear');
                        const endYearSelect = document.getElementById('endYear');

                        // 清空现有选项
                        startYearSelect.innerHTML = '';
                        endYearSelect.innerHTML = '';

                        // 添加年份选项
                        result.years.forEach((year, index) => {
                            const startOption = new Option(`${year}年`, year);
                            const endOption = new Option(`${year}年`, year);

                            // 默认选择最近的两年
                            if (index === 1) startOption.selected = true;
                            if (index === 0) endOption.selected = true;

                            startYearSelect.appendChild(startOption);
                            endYearSelect.appendChild(endOption);
                        });

                        console.log('可用年份加载完成:', result.years);
                    } else {
                        console.error('获取可用年份失败:', result.message);
                    }
                } catch (error) {
                    console.error('加载可用年份时出错:', error);
                }
            }

            // 开始分析按钮点击事件
            startAnalysisBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('分析按钮被点击');
                startHuaxiAnalysis();
            });

            // 华西秋雨分析
            async function startHuaxiAnalysis() {
                console.log('开始华西秋雨分析');

                // 获取分析参数
                const analysisParams = getAnalysisParameters();
                console.log('分析参数:', analysisParams);

                // 显示加载状态
                loadingSpinner.style.display = 'block';
                analysisResults.style.display = 'none';
                analysisStatus.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>正在分析华西秋雨数据...';

                // 禁用按钮
                startAnalysisBtn.disabled = true;
                startAnalysisBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>正在分析...';

                try {
                    // 调用后端API
                    const response = await fetch('/api/compute/huaxi-autumn-rain', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(analysisParams)
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 显示分析结果
                        showAnalysisResults(result.results);

                        // 更新状态
                        analysisStatus.innerHTML = '<i class="bi bi-check-circle me-2"></i>分析完成！以下是华西秋雨分析结果。';
                        analysisStatus.className = 'alert alert-success';
                    } else {
                        throw new Error(result.message || '分析失败');
                    }
                } catch (error) {
                    console.error('华西秋雨分析失败:', error);
                    analysisStatus.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>分析失败: ${error.message}`;
                    analysisStatus.className = 'alert alert-danger';
                } finally {
                    // 隐藏加载状态
                    loadingSpinner.style.display = 'none';

                    // 恢复按钮
                    startAnalysisBtn.disabled = false;
                    startAnalysisBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>重新分析';
                }
            }

            // 获取分析参数
            function getAnalysisParameters() {
                const startYear = parseInt(document.getElementById('startYear').value);
                const endYear = parseInt(document.getElementById('endYear').value);

                return {
                    start_year: startYear,
                    end_year: endYear,
                    start_month: 8,  // 固定为8月
                    end_month: 11,   // 固定为11月
                    stations: [],    // 使用分析器内置的华西地区站点
                    options: {
                        intensity: true,
                        trend: true,
                        spatial: false,
                        comparison: true
                    }
                };
            }

            // 显示分析结果
            function showAnalysisResults(results) {
                analysisResults.style.display = 'block';

                // 生成强度等级结果
                generateIntensityResults(results);

                // 生成时间序列图表
                generateTimeSeriesChart(results);
            }

            // 生成华西秋雨分析结果（表格形式）
            function generateIntensityResults(results) {
                const intensityResults = document.getElementById('intensityResults');

                if (!results || results.length === 0) {
                    intensityResults.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无数据</td></tr>';
                    return;
                }

                let html = '';
                results.forEach(result => {
                    if (result.success) {
                        html += `
                            <tr>
                                <td><strong>${result.year}年</strong></td>
                                <td>${result.rainfall_index || 0} mm</td>
                                <td>${result.duration_days || 0} 天</td>
                                <td>${result.start_date || '-'}</td>
                                <td>${result.end_date || '-'}</td>
                                <td><span class="badge bg-success">检测到</span></td>
                            </tr>
                        `;
                    } else {
                        html += `
                            <tr>
                                <td><strong>${result.year}年</strong></td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td><span class="badge bg-secondary">未检测到</span></td>
                            </tr>
                        `;
                    }
                });

                intensityResults.innerHTML = html;
            }

            // 全局变量存储图表实例和数据
            let currentChart = null;
            let allResultsData = null;
            let currentSelectedYear = null;

            // 生成时间序列图表
            function generateTimeSeriesChart(results) {
                console.log('开始生成时间序列图表');

                // 检查Chart.js是否已加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js未加载，无法生成图表');
                    return;
                }

                if (!results || results.length === 0) {
                    const ctx = document.getElementById('timeSeriesChart').getContext('2d');
                    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                    ctx.fillText('暂无数据', ctx.canvas.width / 2, ctx.canvas.height / 2);
                    return;
                }

                // 存储所有结果数据
                allResultsData = results;

                // 生成年份切换按钮
                generateYearToggleButtons(results);

                // 默认显示第一个有数据的年份
                const firstValidResult = results.find(r => r.success && r.daily_rainfall);
                if (firstValidResult) {
                    currentSelectedYear = firstValidResult.year;
                    createChartForYear(firstValidResult.year, 'line');
                }

                // 添加图表类型切换事件监听器
                document.querySelectorAll('input[name="chartType"]').forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.checked && currentSelectedYear) {
                            createChartForYear(currentSelectedYear, this.value);
                        }
                    });
                });
            }

            // 生成年份切换按钮
            function generateYearToggleButtons(results) {
                const yearToggle = document.getElementById('yearToggle');
                yearToggle.innerHTML = '';

                results.forEach((result, index) => {
                    if (result.success && result.daily_rainfall) {
                        const isFirst = index === 0;
                        const buttonId = `year${result.year}`;

                        const input = document.createElement('input');
                        input.type = 'radio';
                        input.className = 'btn-check';
                        input.name = 'selectedYear';
                        input.id = buttonId;
                        input.value = result.year;
                        if (isFirst) input.checked = true;

                        const label = document.createElement('label');
                        label.className = 'btn btn-outline-success btn-sm';
                        label.setAttribute('for', buttonId);
                        label.innerHTML = `${result.year}年`;

                        // 添加点击事件
                        input.addEventListener('change', function() {
                            if (this.checked) {
                                currentSelectedYear = parseInt(this.value);
                                const chartType = document.querySelector('input[name="chartType"]:checked').value;
                                createChartForYear(currentSelectedYear, chartType);
                            }
                        });

                        yearToggle.appendChild(input);
                        yearToggle.appendChild(label);
                    }
                });
            }

            // 为指定年份创建图表
            function createChartForYear(year, chartType) {
                const result = allResultsData.find(r => r.year === year);
                if (!result || !result.success || !result.daily_rainfall) {
                    console.error(`年份 ${year} 没有有效数据`);
                    return;
                }

                // 准备该年份的图表数据
                const dates = Object.keys(result.daily_rainfall).sort();
                const values = dates.map(date => result.daily_rainfall[date] || 0);

                const chartData = {
                    labels: dates.map(date => {
                        const d = new Date(date);
                        return `${d.getMonth() + 1}/${d.getDate()}`;
                    }),
                    datasets: [{
                        label: `${year}年区域累计降水量`,
                        data: values,
                        borderColor: '#74b9ff',
                        backgroundColor: chartType === 'bar' ? '#74b9ff' : 'rgba(116, 185, 255, 0.1)',
                        tension: chartType === 'line' ? 0.3 : 0,
                        fill: chartType === 'bar',
                        pointRadius: chartType === 'line' ? 2 : 0,
                        pointHoverRadius: chartType === 'line' ? 4 : 0,
                        borderWidth: chartType === 'line' ? 2 : 1
                    }]
                };

                createChart(chartData, chartType);
            }

            // 创建图表
            function createChart(chartData, type) {
                // 确保Chart.js已加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js未加载，无法创建图表');
                    return;
                }

                const ctx = document.getElementById('timeSeriesChart').getContext('2d');

                // 销毁现有图表
                if (currentChart) {
                    currentChart.destroy();
                }

                currentChart = new Chart(ctx, {
                    type: type,
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `华西秋雨期间逐日区域累计降水量 (${currentSelectedYear}年)`,
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            legend: {
                                position: 'top'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    title: function(context) {
                                        return `日期: ${context[0].label}`;
                                    },
                                    label: function(context) {
                                        return `${context.dataset.label}: ${context.parsed.y.toFixed(1)} mm`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '降水量 (mm)',
                                    font: {
                                        size: 12,
                                        weight: 'bold'
                                    }
                                },
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '日期 (月/日)',
                                    font: {
                                        size: 12,
                                        weight: 'bold'
                                    }
                                },
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            }
                        },
                        interaction: {
                            mode: 'nearest',
                            axis: 'x',
                            intersect: false
                        }
                    }
                });
            }


        });
    </script>
</body>
</html>
