// 主JavaScript文件
document.addEventListener('DOMContentLoaded', function() {
    // 上传模态框配置
    const uploadModal = document.getElementById('uploadModal');
    if (uploadModal) {
        uploadModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const fileType = button.getAttribute('data-file-type');
            const modalTitle = uploadModal.querySelector('.modal-title');
            const fileTypeInput = document.getElementById('fileType');
            
            // 重置表单和进度条
            document.getElementById('uploadForm').reset();
            document.getElementById('uploadProgress').classList.add('d-none');
            document.getElementById('uploadStatus').textContent = '';
            
            // 设置标题和文件类型
            let titleText = '';
            switch(fileType) {
                case 'combined':
                    titleText = '导入组合数据';
                    break;
                case 'stations':
                    titleText = '导入站点信息';
                    break;
                case 'observations':
                    titleText = '导入观测数据';
                    break;
            }
            
            modalTitle.textContent = titleText;
            fileTypeInput.value = fileType;
        });
    }
    
    // 文件上传处理
    const uploadButton = document.getElementById('uploadButton');
    if (uploadButton) {
        uploadButton.addEventListener('click', function() {
            const fileInput = document.getElementById('fileInput');
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData(document.getElementById('uploadForm'));
            const progressBar = document.querySelector('#uploadProgress .progress-bar');
            const progressDiv = document.getElementById('uploadProgress');
            const statusText = document.getElementById('uploadStatus');
            
            // 显示进度条
            progressDiv.classList.remove('d-none');
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
            statusText.textContent = '正在上传文件...';
            
            // 禁用上传按钮
            uploadButton.disabled = true;
            
            // 发送上传请求
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusText.textContent = data.message;
                    progressBar.style.width = '10%';
                    progressBar.setAttribute('aria-valuenow', 10);
                    
                    // 启动进度轮询
                    if (data.task_id) {
                        pollImportProgress(data.task_id, progressBar, statusText);
                    }
                } else {
                    statusText.textContent = '错误: ' + data.message;
                    uploadButton.disabled = false;
                }
            })
            .catch(error => {
                statusText.textContent = '上传失败: ' + error.message;
                uploadButton.disabled = false;
            });
        });
    }
});

// 轮询导入进度
function pollImportProgress(taskId, progressBar, statusText) {
    const interval = setInterval(function() {
        fetch(`/api/import_progress/${taskId}`)
            .then(response => response.json())
            .then(data => {
                // 更新进度条
                const progress = data.progress || 0;
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                
                if (data.message) {
                    statusText.textContent = data.message;
                }
                
                // 检查是否完成
                if (data.status === 'completed') {
                    clearInterval(interval);
                    statusText.textContent = '导入完成!';
                    
                    // 导入完成，视为成功
                    setTimeout(function() {
                        statusText.textContent = `成功导入数据! ${data.message || ''}`;
                        // 刷新页面或显示其他反馈
                        setTimeout(() => window.location.reload(), 1500);
                    }, 1000);
                } 
                else if (data.status === 'error') {
                    clearInterval(interval);
                    statusText.textContent = `导入失败: ${data.message}`;
                    document.getElementById('uploadButton').disabled = false;
                }
            })
            .catch(error => {
                console.error('轮询进度出错:', error);
            });
    }, 1000); // 每秒轮询一次
}

// 获取近期站点数据
function fetchRecentStations() {
    fetch('/api/stations')
        .then(response => response.json())
        .then(stations => {
            // 获取近期站点
            const recentStationsTable = document.getElementById('recentStations');
            if (recentStationsTable) {
                // 按更新时间排序站点
                const recentStations = stations.slice(0, 5);
                
                // 清空表格内容
                recentStationsTable.innerHTML = '';
                
                // 添加站点行
                if (recentStations.length > 0) {
                    recentStations.forEach((station, index) => {
                        const row = document.createElement('tr');
                        
                        // 添加鼠标悬停效果
                        row.classList.add('station-row');
                        row.style.transition = 'all 0.3s ease';
                        
                        // 交替行颜色
                        if (index % 2 === 0) {
                            row.classList.add('table-light');
                        }
                        
                        row.innerHTML = `
                            <td class="py-3"><span class="badge bg-primary">${station.station_id}</span></td>
                            <td class="py-3"><strong>${station.name || station.station_id}</strong></td>
                            <td class="py-3"><span class="badge bg-light text-dark">${station.country || '未知'}</span></td>
                            <td class="py-3">${station.latitude || '0.00'}</td>
                            <td class="py-3">${station.longitude || '0.00'}</td>
                            <td class="py-3">
                                <span class="badge rounded-pill ${station.observation_count > 0 ? 'bg-success' : 'bg-secondary'}">
                                    ${station.observation_count || 0}
                                </span>
                            </td>
                            <td class="py-3 text-center">
                                <a href="/station/${station.station_id}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-eye"></i> 查看
                                </a>
                            </td>
                        `;
                        recentStationsTable.appendChild(row);
                    });
                    
                    // 添加鼠标悬停效果
                    document.querySelectorAll('.station-row').forEach(row => {
                        row.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = 'rgba(13, 110, 253, 0.05)';
                            this.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
                        });
                        row.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = '';
                            this.style.boxShadow = 'none';
                        });
                    });
                    
                } else {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td colspan="7" class="text-center py-4">
                            <div class="alert alert-info mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                暂无站点数据，请先导入站点信息
                            </div>
                        </td>
                    `;
                    recentStationsTable.appendChild(row);
                }
            }
        })
        .catch(error => console.error('获取站点数据失败:', error));
}

