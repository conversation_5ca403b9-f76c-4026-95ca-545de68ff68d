# In[0]：数据读取
import pandas as pd
import numpy as np

year_data = pd.read_csv(r'D:\python\四川\climate_background\data\龙泉驿56286.csv')
month_data = pd.read_csv(r'D:\python\四川\climate_background\data\龙泉驿月值56286.csv')

#In[1]:年平均风速绘图

import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.sans-serif'] = ['SimHei']  # 设置字体为黑体
mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 提取1991-2020年数据
data1 = year_data.iloc[11:]
year = data1['年'].values
wind_speed = data1['平均2分钟风速']
# 创建画布和坐标轴
fig, ax = plt.subplots(figsize=(12, 6), dpi=300)
# 绘制折线图
ax.plot(year, wind_speed, marker='o', label='平均风速')
# 拟合回归方程
x = np.arange(1,31)
coef = np.polyfit(x, wind_speed, 1)
poly1d_fn = np.poly1d(coef)
# 绘制拟合曲线
ax.plot(year, poly1d_fn(x), '--k')
# 添加拟合方程信息（使用相对坐标定位在右上角）
if coef[1] >= 0:
    fcurve_text = f'拟合方程: y = {coef[0]:.4f}x + {coef[1]:.2f}'
else:
    fcurve_text = f'拟合方程: y = {coef[0]:.4f}x {coef[1]:.2f}'

ax.text(0.55, 0.1, fcurve_text, 
         fontsize=12, transform=ax.transAxes, verticalalignment='top',
         bbox=dict(facecolor='white', alpha=0.8))
ax.set_xlabel('年份')
ax.set_ylabel('平均风速(m/s)')
ax.set_title('成都龙泉驿区平均风速')
ax.grid(False)
ax.legend()
plt.savefig(r'D:\python\四川\climate_background\figure\成都龙泉驿区平均风速.png', dpi=300)
plt.close()


# In[2]: 月平均最高最低气温数据统计
month_data['资料时间'] = pd.to_datetime(month_data['资料时间'])
# 按月份分组
month_data['月'] = month_data['资料时间'].dt.month
monthly_data = month_data.groupby('月')

# 计算每个月的平均气温、平均最高气温平均值和平均最低气温平均值
monthly_avg_temp = monthly_data['平均气温'].mean()
monthly_avg_max_temp = monthly_data['平均最高气温'].mean()
monthly_avg_min_temp = monthly_data['平均最低气温'].mean()

# 保存为表格文件
monthly_data_df = pd.DataFrame({
    '平均气温': round(monthly_avg_temp,1),
    '平均最高气温': round(monthly_avg_max_temp,1),
    '平均最低气温': round(monthly_avg_min_temp,1)
})
monthly_data_df.to_csv(r'D:\python\四川\climate_background\data\成都龙泉驿区月平均气温.csv', index=True)


# In[3]: 月极端最高最低气温
avg_max_temp = monthly_data['最高气温'].max()
avg_min_temp = monthly_data['最低气温'].min()

# 保存为表格文件
monthly_average_data_df = pd.DataFrame({
    '极端最高气温': round(avg_max_temp,1),
    '极端最低气温': round(avg_min_temp,1)
})
monthly_average_data_df.to_csv(r'D:\python\四川\climate_background\data\成都龙泉驿区月极端气温.csv', index=True)


# In[4]: 平均气压绘制柱状图（年变化）

monthly_avg_temp = monthly_data['平均气压'].mean()
# 创建画布和坐标轴
fig, ax = plt.subplots(figsize=(12, 6), dpi=300)
# 绘制柱状图
ax.bar(monthly_avg_temp.index, monthly_avg_temp.values, color='skyblue')
ax.set_xlabel('月份')
ax.set_ylabel('平均气压(hPa)')
ax.set_title('成都龙泉驿区平均气压年变化')
ax.set_xticks(monthly_avg_temp.index)
ax.set_xticklabels(['1月', '2月', '3月', '4月', '5月', '6月', 
                    '7月', '8月', '9月', '10月', '11月', '12月'])
# 限制y轴范围
ax.set_ylim(930, 970)
# 添加图例平均气压
ax.legend(['平均气压'], loc='upper center')
plt.tight_layout()

plt.savefig(r'D:\python\四川\climate_background\figure\成都龙泉驿区平均气压年变化.png', dpi=300)
plt.close()


# In[5]:


