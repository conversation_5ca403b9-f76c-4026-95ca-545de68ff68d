
import pandas as pd
from datetime import datetime

file_path = r'D:\python\山西\data\中国地面标准气候值（1991-2020）降水日值数据(SURF_CLI_CHN_MDAY_PRE).xlsx'  
df = pd.read_excel(file_path)
station_ids = df['区站号(字符)'].unique()

df['日序'] = df['日序'].apply(lambda x: datetime.strptime(f'2000-{x}', '%Y-%j').strftime('%m-%d'))
df = df.rename(columns={'日序': '日期'})


results = []
for station in station_ids:
    station_df = df[df['区站号(字符)'] == station].copy()
    total_precipitation = station_df['累年20-20时日平均降水量'].sum()
    results.append({
        '区站号(字符)': station,
        '累年20-20时日平均降水量': total_precipitation
    })

result_df = pd.DataFrame(results)
result_df.to_excel(r'D:\python\山西\data\降水气候态.xlsx', index=False)

#%%
start_date = df['日期'].min()
end_date = df['日期'].max()
print(f"数据范围：{start_date} 到 {end_date}")
# %%
