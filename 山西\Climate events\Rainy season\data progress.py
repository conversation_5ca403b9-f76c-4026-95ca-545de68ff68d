#%%
import pandas as pd

filepath = r'D:\python\山西\Climate events\Rainy season\data\SURF_CHN_MUL_DAY(2020-2024).xlsx'
df = pd.read_excel(filepath)

df['资料时间'] = pd.to_datetime(df['资料时间'])
df['20-20时降水量'] = df['20-20时降水量'].replace({999999: 0, 999990: 0, 999998: 0})
data = df[
    (df['资料时间'].dt.year.isin([2020, 2021, 2022, 2023, 2024])) &
    (df['资料时间'].dt.month.isin([6, 7, 8]))
]

data = data.drop(columns=['平均气温', '最高气温', '最低气温',
                           '最小相对湿度', '08-08时降水量', '平均2分钟风速', '最大风速', '极大风速'])

data.to_excel(r'D:\python\山西\Climate events\Rainy season\data\降水2020-2024(6-8).xlsx', index=False)
print('降水2020-2024(6-8).xlsx文件已生成')
# %%
